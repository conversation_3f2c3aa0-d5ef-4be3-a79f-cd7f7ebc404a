<?php
session_start();
require 'db.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

if (isset($_GET['id'])) {
    $id = intval($_GET['id']);
    $stmt = $conn->prepare("SELECT * FROM BudgetSupport WHERE ID = ?");
    $stmt->execute([$id]);
    $record = $stmt->fetch();

    if (!$record) {
        echo "<script>alert('السجل غير موجود.'); window.location.href='index.php';</script>";
        exit;
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $amount = $_POST['amount'];
    $description = $_POST['description'];
    $date = $_POST['date'];

    try {
        // جلب المبلغ القديم
        $stmt = $conn->prepare("SELECT Amount FROM BudgetSupport WHERE ID = ?");
        $stmt->execute([$id]);
        $oldAmount = $stmt->fetchColumn();

        // تحديث السجل
        $stmt = $conn->prepare("UPDATE BudgetSupport SET Amount = ?, Date = ?, Description = ? WHERE ID = ?");
        $stmt->execute([$amount, $date, $description, $id]);

        // تعديل إجمالي الموازنة
        $difference = $amount - $oldAmount;
        $stmt = $conn->prepare("UPDATE BudgetTotal SET Total = Total + ? WHERE ID = 1");
        $stmt->execute([$difference]);

        echo "<script>alert('تم التعديل بنجاح!'); window.location.href='/e-finance/index';</script>";
    } catch (PDOException $e) {
        echo "حدث خطأ أثناء التعديل: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل المبلغ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-5">
    <a href="/e-finance/index" class="btn btn-secondary mb-3">العودة إلى الرئيسية</a>
    <h1 class="text-center">تعديل المبلغ</h1>
    <form method="POST" class="mt-4">
        <div class="mb-3">
            <label for="amount" class="form-label">المبلغ</label>
            <input type="number" name="amount" class="form-control" value="<?= htmlspecialchars($record['Amount']) ?>" required>
        </div>
        <div class="mb-3">
            <label for="date" class="form-label">التاريخ</label>
            <input type="date" name="date" class="form-control" value="<?= htmlspecialchars($record['Date']) ?>" required>
        </div>
        <div class="mb-3">
            <label for="description" class="form-label">الشرح</label>
            <textarea name="description" class="form-control" rows="3" required><?= htmlspecialchars($record['Description']) ?></textarea>
        </div>
        <button type="submit" class="btn btn-primary">تعديل</button>
    </form>
</div>
</body>
</html>
