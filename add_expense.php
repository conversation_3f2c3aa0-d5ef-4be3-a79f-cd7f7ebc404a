<?php
require 'db.php';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $customerID = $_POST['customer_id'];
        $budgetID = $_POST['budget_id'];
        $amount = $_POST['amount'];
        $tax = $_POST['tax'] ?? 0;
        $netAmount = $amount - $tax;

        $budgetStmt = $conn->prepare("SELECT TotalAmount, SpentAmount FROM Budgets WHERE ID = ?");
        $budgetStmt->execute([$budgetID]);
        $budget = $budgetStmt->fetch(PDO::FETCH_ASSOC);

        $remaining = $budget['TotalAmount'] - $budget['SpentAmount'];
        if ($netAmount > $remaining) {
            http_response_code(400);
            echo "المبلغ يتجاوز الموازنة المتاحة.";
            exit;
        }

        $stmt = $conn->prepare("INSERT INTO Expenses 
            (CustomerID, AuthorizationNumber, AccountNumber, Amount, Tax, NetAmount, Date, PaymentDate, 
            TransferNumber, TransferType, ExpenseNumber, File, Notes, CreatedAt, Description, BudgetID) 
            VALUES 
            (:customerID, :authorizationNumber, :accountNumber, :amount, :tax, :netAmount, :date, :paymentDate, 
            :transferNumber, :transferType, :expenseNumber, :file, :notes, :createdAt, :description, :budgetID)");

        $stmt->execute([
            ':customerID' => $customerID,
            ':authorizationNumber' => $_POST['authorization_number'],
            ':accountNumber' => $_POST['account_number'],
            ':amount' => $amount,
            ':tax' => $tax,
            ':netAmount' => $netAmount,
            ':date' => $_POST['date'],
            ':paymentDate' => $_POST['payment_date'],
            ':transferNumber' => $_POST['transfer_number'],
            ':transferType' => $_POST['transfer_type'],
            ':expenseNumber' => 'EXP' . time(),
            ':file' => null,
            ':notes' => $_POST['notes'] ?? null,
            ':createdAt' => date('Y-m-d H:i:s'),
            ':description' => $_POST['description'],
            ':budgetID' => $budgetID
        ]);

        $update = $conn->prepare("UPDATE Budgets SET SpentAmount = SpentAmount + ? WHERE ID = ?");
        $update->execute([$netAmount, $budgetID]);

        echo json_encode([
            'CustomerName' => $_POST['customer_name'],
            'Amount' => $amount,
            'Tax' => $tax,
            'NetAmount' => $netAmount,
            'Date' => $_POST['date'],
            'BudgetTitle' => $_POST['budget_title']
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo "خطأ: " . $e->getMessage();
    }
}
