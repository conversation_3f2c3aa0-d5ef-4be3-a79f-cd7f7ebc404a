<?php
require 'db.php';

if (!isset($_GET['name']) || strlen(trim($_GET['name'])) < 2) {
    echo json_encode([]);
    exit;
}

$name = trim($_GET['name']);

// نحاول جلب أول عميل يبدأ بهذا الاسم
$stmt = $conn->prepare("SELECT ID, AuthorizationNumber, AccountNumber, TransferType, Description, Notes 
                        FROM Customers 
                        WHERE Name LIKE ? LIMIT 1");
$stmt->execute([$name . '%']);
$customer = $stmt->fetch(PDO::FETCH_ASSOC);

echo json_encode($customer ?: []);
