<?php
session_start();
require 'db.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

$isAdmin = (isset($_SESSION['role']) && $_SESSION['role'] === 'admin');

// حذف موازنة
if ($isAdmin && isset($_GET['delete_budget'])) {
    $stmt = $conn->prepare("DELETE FROM Budgets WHERE ID = ?");
    $stmt->execute([$_GET['delete_budget']]);
    header("Location: Budget_Management.php");
    exit;
}

// حذف بند
if ($isAdmin && isset($_GET['delete_category'])) {
    $stmt = $conn->prepare("DELETE FROM Categories WHERE ID = ?");
    $stmt->execute([$_GET['delete_category']]);
    header("Location: Budget_Management.php");
    exit;
}

// إضافة موازنة
if ($isAdmin && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['title'])) {
    $stmt = $conn->prepare("INSERT INTO Budgets (Title, TotalAmount, UsedAmount) VALUES (?, ?, 0)");
    $stmt->execute([$_POST['title'], $_POST['total_amount']]);
}

// إضافة بند مرتبط بموازنة
if ($isAdmin && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['category_name'], $_POST['budget_id'])) {
    $amount = !empty($_POST['category_amount']) ? floatval($_POST['category_amount']) : null;
    $budgetID = $_POST['budget_id'];

    // استعلام للحصول على إجمالي الموازنة
    $stmt = $conn->prepare("SELECT TotalAmount FROM Budgets WHERE ID = ?");
    $stmt->execute([$budgetID]);
    $budget = $stmt->fetch(PDO::FETCH_ASSOC);
    $totalBudget = $budget['TotalAmount'];

    // استعلام لحساب مجموع المبالغ المخصصة الحالية للبنود
    $stmt = $conn->prepare("SELECT SUM(Amount) as total_assigned FROM Categories WHERE BudgetID = ?");
    $stmt->execute([$budgetID]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $assigned = $result['total_assigned'] ?? 0;

    // تحقق إذا كانت الإضافة الجديدة تتجاوز الموازنة
    if ($amount !== null && ($assigned + $amount) > $totalBudget) {
        $_SESSION['error'] = "لا يمكن تخصيص هذا المبلغ. مجموع البنود يتجاوز الموازنة. يرجى إعادة التوزيع.";
        header("Location: Budget_Management.php");
        exit;
    }

    // تنفيذ الإضافة
    $stmt = $conn->prepare("INSERT INTO Categories (Name, BudgetID, Amount) VALUES (?, ?, ?)");
    $stmt->execute([$_POST['category_name'], $budgetID, $amount]);
    header("Location: Budget_Management.php");
    exit;
}


$budgets = $conn->query("SELECT * FROM Budgets ORDER BY CreatedAt DESC")->fetchAll(PDO::FETCH_ASSOC);
$categories = $conn->query("SELECT * FROM Categories")->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة الموازنات والبنود</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .card-header {
            font-size: 1.1rem;
        }
        .btn-sm {
            font-size: 0.85rem;
        }
        .form-control, .form-select {
            font-size: 0.95rem;
        }

        /* مؤشرات التقدم المحسنة */
        .progress-container {
            margin: 10px 0;
        }

        .budget-progress {
            height: 8px;
            border-radius: 10px;
            overflow: hidden;
            background-color: #e9ecef;
        }

        .budget-progress .progress-bar {
            transition: width 0.3s ease;
        }

        .category-progress {
            height: 6px;
            border-radius: 5px;
            margin: 5px 0;
        }

        /* ألوان التحذير */
        .text-success { color: #28a745 !important; }
        .text-warning { color: #ffc107 !important; }
        .text-danger { color: #dc3545 !important; }

        .bg-success-light { background-color: rgba(40, 167, 69, 0.1); }
        .bg-warning-light { background-color: rgba(255, 193, 7, 0.1); }
        .bg-danger-light { background-color: rgba(220, 53, 69, 0.1); }

        /* تحسين البطاقات */
        .budget-card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border-radius: 15px;
            overflow: hidden;
        }

        .budget-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .budget-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .category-item {
            border-radius: 10px;
            margin-bottom: 10px;
            padding: 15px;
            background: white;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease;
        }

        .category-item:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.15);
        }

        .add-form {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
<?php include 'header.php'; ?>

<div class="main-content" id="mainContent">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1"><i class="fas fa-wallet me-2 text-primary"></i>إدارة الموازنات والبنود</h2>
                    <p class="text-muted mb-0">إدارة وتتبع الموازنات والبنود المخصصة</p>
                </div>
                <div class="text-end">
                    <?php
                    $totalBudgets = count($budgets);
                    $totalBudgetAmount = array_sum(array_column($budgets, 'TotalAmount'));
                    $totalUsedAmount = array_sum(array_column($budgets, 'UsedAmount'));
                    $totalRemaining = $totalBudgetAmount - $totalUsedAmount;
                    ?>
                    <div class="d-flex gap-3">
                        <div class="text-center">
                            <div class="h4 mb-0 text-primary"><?= $totalBudgets ?></div>
                            <small class="text-muted">موازنة</small>
                        </div>
                        <div class="text-center">
                            <div class="h4 mb-0 text-success"><?= number_format($totalBudgetAmount, 0) ?></div>
                            <small class="text-muted">إجمالي</small>
                        </div>
                        <div class="text-center">
                            <div class="h4 mb-0 text-warning"><?= number_format($totalUsedAmount, 0) ?></div>
                            <small class="text-muted">مصروف</small>
                        </div>
                        <div class="text-center">
                            <div class="h4 mb-0 <?= $totalRemaining > 0 ? 'text-success' : 'text-danger' ?>"><?= number_format($totalRemaining, 0) ?></div>
                            <small class="text-muted">متبقي</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?= $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($isAdmin): ?>
        <div class="card mb-5 shadow-lg border-0" style="border-radius: 15px;">
            <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); border-radius: 15px 15px 0 0;">
                <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>إضافة موازنة جديدة</h5>
            </div>
            <div class="card-body p-4">
                <form method="POST" class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">عنوان الموازنة</label>
                        <input type="text" name="title" class="form-control form-control-lg" placeholder="أدخل عنوان الموازنة" required>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label fw-bold">قيمة الموازنة</label>
                        <div class="input-group">
                            <input type="number" name="total_amount" class="form-control form-control-lg" placeholder="0.00" step="0.01" min="0" required>
                            <span class="input-group-text">جنيه</span>
                        </div>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-save me-1"></i>إضافة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <div class="row">
        <?php foreach ($budgets as $b):
            $totalAmount = $b['TotalAmount'];
            $usedAmount = $b['UsedAmount'];
            $remainingAmount = $totalAmount - $usedAmount;
            $usagePercentage = $totalAmount > 0 ? ($usedAmount / $totalAmount) * 100 : 0;

            // تحديد لون التحذير
            $alertClass = '';
            $progressClass = 'bg-success';
            if ($usagePercentage >= 90) {
                $alertClass = 'text-danger';
                $progressClass = 'bg-danger';
            } elseif ($usagePercentage >= 70) {
                $alertClass = 'text-warning';
                $progressClass = 'bg-warning';
            } else {
                $alertClass = 'text-success';
            }
        ?>
            <div class="col-lg-6 col-md-12">
                <div class="card mb-4 budget-card shadow-sm border-0">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <span><strong><?= htmlspecialchars($b['Title']) ?></strong></span>
                        <?php if ($isAdmin): ?>
                            <div>
                                <a href="Edit_Budget1.php?id=<?= $b['ID'] ?>" class="btn btn-sm btn-light">تعديل</a>
                                <a href="?delete_budget=<?= $b['ID'] ?>" class="btn btn-sm btn-danger"
                                   onclick="return confirm('هل أنت متأكد من الحذف؟');">حذف</a>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <!-- إحصائيات الموازنة -->
                        <div class="budget-stats">
                            <div class="stat-item">
                                <div class="stat-value text-primary"><?= number_format($totalAmount, 0) ?></div>
                                <div class="stat-label">إجمالي الموازنة</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value <?= $alertClass ?>"><?= number_format($usedAmount, 0) ?></div>
                                <div class="stat-label">المصروف</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value <?= $remainingAmount > 0 ? 'text-success' : 'text-danger' ?>"><?= number_format($remainingAmount, 0) ?></div>
                                <div class="stat-label">المتبقي</div>
                            </div>
                        </div>

                        <!-- شريط التقدم -->
                        <div class="main-content" id="mainContent">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">نسبة الاستخدام</small>
                                <small class="<?= $alertClass ?> fw-bold"><?= number_format($usagePercentage, 1) ?>%</small>
                            </div>
                            <div class="progress budget-progress">
                                <div class="progress-bar <?= $progressClass ?>" style="width: <?= min($usagePercentage, 100) ?>%"></div>
                            </div>
                        </div>

                        <hr>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">البنود المخصصة</h6>
                            <span class="badge bg-secondary"><?= count($budgetCategories ?? []) ?> بند</span>
                        </div>

                        <?php
                        $stmt = $conn->prepare("SELECT ID, Name, Amount, SpentAmount, (Amount - SpentAmount) AS RemainingAmount FROM Categories WHERE BudgetID = ?");
                        $stmt->execute([$b['ID']]);
                        $budgetCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        ?>

                        <?php if ($budgetCategories): ?>
                            <div class="main-content" id="mainContent">
                                <?php foreach ($budgetCategories as $cat):
                                    $catAmount = $cat['Amount'] ?? 0;
                                    $catSpent = $cat['SpentAmount'] ?? 0;
                                    $catRemaining = $catAmount - $catSpent;
                                    $catPercentage = $catAmount > 0 ? ($catSpent / $catAmount) * 100 : 0;

                                    // تحديد لون البند
                                    $catAlertClass = '';
                                    $catProgressClass = 'bg-success';
                                    if ($catPercentage >= 90) {
                                        $catAlertClass = 'text-danger';
                                        $catProgressClass = 'bg-danger';
                                    } elseif ($catPercentage >= 70) {
                                        $catAlertClass = 'text-warning';
                                        $catProgressClass = 'bg-warning';
                                    } else {
                                        $catAlertClass = 'text-success';
                                    }
                                ?>
                                    <div class="category-item">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <h6 class="mb-1"><?= htmlspecialchars($cat['Name']) ?></h6>
                                                <?php if ($catAmount > 0): ?>
                                                    <small class="text-muted">
                                                        مخصص: <span class="fw-bold"><?= number_format($catAmount, 0) ?></span> |
                                                        مصروف: <span class="<?= $catAlertClass ?> fw-bold"><?= number_format($catSpent, 0) ?></span> |
                                                        متبقي: <span class="<?= $catRemaining > 0 ? 'text-success' : 'text-danger' ?> fw-bold"><?= number_format($catRemaining, 0) ?></span>
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-muted">بند غير محدد المبلغ</small>
                                                <?php endif; ?>
                                            </div>
                                            <?php if ($isAdmin): ?>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="Edit_Category.php?id=<?= $cat['ID'] ?>" class="btn btn-outline-primary btn-sm">تعديل</a>
                                                    <a href="?delete_category=<?= $cat['ID'] ?>" class="btn btn-outline-danger btn-sm"
                                                       onclick="return confirm('هل أنت متأكد من الحذف؟');">حذف</a>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <?php if ($catAmount > 0): ?>
                                            <div class="d-flex justify-content-between align-items-center mb-1">
                                                <small class="text-muted">نسبة الاستخدام</small>
                                                <small class="<?= $catAlertClass ?> fw-bold"><?= number_format($catPercentage, 1) ?>%</small>
                                            </div>
                                            <div class="progress category-progress">
                                                <div class="progress-bar <?= $catProgressClass ?>" style="width: <?= min($catPercentage, 100) ?>%"></div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد بنود مضافة بعد</p>
                            </div>
                        <?php endif; ?>

                        <?php if ($isAdmin): ?>
                            <div class="add-form">
                                <h6 class="mb-3"><i class="fas fa-plus-circle me-2"></i>إضافة بند جديد</h6>
                                <form method="POST" class="row g-3">
                                    <input type="hidden" name="budget_id" value="<?= $b['ID'] ?>">
                                    <div class="col-md-6">
                                        <label class="form-label">اسم البند</label>
                                        <input type="text" name="category_name" class="form-control" placeholder="أدخل اسم البند" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">المبلغ المخصص</label>
                                        <input type="number" name="category_amount" step="0.01" class="form-control" placeholder="0.00" min="0">
                                        <small class="text-muted">اتركه فارغاً إذا لم يكن محدد</small>
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-plus me-1"></i>إضافة
                                        </button>
                                    </div>
                                </form>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

</div><script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للبطاقات
    const cards = document.querySelectorAll('.budget-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // تحديث شرائط التقدم بتأثير متحرك
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
});
</script>
</body>
</html>
