<?php
require 'db.php';
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo "<script>alert('ليس لديك صلاحية الوصول إلى هذه الصفحة.'); window.location.href = '/e-finance/index';</script>";
    exit;
}

$success = $error = "";

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    try {
        $customerID = $_POST['customer_id'];
        $authorizationNumber = $_POST['authorization_number'];
        $accountNumber = $_POST['account_number'];
        $amount = $_POST['amount'];
        $tax = $_POST['tax'];
        $netAmount = $_POST['net_amount'];
        $date = $_POST['date'];
        $paymentDate = $_POST['payment_date'];
        $transferNumber = $_POST['transfer_number'];
        $transferType = $_POST['transfer_type'];
        $expenseNumber = $_POST['expense_number'];
        $notes = $_POST['notes'];
        $description = $_POST['description'];
        $budgetID = $_POST['budget_id'];
        $categoryID = $_POST['category_id'];
        $createdAt = date('Y-m-d H:i:s');
        $file = "";

        // تحقق من الملف
        if (isset($_FILES['file']) && $_FILES['file']['error'] === UPLOAD_ERR_OK) {
            $uploadDir = 'uploads/';
            $filename = uniqid() . '_' . basename($_FILES['file']['name']);
            $uploadPath = $uploadDir . $filename;

            if (move_uploaded_file($_FILES['file']['tmp_name'], $uploadPath)) {
                $file = $uploadPath;
            }
        }

        // التحقق من الرصيد المتاح في الموازنة
        $stmt = $conn->prepare("SELECT TotalAmount, SpentAmount FROM Budgets WHERE ID = ?");
        $stmt->execute([$budgetID]);
        $budget = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$budget) throw new Exception("الموازنة غير موجودة.");
        $budgetRemaining = $budget['TotalAmount'] - $budget['SpentAmount'];
        if ($netAmount > $budgetRemaining) throw new Exception("المبلغ يتجاوز المتاح في الموازنة.");

        // التحقق من رصيد البند
        $catStmt = $conn->prepare("SELECT amount, SpentAmount FROM Categories  WHERE ID = ?");
        $catStmt->execute([$categoryID]);
        $category = $catStmt->fetch(PDO::FETCH_ASSOC);
        $categoryRemaining = $category['TotalAmount'] - $category['SpentAmount'];
        if ($netAmount > $categoryRemaining) throw new Exception("المبلغ يتجاوز المتاح في البند.");

        // إدخال الصرفية
        $stmt = $conn->prepare("INSERT INTO Expenses 
            (CustomerID, AuthorizationNumber, AccountNumber, Amount, Tax, NetAmount, Date, PaymentDate, 
            TransferNumber, TransferType, ExpenseNumber, File, Notes, CreatedAt, Description, BudgetID, CategoryID) 
            VALUES 
            (:customerID, :authorizationNumber, :accountNumber, :amount, :tax, :netAmount, :date, :paymentDate, 
            :transferNumber, :transferType, :expenseNumber, :file, :notes, :createdAt, :description, :budgetID, :categoryID)");

        $stmt->execute([
            ':customerID' => $customerID,
            ':authorizationNumber' => $authorizationNumber,
            ':accountNumber' => $accountNumber,
            ':amount' => $amount,
            ':tax' => $tax,
            ':netAmount' => $netAmount,
            ':date' => $date,
            ':paymentDate' => $paymentDate,
            ':transferNumber' => $transferNumber,
            ':transferType' => $transferType,
            ':expenseNumber' => $expenseNumber,
            ':file' => $file,
            ':notes' => $notes,
            ':createdAt' => $createdAt,
            ':description' => $description,
            ':budgetID' => $budgetID,
            ':categoryID' => $categoryID
        ]);

        // تحديث الموازنة
        $conn->prepare("UPDATE Budgets SET SpentAmount = SpentAmount + ? WHERE ID = ?")
            ->execute([$netAmount, $budgetID]);

        // تحديث البند
        $conn->prepare("UPDATE Categories SET SpentAmount = SpentAmount + ? WHERE ID = ?")
            ->execute([$netAmount, $categoryID]);

        $success = "تمت إضافة الصرفية بنجاح.";
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// جلب العملاء والموازنات
$customers = $conn->query("SELECT ID, Name FROM Customers")->fetchAll();
$budgets = $conn->query("SELECT ID, Title FROM Budgets")->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة الصرفيات</title>
    <!-- jQuery و jQuery UI للـ Autocomplete -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        .form-section { background: #f9f9f9; padding: 20px; border-radius: 8px; }
    </style>
</head>
<body>
    <?php include 'header.php'; ?>
    <h2>إضافة صرفية</h2>

    <?php if ($success): ?>
        <p style="color: green;"><?= $success ?></p>
    <?php elseif ($error): ?>
        <p style="color: red;"><?= $error ?></p>
    <?php endif; ?>
<div class="container mt-4">
<form method="POST" enctype="multipart/form-data" id="expenseForm" class="form-section">
        <div class="row g-3">
        <div class="col-md-4">
                <label>اسم العميل</label>
                <input type="text" id="customer_name" class="form-control" autocomplete="off">
                <div id="customer_list" class="list-group"></div>
                <input type="hidden" name="customer_id" id="customer_id">
            </div>
     <div class="col-md-4">
        <label>رقم الاذن:</label>
        <input type="text" name="authorization_number" id="authorization_number" class="form-control" required>
    </div>
    <div class="col-md-4">
        <label>رقم الحساب:</label>
        <input type="text" name="account_number" id="account_number" class="form-control" required>
    </div>
    <div class="col-md-4">
        <label>المبلغ:</label>
        <input type="number" name="amount" id="amount" class="form-control" step="0.01" required>
            </div>

             <div class="col-md-4">
        <label>الضريبة:</label>
        <input type="number" name="tax" id="tax" step="0.01"  class="form-control" required>
</div>
<div class="col-md-4">
        <label>الصافي:</label>
        <input type="number" name="net_amount" id="net_amount" class="form-control" step="0.01" readonly required>
</div>
<div class="col-md-4">
        <label>التاريخ:</label>
        <input type="date" name="date" id="date" class="form-control" required>
</div>
 <div class="col-md-4">
        <label>تاريخ الدفع:</label>
        <input type="date" name="payment_date" id="payment_date" class="form-control" required>
</div>

<div class="col-md-4">
        <label>رقم التحويل:</label>
        <input type="text" name="transfer_number" id="transfer_number" class="form-control" required>
</div>

 <div class="col-md-4">
        <label>نوع التحويل:</label>
        <input type="text" name="transfer_type" id="transfer_type" class="form-control" required>
</div>

<div class="col-md-4">
        <label>رقم الصرفية:</label>
        <input type="text" name="expense_number" class="form-control" required>
</div>

 <div class="col-md-4">
        <label>المرفقات:</label>
        <input type="file" name="file" class="form-control">
</div>

<div class="col-md-12">
        <label>الوصف:</label>
        <textarea name="description" id="description" class="form-control" rows="2" required></textarea>
</div>

<div class="col-md-12">
        <label>ملاحظات:</label>
        <textarea name="notes" id="notes" class="form-control" rows="2"></textarea>
</div>

<div class="col-md-4">
        <label>الموازنة:</label>
        <select name="budget_id" id="budget" class="form-control" required>
            <option value="">اختر الموازنة</option>
            <?php foreach ($budgets as $b): ?>
            <option value="<?= $b['ID'] ?>"><?= $b['Title'] ?></option>
            <?php endforeach; ?>
        </select>
</div>
<div class="col-md-4">
        <label>البند:</label>
        <select name="category_id" id="category" class="form-control" required>
            <option value="">اختر الموازنة أولاً</option>
        </select><br><br>
</div>
 </div>
        <button type="submit" id="submitExpense" class="btn btn-primary mt-4" style= "width: 100%">حفظ</button>
    </form>
    </div>
<?php include 'footer.php'; ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#budget').on('change', function () {
                const budgetId = $(this).val();
                const $category = $('#category');
                $category.html('<option value="">جاري التحميل...</option>');

                if (budgetId) {
                    $.ajax({
                        url: 'get_categories.php',
                        method: 'GET',
                        data: { budget_id: budgetId },
                        dataType: 'json',
                        success: function (data) {
                            $category.empty();
                            if (data.length === 0) {
                                $category.append('<option value="">لا توجد بنود</option>');
                            } else {
                                $category.append('<option value="">اختر البند</option>');
                                data.forEach(function (item) {
                                    $category.append('<option value="' + item.ID + '">' + item.Name + '</option>');
                                });
                            }
                        },
                        error: function () {
                            $category.html('<option value="">فشل في التحميل</option>');
                        }
                    });
                } else {
                    $category.html('<option value="">اختر الموازنة أولاً</option>');
                }
            });

            $('#amount, #tax').on('input', function () {
                const amount = parseFloat($('#amount').val()) || 0;
                const tax = parseFloat($('#tax').val()) || 0;
                $('#net_amount').val((amount + tax).toFixed(2));
            });
        });
    </script>
</body>
</html>
