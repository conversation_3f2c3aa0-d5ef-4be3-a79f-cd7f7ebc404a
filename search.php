<?php
session_start();
require 'db.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: /login");
    exit;
}

$is_admin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';

$query_parts = [];
$params = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {




if (!empty($_POST['category'])) {
    $query_parts[] = "Category = :category";
    $params[':category'] = $_POST['category'];
}

    if (!empty($_POST['name'])) {
        $query_parts[] = "Name LIKE :name";
        $params[':name'] = '%' . $_POST['name'] . '%';
    }
    if (!empty($_POST['national_id'])) {
        $query_parts[] = "NationalID LIKE :national_id";
        $params[':national_id'] = '%' . $_POST['national_id'] . '%';
    }
    if (!empty($_POST['account_number'])) {
        $query_parts[] = "AccountNumber LIKE :account_number";
        $params[':account_number'] = '%' . $_POST['account_number'] . '%';
    }
    if (!empty($_POST['start_date']) && !empty($_POST['end_date'])) {
        $query_parts[] = "StartDate BETWEEN :start_date AND :end_date";
        $params[':start_date'] = $_POST['start_date'];
        $params[':end_date'] = $_POST['end_date'];
    }
    if (!empty($_POST['card_expiry_start']) && !empty($_POST['card_expiry_end'])) {
        $query_parts[] = "CardExpiryDate BETWEEN :card_expiry_start AND :card_expiry_end";
        $params[':card_expiry_start'] = $_POST['card_expiry_start'];
        $params[':card_expiry_end'] = $_POST['card_expiry_end'];
    }
}

$query = "SELECT * FROM Customers";
if (!empty($query_parts)) {
    $query .= " WHERE " . implode(" AND ", $query_parts);
}
$sort_by = $_GET['sort_by'] ?? 'ID';
$allowed_sort = ['ID', 'Name', 'Amount'];
$sort_by = in_array($sort_by, $allowed_sort) ? $sort_by : 'ID';
$query .= " ORDER BY $sort_by DESC";

$stmt = $conn->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$search_results = $stmt->fetchAll(PDO::FETCH_ASSOC);
// بعد تنفيذ الاستعلام مباشرة
$_SESSION['filter_params'] = $params;
$_SESSION['filter_sql'] = implode(" AND ", $query_parts);

if (isset($_GET['delete_id']) && $is_admin) {
    $stmt = $conn->prepare("DELETE FROM Customers WHERE ID = :id");
    $stmt->bindParam(':id', $_GET['delete_id']);
    $stmt->execute();
    header("Location: search.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة العملاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">

    <!-- Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- DataTables CSS with Bootstrap 5 -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.bootstrap5.min.css">

    <style>
        /* تحسين الجدول ليأخذ عرض الصفحة الكامل */
        body {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e3f2fd 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 100% !important;
            padding: 0 15px;
        }

        .table-responsive {
            width: 100%;
            overflow-x: auto;
            margin-bottom: 1rem;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            background: white;
        }

        .table-custom {
            width: 100% !important;
            min-width: 1400px;
            font-size: 13px;
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table-custom th {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            font-weight: 600;
            text-align: center;
            padding: 12px 8px;
            border: none;
            white-space: nowrap;
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 12px;
        }

        .table-custom td {
            padding: 10px 8px;
            text-align: center;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
            border-right: 1px solid #e9ecef;
            white-space: nowrap;
            font-size: 12px;
        }

        .table-custom tbody tr:hover {
            background-color: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .table-custom tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        /* تحسين شكل مربع البحث */
        .dataTables_filter {
            text-align: left !important;
            margin-bottom: 15px;
        }

        .dataTables_filter label {
            font-weight: bold;
            font-size: 16px;
            color: #333;
        }

        .dataTables_filter input {
            border-radius: 5px;
            padding: 8px 12px;
            margin-right: 10px;
            border: 2px solid #ddd;
            transition: border-color 0.3s ease;
        }

        .dataTables_filter input:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* تحسين شكل أزرار التصدير */
        .dt-buttons .btn {
            margin-left: 5px;
            border-radius: 6px;
            font-size: 12px;
            padding: 6px 12px;
        }

        /* تحسين أزرار الإجراءات */
        .action-buttons .btn {
            margin: 2px;
            padding: 4px 8px;
            font-size: 11px;
            border-radius: 4px;
        }

        /* تحسين شكل الصفوف المنتهية الصلاحية */
        .table-warning {
            background-color: #fff3cd !important;
            border-left: 4px solid #ffc107;
        }

        /* تحسين شكل نموذج البحث */
        .search-form {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .search-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #0056b3);
        }

        .search-form h4 {
            background: linear-gradient(135deg, #000000, #333333);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 25px;
        }

        /* تحسين الأزرار */
        .btn-group-custom .btn {
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-group-custom .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-group-custom .btn:hover::before {
            left: 100%;
        }

        .btn-group-custom .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
            border: none;
        }

        .btn-success {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
        }

        .btn-danger {
            background: linear-gradient(135deg, #000000, #333333);
            border: none;
        }

        /* تحسين عرض الجدول على الشاشات الصغيرة */
        @media (max-width: 768px) {
            .table-custom {
                font-size: 11px;
            }

            .table-custom th,
            .table-custom td {
                padding: 6px 4px;
            }

            .container {
                padding: 0 10px;
            }

            .search-form {
                padding: 15px;
            }
        }

        /* تحسين مظهر DataTables */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate {
            margin-bottom: 10px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            padding: 6px 12px;
            margin: 0 2px;
            border-radius: 4px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: #007bff !important;
            color: white !important;
            border: 1px solid #007bff !important;
        }

        /* تحسين مظهر الأزرار */
        .dt-buttons {
            margin-bottom: 15px;
        }

        .dt-button {
            margin-right: 5px !important;
            border-radius: 6px !important;
            font-size: 12px !important;
        }

        /* تحسين مظهر البحث السريع */
        .dataTables_filter input[type="search"] {
            width: 250px !important;
            padding: 8px 12px !important;
            border: 2px solid #ddd !important;
            border-radius: 6px !important;
        }

        /* تحسين مظهر عرض عدد النتائج */
        .dataTables_length select {
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
<?php include 'header.php'; ?>
<div class="main-content" id="mainContent">
   <!-- <h1 class="text-center mb-4">إدارة العملاء</h1>-->

    <form method="POST" class="search-form">
        <h4 class="mb-3 text-center" style="color: #2c3e50; font-weight: 600;">
            <i class="fas fa-search me-2"></i>البحث في بيانات العملاء
        </h4>
        <div class="row g-3">
            <div class="col-md-4">
                <label class="form-label">الاسم</label>
                <input type="text" name="name" class="form-control" placeholder="بحث بالاسم" value="<?= htmlspecialchars($_POST['name'] ?? '') ?>">
            </div>
            <div class="col-md-4">
                <label class="form-label">الرقم القومي</label>
                <input type="text" name="national_id" class="form-control" placeholder="بحث بالرقم القومي" value="<?= htmlspecialchars($_POST['national_id'] ?? '') ?>">
            </div>
            <div class="col-md-4">
                <label class="form-label">رقم الحساب</label>
                <input type="text" name="account_number" class="form-control" placeholder="بحث برقم الحساب" value="<?= htmlspecialchars($_POST['account_number'] ?? '') ?>">
            </div>

            <div class="col-md-3">
                <label class="form-label">من تاريخ</label>
                <input type="date" name="start_date" class="form-control" value="<?= htmlspecialchars($_POST['start_date'] ?? '') ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">إلى تاريخ</label>
                <input type="date" name="end_date" class="form-control" value="<?= htmlspecialchars($_POST['end_date'] ?? '') ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">انتهاء الكارت من</label>
                <input type="date" name="card_expiry_start" class="form-control" value="<?= htmlspecialchars($_POST['card_expiry_start'] ?? '') ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">انتهاء الكارت إلى</label>
                <input type="date" name="card_expiry_end" class="form-control" value="<?= htmlspecialchars($_POST['card_expiry_end'] ?? '') ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">البند</label>
                <select name="category" class="form-select">
                <option value="">-- اختر البند --</option>
                <option value="مساعدات" <?= (isset($_POST['category']) && $_POST['category'] == 'مساعدات') ? 'selected' : '' ?>>مساعدات</option>
                <option value="تشغيل" <?= (isset($_POST['category']) && $_POST['category'] == 'تشغيل') ? 'selected' : '' ?>>تشغيل</option>
                <option value="تشغيل شهري" <?= (isset($_POST['category']) && $_POST['category'] == 'تشغيل شهري') ? 'selected' : '' ?>>تشغيل شهري</option>
            </select>
            </div>
        </div>

        <div class="row mt-4 btn-group-custom">
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-2"></i>بحث
                </button>
            </div>
            <div class="col-md-3">
                <button type="reset" class="btn btn-secondary w-100" onclick="window.location.href='search.php'">
                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                </button>
            </div>
            <div class="col-md-3">
                <a href="export_excel.php" class="btn btn-success w-100">
                    <i class="fas fa-file-excel me-2"></i>تحميل Excel
                </a>
            </div>
            <div class="col-md-3">
                <a href="export_pdf.php" class="btn btn-danger w-100">
                    <i class="fas fa-file-pdf me-2"></i>تحميل PDF
                </a>
            </div>
        </div>
    </form>

    <?php if (!empty($search_results)): ?>
        <div class="results-header mb-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0" style="color: #2c3e50;">
                        <i class="fas fa-table me-2"></i>نتائج البحث
                        <span class="badge bg-primary ms-2"><?= count($search_results) ?> نتيجة</span>
                    </h5>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        يمكنك التمرير أفقياً لعرض جميع الأعمدة
                    </small>
                </div>
            </div>
        </div>

        <div class="table-responsive w-100">
            <table class="table table-bordered table-striped table-custom text-center" id="customersTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>الاسم</th>
                        <th>الرقم القومي</th>
                        <th>رقم الحساب</th>
                        <th>المبلغ</th>
                        <th>الضريبة</th>
                        <th>الصافي</th>
                        <th>مدة الصرف</th>
                        <th>تاريخ البداية</th>
                        <th>تاريخ النهاية</th>
                        <th>رقم الإذن</th>
                        <th>رقم التحويل</th>
                        <th>عدد المستندات</th>
                        <th>تاريخ انتهاء الكارت</th>
                        <th>الملاحظات</th>
                        <th>البند</th>
                        <th>الملف</th>
                        <?php if ($is_admin): ?>
                            <th>إجراءات</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($search_results as $result): ?>
                    <?php
                        $expiry = $result['CardExpiryDate'] ?? null;
                        $is_expiring = false;
                        if ($expiry) {
                            $expiry_date = new DateTime($expiry);
                            $today = new DateTime();
                            $interval = $today->diff($expiry_date)->days;
                            $is_expiring = $expiry_date > $today && $interval <= 30;
                        }
                    ?>
                    <tr class="<?= $is_expiring ? 'table-warning' : '' ?>">
                        <td><?= $result['ID'] ?></td>
                        <td><?= $result['Name'] ?></td>
                        <td><?= $result['NationalID'] ?></td>
                        <td><?= $result['AccountNumber'] ?></td>
                        <td><?= $result['Amount'] ?></td>
                        <td><?= $result['Tax'] ?></td>
                        <td><?= $result['Net'] ?></td>
                        <td><?= $result['PaymentDuration'] ?></td>
                        <td><?= $result['StartDate'] ?></td>
                        <td><?= $result['EndDate'] ?></td>
                        <td><?= $result['AuthorizationNumber'] ?></td>
                        <td><?= $result['TransferNumber'] ?></td>
                        <td><?= $result['DocumentCount'] ?></td>
                        <td><?= $result['CardExpiryDate'] ?></td>
                        <td><?= $result['Notes'] ?></td>
                        <td><?= $result['Category'] ?></td>
                        <td>
                            <?php if (!empty($result['FilePath'])): ?>
                                <a href="<?= $result['FilePath'] ?>" target="_blank" class="btn btn-sm btn-success">
                                    <i class="fas fa-eye me-1"></i>عرض
                                </a>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <?php if ($is_admin): ?>
                        <td class="action-buttons">
                            <div class="btn-group" role="group">
                                <a href="edit.php?id=<?= $result['ID'] ?>" class="btn btn-sm btn-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="?delete_id=<?= $result['ID'] ?>" class="btn btn-sm btn-danger"
                                   onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                        <?php endif; ?>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="no-results text-center py-5">
            <div class="card border-0 shadow-sm">
                <div class="card-body py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted mb-3">لا توجد نتائج</h4>
                    <p class="text-muted mb-4">لم يتم العثور على أي عملاء يطابقون معايير البحث المحددة</p>
                    <div class="d-flex justify-content-center gap-2">
                        <button type="button" class="btn btn-outline-primary" onclick="document.querySelector('form').reset();">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين البحث
                        </button>
                        <a href="add_customer.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div><!-- jQuery & DataTables Scripts -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>

<!-- Arabic Translation -->
<script src="//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json"></script>

<!-- DataTables Init -->
<script>
    $(document).ready(function () {
        $('#customersTable').DataTable({
            scrollX: true,
            scrollY: '500px',
            scrollCollapse: true,
            paging: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "الكل"]],
            language: {
                url: "//cdn.datatables.net/plug-ins/1.13.6/i18n/ar.json"
            },
            dom: 'Blfrtip',
            buttons: [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel"></i> Excel',
                    className: 'btn btn-success btn-sm',
                    exportOptions: {
                        columns: ':visible'
                    }
                },
                {
                    extend: 'pdf',
                    text: '<i class="fas fa-file-pdf"></i> PDF',
                    className: 'btn btn-danger btn-sm',
                    exportOptions: {
                        columns: ':visible'
                    },
                    orientation: 'landscape',
                    pageSize: 'A3'
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> طباعة',
                    className: 'btn btn-info btn-sm',
                    exportOptions: {
                        columns: ':visible'
                    }
                },
                {
                    extend: 'colvis',
                    text: '<i class="fas fa-columns"></i> إظهار/إخفاء الأعمدة',
                    className: 'btn btn-secondary btn-sm'
                }
            ],
            responsive: true,
            fixedHeader: true,
            order: [[0, 'desc']],
            columnDefs: [
                {
                    targets: -1,
                    orderable: false,
                    searchable: false
                }
            ],
            initComplete: function () {
                // إضافة مؤشر التحميل
                $('.dataTables_processing').html('<i class="fas fa-spinner fa-spin"></i> جاري التحميل...');
            }
        });

        // تحسين عرض الجدول على الشاشات الصغيرة
        $(window).resize(function() {
            $('#customersTable').DataTable().columns.adjust().draw();
        });
    });
</script>

</body>
</html>
