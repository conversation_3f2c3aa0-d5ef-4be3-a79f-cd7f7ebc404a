<?php
session_start();
require 'db.php';
require_once 'functions.php';

// التحقق من الصلاحيات
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo "<script>alert('ليس لديك صلاحية الوصول إلى هذه الصفحة.'); window.location.href = '/e-finance/index';</script>";
    exit;
}

// التحقق من وجود معرف المستخدم
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo "<script>alert('معرف المستخدم غير صالح'); window.location.href = 'manage_users.php';</script>";
    exit;
}

$user_id = intval($_GET['id']);

// جلب بيانات المستخدم
$stmt = $conn->prepare("SELECT * FROM Users WHERE ID = :id");
$stmt->bindParam(':id', $user_id, PDO::PARAM_INT);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    echo "<script>alert('المستخدم غير موجود'); window.location.href = 'manage_users.php';</script>";
    exit;
}

// تحديث بيانات المستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_user'])) {
    $username = $_POST['username'];
    $role = $_POST['role'];
    $password = $_POST['password'];

    if (!empty($password)) {
        $hashed_password = password_hash($password, PASSWORD_BCRYPT);
        $stmt = $conn->prepare("UPDATE Users SET Username = :username, Password = :password, Role = :role WHERE ID = :id");
        $stmt->bindParam(':password', $hashed_password);
    } else {
        $stmt = $conn->prepare("UPDATE Users SET Username = :username, Role = :role WHERE ID = :id");
    }

    $stmt->bindParam(':username', $username);
    $stmt->bindParam(':role', $role);
    $stmt->bindParam(':id', $user_id);

    if ($stmt->execute()) {
        echo "<script>alert('تم تحديث بيانات المستخدم بنجاح'); window.location.href = 'manage_users.php';</script>";
        exit;
    } else {
        echo "<script>alert('حدث خطأ أثناء التحديث');</script>";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تعديل المستخدم</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<?php include 'header.php'; ?>
<div class="container mt-5">
    <h2 class="text-center mb-4">تعديل بيانات المستخدم</h2>
    <form method="POST">
        <div class="mb-3">
            <label for="username" class="form-label">اسم المستخدم</label>
            <input type="text" name="username" id="username" class="form-control" value="<?= htmlspecialchars($user['Username']) ?>" required>
        </div>
        <div class="mb-3">
            <label for="password" class="form-label">كلمة المرور الجديدة (اتركها فارغة للإبقاء على الحالية)</label>
            <input type="password" name="password" id="password" class="form-control">
        </div>
        <div class="mb-3">
            <label for="role" class="form-label">الدور</label>
            <select name="role" id="role" class="form-select" required>
                <option value="viewer" <?= $user['Role'] === 'viewer' ? 'selected' : '' ?>>مشاهدة فقط</option>
                <option value="editor" <?= $user['Role'] === 'editor' ? 'selected' : '' ?>>تعديل وإضافة</option>
                <option value="admin" <?= $user['Role'] === 'admin' ? 'selected' : '' ?>>مسؤول</option>
            </select>
        </div>
        <button type="submit" name="update_user" class="btn btn-success">تحديث المستخدم</button>
        <a href="manage_users.php" class="btn btn-secondary">رجوع</a>
    </form>
</div></body>
</html>
