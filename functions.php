<?php
function logActivity($conn, $userId, $page, $action, $url, $method, $data = null, $duration = null) {
    try {
        $stmt = $conn->prepare("
            INSERT INTO UserActivity (UserID, Page, Action, URL, Method, Data, Duration, Timestamp)
            VALUES (:userId, :page, :action, :url, :method, :data, :duration, NOW())
        ");
        $stmt->execute([
            ':userId' => $userId,
            ':page' => $page,
            ':action' => $action,
            ':url' => $url,
            ':method' => $method,
            ':data' => $data,
            ':duration' => $duration
        ]);
    } catch (PDOException $e) {
        error_log("Activity log error: " . $e->getMessage());
    }
}
?>
