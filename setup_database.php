<?php
require_once 'db.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// التحقق من صلاحيات المدير
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    die('غير مصرح لك بالوصول لهذه الصفحة');
}

$success_messages = [];
$error_messages = [];

// دالة لتنفيذ استعلام مع تجاهل الأخطاء
function executeQuery($conn, $query, $description) {
    global $success_messages, $error_messages;
    
    try {
        $conn->exec($query);
        $success_messages[] = "✅ $description";
        return true;
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false || 
            strpos($e->getMessage(), 'already exists') !== false ||
            strpos($e->getMessage(), 'Duplicate key name') !== false) {
            $success_messages[] = "ℹ️ $description (موجود مسبقاً)";
            return true;
        } else {
            $error_messages[] = "❌ خطأ في $description: " . $e->getMessage();
            return false;
        }
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_database'])) {
    
    // إضافة الأعمدة الجديدة
    $columns = [
        "ALTER TABLE `notifications` ADD COLUMN `Priority` ENUM('منخفضة', 'متوسطة', 'عالية', 'عاجلة') DEFAULT 'متوسطة' AFTER `Status`" => "إضافة عمود الأولوية",
        "ALTER TABLE `notifications` ADD COLUMN `Category` VARCHAR(100) DEFAULT NULL AFTER `Priority`" => "إضافة عمود التصنيف",
        "ALTER TABLE `notifications` ADD COLUMN `AssignedTo` INT(11) DEFAULT NULL AFTER `Category`" => "إضافة عمود المسؤول",
        "ALTER TABLE `notifications` ADD COLUMN `Progress` INT(3) DEFAULT 0 AFTER `AssignedTo`" => "إضافة عمود التقدم",
        "ALTER TABLE `notifications` ADD COLUMN `Notes` TEXT DEFAULT NULL AFTER `Progress`" => "إضافة عمود الملاحظات",
        "ALTER TABLE `notifications` ADD COLUMN `LastUpdated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `Notes`" => "إضافة عمود آخر تحديث",
        "ALTER TABLE `notifications` ADD COLUMN `CreatedBy` INT(11) DEFAULT NULL AFTER `LastUpdated`" => "إضافة عمود منشئ المهمة"
    ];
    
    foreach ($columns as $query => $description) {
        executeQuery($conn, $query, $description);
    }
    
    // إنشاء جدول التصنيفات
    $createCategoriesTable = "
    CREATE TABLE IF NOT EXISTS `task_categories` (
      `ID` int(11) NOT NULL AUTO_INCREMENT,
      `Name` varchar(100) NOT NULL,
      `Description` text DEFAULT NULL,
      `Color` varchar(7) DEFAULT '#007bff',
      `Icon` varchar(50) DEFAULT 'fas fa-folder',
      `CreatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `IsActive` tinyint(1) DEFAULT 1,
      PRIMARY KEY (`ID`),
      UNIQUE KEY `unique_name` (`Name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    executeQuery($conn, $createCategoriesTable, "إنشاء جدول التصنيفات");
    
    // إدراج التصنيفات الافتراضية
    $insertCategories = "
    INSERT IGNORE INTO `task_categories` (`Name`, `Description`, `Color`, `Icon`) VALUES
    ('عام', 'مهام عامة', '#6c757d', 'fas fa-tasks'),
    ('مالية', 'مهام متعلقة بالشؤون المالية', '#28a745', 'fas fa-dollar-sign'),
    ('إدارية', 'مهام إدارية وتنظيمية', '#007bff', 'fas fa-briefcase'),
    ('تقنية', 'مهام تقنية وتطوير', '#17a2b8', 'fas fa-code'),
    ('عاجل', 'مهام عاجلة تحتاج متابعة فورية', '#dc3545', 'fas fa-exclamation-triangle')";
    
    executeQuery($conn, $insertCategories, "إدراج التصنيفات الافتراضية");
    
    // إنشاء جدول التعليقات
    $createCommentsTable = "
    CREATE TABLE IF NOT EXISTS `task_comments` (
      `ID` int(11) NOT NULL AUTO_INCREMENT,
      `TaskID` int(11) NOT NULL,
      `UserID` int(11) NOT NULL,
      `Comment` text NOT NULL,
      `CreatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`ID`),
      KEY `idx_task_id` (`TaskID`),
      KEY `idx_user_id` (`UserID`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    executeQuery($conn, $createCommentsTable, "إنشاء جدول التعليقات");
    
    // إنشاء جدول سجل التغييرات
    $createHistoryTable = "
    CREATE TABLE IF NOT EXISTS `task_history` (
      `ID` int(11) NOT NULL AUTO_INCREMENT,
      `TaskID` int(11) NOT NULL,
      `UserID` int(11) NOT NULL,
      `Action` varchar(100) NOT NULL,
      `OldValue` text DEFAULT NULL,
      `NewValue` text DEFAULT NULL,
      `CreatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`ID`),
      KEY `idx_task_id` (`TaskID`),
      KEY `idx_user_id` (`UserID`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    executeQuery($conn, $createHistoryTable, "إنشاء جدول سجل التغييرات");
    
    // إضافة الفهارس
    $indexes = [
        "ALTER TABLE `notifications` ADD INDEX `idx_priority` (`Priority`)" => "إضافة فهرس الأولوية",
        "ALTER TABLE `notifications` ADD INDEX `idx_category` (`Category`)" => "إضافة فهرس التصنيف",
        "ALTER TABLE `notifications` ADD INDEX `idx_assigned_to` (`AssignedTo`)" => "إضافة فهرس المسؤول",
        "ALTER TABLE `notifications` ADD INDEX `idx_created_by` (`CreatedBy`)" => "إضافة فهرس منشئ المهمة"
    ];
    
    foreach ($indexes as $query => $description) {
        executeQuery($conn, $query, $description);
    }
    
    // تحديث جدول المستخدمين
    $userUpdates = [
        "ALTER TABLE `users` ADD COLUMN `FullName` VARCHAR(255) DEFAULT NULL AFTER `Username`" => "إضافة عمود الاسم الكامل للمستخدمين",
        "ALTER TABLE `users` ADD COLUMN `IsActive` TINYINT(1) DEFAULT 1 AFTER `FullName`" => "إضافة عمود حالة المستخدم"
    ];
    
    foreach ($userUpdates as $query => $description) {
        executeQuery($conn, $query, $description);
    }
    
    // تحديث المهام الموجودة
    try {
        $updateExisting = "UPDATE `notifications` SET `CreatedBy` = 1 WHERE `CreatedBy` IS NULL";
        $conn->exec($updateExisting);
        $success_messages[] = "✅ تحديث المهام الموجودة";
    } catch (PDOException $e) {
        // تجاهل الخطأ إذا كان العمود غير موجود
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - تحسينات إدارة المهام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e3f2fd 100%); }
        .setup-container { max-width: 800px; margin: 50px auto; }
        .setup-card { background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container setup-container">
        <div class="setup-card p-4">
            <div class="text-center mb-4">
                <h1><i class="fas fa-database"></i> إعداد قاعدة البيانات</h1>
                <p class="text-muted">تحديث قاعدة البيانات لدعم ميزات إدارة المهام المحسنة</p>
            </div>
            
            <?php if (!empty($success_messages) || !empty($error_messages)): ?>
                <div class="results mb-4">
                    <?php foreach ($success_messages as $msg): ?>
                        <div class="message success"><?= $msg ?></div>
                    <?php endforeach; ?>
                    
                    <?php foreach ($error_messages as $msg): ?>
                        <div class="message error"><?= $msg ?></div>
                    <?php endforeach; ?>
                    
                    <?php if (empty($error_messages)): ?>
                        <div class="alert alert-success text-center mt-3">
                            <h4><i class="fas fa-check-circle"></i> تم الإعداد بنجاح!</h4>
                            <p>يمكنك الآن الانتقال إلى صفحة إدارة المهام للاستفادة من الميزات الجديدة.</p>
                            <a href="Add_Notification2.php" class="btn btn-success">
                                <i class="fas fa-arrow-left"></i> الانتقال لإدارة المهام
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> ما سيتم تنفيذه:</h5>
                    <ul>
                        <li>إضافة أعمدة جديدة لجدول المهام (الأولوية، التصنيف، المسؤول، التقدم، الملاحظات)</li>
                        <li>إنشاء جدول التصنيفات مع البيانات الافتراضية</li>
                        <li>إنشاء جدول التعليقات</li>
                        <li>إنشاء جدول سجل التغييرات</li>
                        <li>إضافة فهارس لتحسين الأداء</li>
                        <li>تحديث جدول المستخدمين</li>
                    </ul>
                    <p><strong>ملاحظة:</strong> هذه العملية آمنة ولن تؤثر على البيانات الموجودة.</p>
                </div>
                
                <div class="text-center">
                    <button type="submit" name="setup_database" class="btn btn-primary btn-lg">
                        <i class="fas fa-play"></i> بدء إعداد قاعدة البيانات
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
