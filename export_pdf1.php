<?php
ob_start(); // يبدأ تخزين المخرجات مؤقتًا لمنع أي خطأ
session_start();
require 'db.php';
require_once('TCPDF/tcpdf.php'); // تأكد أن المسار صحيح

// إعدادات PDF
$pdf = new TCPDF();
$pdf->setRTL(true); // لتفعيل الكتابة من اليمين لليسار
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('Your Name');
$pdf->SetTitle('Export Customers Data');
$pdf->SetSubject('Customer Report');
$pdf->SetMargins(15, 20, 15);
$pdf->AddPage();

// إعداد خط العنوان
$pdf->SetFont('dejavusans', '', 12);


// العنوان
$pdf->Cell(0, 10, 'تقارير العملاء', 0, 1, 'C');

// إعداد الخط لبقية الجدول
$pdf->SetFont('dejavusans', '', 12);

// استعلام البيانات
$query = "SELECT * FROM Customers";
$params = [];

if (!empty($_SESSION['filter_sql'])) {
    $query .= " WHERE " . $_SESSION['filter_sql'];
    $params = $_SESSION['filter_params'];
}

$stmt = $conn->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

// عناوين الأعمدة
$pdf->Ln(10);
$pdf->Cell(15, 10, 'ID', 1, 0, 'C');
$pdf->Cell(30, 10, 'الاسم', 1, 0, 'C');
$pdf->Cell(30, 10, 'الرقم القومي', 1, 0, 'C');
$pdf->Cell(30, 10, 'رقم الحساب', 1, 0, 'C');
$pdf->Cell(20, 10, 'المبلغ', 1, 0, 'C');
$pdf->Cell(20, 10, 'الضريبة', 1, 0, 'C');
$pdf->Cell(20, 10, 'الصافي', 1, 0, 'C');
$pdf->Cell(30, 10, 'مدة الصرف', 1, 0, 'C');
$pdf->Cell(25, 10, 'تاريخ البداية', 1, 0, 'C');
$pdf->Cell(25, 10, 'تاريخ النهاية', 1, 0, 'C');
$pdf->Cell(20, 10, 'رقم الإذن', 1, 0, 'C');
$pdf->Cell(20, 10, 'رقم التحويل', 1, 0, 'C');
$pdf->Cell(20, 10, 'عدد المستندات', 1, 0, 'C');
$pdf->Cell(30, 10, 'تاريخ انتهاء الكارت', 1, 0, 'C');
$pdf->Cell(30, 10, 'الملاحظات', 1, 0, 'C');
$pdf->Cell(20, 10, 'البند', 1, 1, 'C');

// تعبئة البيانات
foreach ($results as $row) {
    $pdf->Cell(15, 10, $row['ID'], 1, 0, 'C');
    $pdf->Cell(30, 10, $row['Name'], 1, 0, 'C');
    $pdf->Cell(30, 10, $row['NationalID'], 1, 0, 'C');
    $pdf->Cell(30, 10, $row['AccountNumber'], 1, 0, 'C');
    $pdf->Cell(20, 10, $row['Amount'], 1, 0, 'C');
    $pdf->Cell(20, 10, $row['Tax'], 1, 0, 'C');
    $pdf->Cell(20, 10, $row['Net'], 1, 0, 'C');
    $pdf->Cell(30, 10, $row['PaymentDuration'], 1, 0, 'C');
    $pdf->Cell(25, 10, $row['StartDate'], 1, 0, 'C');
    $pdf->Cell(25, 10, $row['EndDate'], 1, 0, 'C');
    $pdf->Cell(20, 10, $row['AuthorizationNumber'], 1, 0, 'C');
    $pdf->Cell(20, 10, $row['TransferNumber'], 1, 0, 'C');
    $pdf->Cell(20, 10, $row['DocumentCount'], 1, 0, 'C');
    $pdf->Cell(30, 10, $row['CardExpiryDate'] ?? '', 1, 0, 'C');
    $pdf->Cell(30, 10, $row['Notes'], 1, 0, 'C');
    $pdf->Cell(20, 10, $row['Category'], 1, 1, 'C');
}

ob_end_clean(); // لازم قبل Output
$pdf->Output('customers_report.pdf', 'D');
exit;
