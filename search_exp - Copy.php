<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /login");
    exit;
}

// عرض الأخطاء أثناء التطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

// جلب العملاء من قاعدة البيانات لاستخدامها في البحث
$customersStmt = $conn->query("SELECT ID, Name FROM Customers");
$customers = $customersStmt->fetchAll(PDO::FETCH_ASSOC);

// التحقق من مدخلات البحث
$customerID = isset($_GET['customer_id']) ? $_GET['customer_id'] : null;
$expenseNumber = isset($_GET['expense_number']) ? $_GET['expense_number'] : null;
$authorizationNumber = isset($_GET['authorization_number']) ? $_GET['authorization_number'] : null;

$query = "SELECT Expenses.*, Customers.Name AS CustomerName 
          FROM Expenses 
          JOIN Customers ON Expenses.CustomerID = Customers.ID 
          WHERE 1=1";

if (!empty($customerID)) {
    $query .= " AND Expenses.CustomerID = :customerID";
}
if (!empty($expenseNumber)) {
    $query .= " AND Expenses.ExpenseNumber LIKE :expenseNumber";
}
if (!empty($authorizationNumber)) {
    $query .= " AND Expenses.AuthorizationNumber LIKE :authorizationNumber";
}

$stmt = $conn->prepare($query);

if (!empty($customerID)) {
    $stmt->bindParam(':customerID', $customerID, PDO::PARAM_INT);
}
if (!empty($expenseNumber)) {
    $searchExpenseNumber = "%$expenseNumber%";
    $stmt->bindParam(':expenseNumber', $searchExpenseNumber);
}
if (!empty($authorizationNumber)) {
    $searchAuthorizationNumber = "%$authorizationNumber%";
    $stmt->bindParam(':authorizationNumber', $searchAuthorizationNumber);
}

$stmt->execute();
$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بحث الصرفيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        table {
            width: 100%;
            table-layout: fixed;
        }
        th, td {
            word-wrap: break-word;
        }
    </style>
</head>
<body>
<?php include 'header.php'; ?>

<div class="container">
    <h1 class="text-center mb-4">بحث الصرفيات</h1>

    <form method="GET">
        <div class="row g-3">
            <div class="col-md-4">
                <label for="customer_id" class="form-label">اسم العميل</label>
                <select name="customer_id" id="customer_id" class="form-control">
                    <option value="">جميع العملاء</option>
                    <?php foreach ($customers as $customer) { ?>
                        <option value="<?= $customer['ID'] ?>" <?= $customer['ID'] == $customerID ? 'selected' : '' ?>><?= $customer['Name'] ?></option>
                    <?php } ?>
                </select>
            </div>
            <div class="col-md-4">
                <label for="expense_number" class="form-label">رقم الصرفية</label>
                <input type="text" name="expense_number" id="expense_number" class="form-control" value="<?= htmlspecialchars($expenseNumber) ?>">
            </div>
            <div class="col-md-4">
                <label for="authorization_number" class="form-label">رقم الإذن</label>
                <input type="text" name="authorization_number" id="authorization_number" class="form-control" value="<?= htmlspecialchars($authorizationNumber) ?>">
            </div>
        </div>

        <button type="submit" class="btn btn-primary mt-4">بحث</button>
    </form>

    <div class="mt-5">
        <h2>نتائج البحث</h2>
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>رقم الصرفية</th>
                    <th>اسم العميل</th>
                    <th>المبلغ</th>
                    <th>الضريبة</th>
                    <th>المبلغ الصافي</th>
                    <th>تاريخ الصرف</th>
                    <th>تاريخ الدفع</th>
                    <th>الوصف</th>
                    <th>رقم الإذن</th>
                    <th>ملف</th>
                    <th>نوع التحويل</th>
                    <th>ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($expenses as $expense) { ?>
                    <tr>
                        <td><?= $expense['ExpenseNumber'] ?></td>
                        <td><?= $expense['CustomerName'] ?></td>
                        <td><?= number_format($expense['Amount'], 2) ?></td>
                        <td><?= number_format($expense['Tax'], 2) ?></td>
                        <td><?= number_format($expense['NetAmount'], 2) ?></td>
                        <td><?= $expense['Date'] ?></td>
                        <td><?= $expense['PaymentDate'] ?></td>
                        <td><?= $expense['Description'] ?></td>
                        <td><?= $expense['AuthorizationNumber'] ?></td>
                        <td><a href="uploads/<?= $expense['File'] ?>" target="_blank">عرض الملف</a></td>
                        <td><?= $expense['TransferType'] ?></td>
                        <td><?= $expense['Notes'] ?></td>
                    </tr>
                <?php } ?>
            </tbody>
        </table>
    </div>
</div>

<?php include 'footer.php'; ?>
</body>
</html>
