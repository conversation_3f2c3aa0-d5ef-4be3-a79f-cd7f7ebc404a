<?php
require 'db.php';

if (isset($_GET['query'])) {
    $search = "%" . $_GET['query'] . "%";
    $stmt = $conn->prepare("SELECT ID, Name FROM Customers WHERE Name LIKE ?");
    $stmt->execute([$search]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($results as $customer) {
        echo "<li class='list-group-item customer-item' data-id='{$customer['ID']}'>{$customer['Name']}</li>";
    }
}
?>
