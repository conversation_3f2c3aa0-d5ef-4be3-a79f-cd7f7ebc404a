<?php
session_start();
require 'db.php'; // الاتصال بقاعدة البيانات

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: login.php");
    exit;
}

// التحقق من وجود ID الصرفية في الرابط
if (!isset($_GET['id'])) {
    echo "<script>alert('رقم الصرفية غير موجود.'); window.location.href = 'search_exp.php';</script>";
    exit;
}

$id = $_GET['id'];

// جلب بيانات الصرفية الحالية مع اسم العميل
$stmt = $conn->prepare("SELECT e.*, c.Name AS CustomerName FROM Expenses e
                        JOIN Customers c ON e.CustomerID = c.ID
                        WHERE e.ID = :id");
$stmt->execute([':id' => $id]);
$expense = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$expense) {
    echo "<script>alert('الصرفية المطلوبة غير موجودة.'); window.location.href = 'search_exp.php';</script>";
    exit;
}

// جلب قائمة العملاء للاختيار منها
$customersStmt = $conn->query("SELECT ID, Name FROM Customers ORDER BY Name");
$customers = $customersStmt->fetchAll(PDO::FETCH_ASSOC);

// تحديث بيانات الصرفية
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $customerID = $_POST['customer_id'];
    $authorizationNumber = $_POST['authorization_number'];
    $accountNumber = $_POST['account_number'];
    $amount = $_POST['amount'];
    $tax = $_POST['tax'];
    $date = $_POST['date'];
    $paymentDate = $_POST['payment_date'];
    $transferNumber = $_POST['transfer_number'];
    $transferType = $_POST['transfer_type'];
    $expenseNumber = $_POST['expense_number'];
    $description = $_POST['description'];
    $notes = $_POST['notes'];

    // تحديث الملف في حالة رفع ملف جديد
    $filePath = $expense['File'];
    if (!empty($_FILES['file']['name'])) {
        $fileName = time() . '_' . basename($_FILES['file']['name']);
        $targetDir = "uploads/";
        $filePath = "{$targetDir}{$fileName}";

        // إنشاء مجلد uploads إذا لم يكن موجوداً
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0777, true);
        }

        if (move_uploaded_file($_FILES['file']['tmp_name'], $filePath)) {
            // حذف الملف القديم إذا كان موجوداً
            if (!empty($expense['File']) && file_exists($expense['File'])) {
                unlink($expense['File']);
            }
        } else {
            $filePath = $expense['File']; // الاحتفاظ بالملف القديم في حالة فشل الرفع
        }
    }

    $updateStmt = $conn->prepare("
        UPDATE Expenses
        SET CustomerID = :customerID, AuthorizationNumber = :authorizationNumber, AccountNumber = :accountNumber,
            Amount = :amount, Tax = :tax, Date = :date, PaymentDate = :paymentDate,
            TransferNumber = :transferNumber, TransferType = :transferType,
            ExpenseNumber = :expenseNumber, File = :filePath, Description = :description, Notes = :notes
        WHERE ID = :id
    ");
    $result = $updateStmt->execute([
        ':customerID' => $customerID,
        ':authorizationNumber' => $authorizationNumber,
        ':accountNumber' => $accountNumber,
        ':amount' => $amount,
        ':tax' => $tax,
        ':date' => $date,
        ':paymentDate' => $paymentDate,
        ':transferNumber' => $transferNumber,
        ':transferType' => $transferType,
        ':expenseNumber' => $expenseNumber,
        ':filePath' => $filePath,
        ':description' => $description,
        ':notes' => $notes,
        ':id' => $id,
    ]);

    if ($result) {
        echo "<script>alert('تم تحديث بيانات الصرفية بنجاح.'); window.location.href = 'search_exp.php';</script>";
    } else {
        echo "<script>alert('حدث خطأ أثناء التحديث.');</script>";
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الصرفية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        .edit-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .edit-card .card-header {
            background: transparent;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            color: white;
        }
        .edit-card .card-body {
            background: rgba(255,255,255,0.95);
            border-radius: 0 0 15px 15px;
        }
    </style>
</head>
<body>
<?php include 'header.php'; ?>

<div class="container mt-4">
    <div class="card edit-card">
        <div class="card-header">
            <h4 class="mb-0">
                <i class="fas fa-edit me-2"></i>تعديل الصرفية رقم: <?= htmlspecialchars($expense['ExpenseNumber']) ?>
            </h4>
            <small>العميل: <?= htmlspecialchars($expense['CustomerName']) ?></small>
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="customer_id" class="form-label"><i class="fas fa-user me-1"></i>العميل</label>
                        <select name="customer_id" id="customer_id" class="form-select" required>
                            <?php foreach ($customers as $customer) { ?>
                                <option value="<?= $customer['ID'] ?>" <?= $customer['ID'] == $expense['CustomerID'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($customer['Name']) ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="expense_number" class="form-label"><i class="fas fa-hashtag me-1"></i>رقم الصرفية</label>
                        <input type="text" name="expense_number" id="expense_number" class="form-control"
                               value="<?= htmlspecialchars($expense['ExpenseNumber']) ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="authorization_number" class="form-label"><i class="fas fa-file-alt me-1"></i>رقم الإذن</label>
                        <input type="text" name="authorization_number" id="authorization_number" class="form-control"
                               value="<?= htmlspecialchars($expense['AuthorizationNumber']) ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="account_number" class="form-label"><i class="fas fa-credit-card me-1"></i>رقم الحساب</label>
                        <input type="text" name="account_number" id="account_number" class="form-control"
                               value="<?= htmlspecialchars($expense['AccountNumber']) ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="amount" class="form-label"><i class="fas fa-money-bill-wave me-1"></i>المبلغ</label>
                        <input type="number" step="0.01" name="amount" id="amount" class="form-control"
                               value="<?= htmlspecialchars($expense['Amount']) ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="tax" class="form-label"><i class="fas fa-percentage me-1"></i>الضريبة</label>
                        <input type="number" step="0.01" name="tax" id="tax" class="form-control"
                               value="<?= htmlspecialchars($expense['Tax']) ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label"><i class="fas fa-calculator me-1"></i>المبلغ الصافي</label>
                        <input type="text" class="form-control" id="net_amount" readonly
                               value="<?= number_format($expense['NetAmount'], 2) ?>">
                    </div>
                    <div class="col-md-6">
                        <label for="date" class="form-label"><i class="fas fa-calendar-alt me-1"></i>تاريخ الصرف</label>
                        <input type="date" name="date" id="date" class="form-control"
                               value="<?= htmlspecialchars($expense['Date']) ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="payment_date" class="form-label"><i class="fas fa-calendar-check me-1"></i>تاريخ الدفع</label>
                        <input type="date" name="payment_date" id="payment_date" class="form-control"
                               value="<?= htmlspecialchars($expense['PaymentDate']) ?>">
                    </div>
                    <div class="col-md-6">
                        <label for="transfer_number" class="form-label"><i class="fas fa-exchange-alt me-1"></i>رقم التحويل</label>
                        <input type="text" name="transfer_number" id="transfer_number" class="form-control"
                               value="<?= htmlspecialchars($expense['TransferNumber']) ?>">
                    </div>
                    <div class="col-md-6">
                        <label for="transfer_type" class="form-label"><i class="fas fa-tags me-1"></i>نوع التحويل</label>
                        <select name="transfer_type" id="transfer_type" class="form-select">
                            <option value="Swift" <?= $expense['TransferType'] == 'Swift' ? 'selected' : '' ?>>Swift</option>
                            <option value="Internal" <?= $expense['TransferType'] == 'Internal' ? 'selected' : '' ?>>Internal</option>
                            <option value="Letter" <?= $expense['TransferType'] == 'Letter' ? 'selected' : '' ?>>Letter</option>
                        </select>
                    </div>
                    <div class="col-md-12">
                        <label for="description" class="form-label"><i class="fas fa-align-left me-1"></i>الوصف</label>
                        <textarea name="description" id="description" class="form-control" rows="2"><?= htmlspecialchars($expense['Description'] ?? '') ?></textarea>
                    </div>
                    <div class="col-md-12">
                        <label for="notes" class="form-label"><i class="fas fa-sticky-note me-1"></i>ملاحظات</label>
                        <textarea name="notes" id="notes" class="form-control" rows="2"><?= htmlspecialchars($expense['Notes'] ?? '') ?></textarea>
                    </div>
                    <div class="col-md-12">
                        <label for="file" class="form-label"><i class="fas fa-file-upload me-1"></i>الملف</label>
                        <input type="file" name="file" id="file" class="form-control">
                        <?php if (!empty($expense['File'])) { ?>
                            <small class="text-muted">
                                الملف الحالي: <a href="<?= htmlspecialchars($expense['File']) ?>" target="_blank">عرض الملف</a>
                            </small>
                        <?php } ?>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <button type="submit" class="btn btn-success btn-lg me-2">
                            <i class="fas fa-save me-2"></i>حفظ التعديلات
                        </button>
                        <a href="search_exp.php" class="btn btn-secondary btn-lg">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div><script>
// حساب المبلغ الصافي تلقائياً
function calculateNetAmount() {
    const amount = parseFloat(document.getElementById('amount').value) || 0;
    const tax = parseFloat(document.getElementById('tax').value) || 0;
    const netAmount = amount - tax;
    document.getElementById('net_amount').value = netAmount.toFixed(2);
}

document.getElementById('amount').addEventListener('input', calculateNetAmount);
document.getElementById('tax').addEventListener('input', calculateNetAmount);

// التحقق من صحة النموذج
document.querySelector('form').addEventListener('submit', function(e) {
    const amount = parseFloat(document.getElementById('amount').value);
    const tax = parseFloat(document.getElementById('tax').value);

    if (tax > amount) {
        e.preventDefault();
        alert('الضريبة لا يمكن أن تكون أكبر من المبلغ الأساسي');
        return false;
    }
});
</script>
</body>
</html>
