<?php
// اختبار الصفحات الحقيقية في الموقع
session_start();
require_once 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

// قائمة الصفحات الحقيقية للاختبار
$real_pages = [
    [
        'name' => 'الصفحة الرئيسية',
        'url' => 'index.php',
        'icon' => 'fas fa-home',
        'description' => 'الصفحة الرئيسية للنظام مع المهام اليومية والموازنات',
        'features' => ['المهام اليومية', 'إحصائيات الموازنة', 'روابط سريعة']
    ],
    [
        'name' => 'لوحة التقارير',
        'url' => 'dashboard.php',
        'icon' => 'fas fa-chart-line',
        'description' => 'لوحة التحكم والتقارير المالية',
        'features' => ['الرسوم البيانية', 'الإحصائيات', 'فلترة البيانات']
    ],
    [
        'name' => 'بحث العملاء',
        'url' => 'search.php',
        'icon' => 'fas fa-search',
        'description' => 'البحث في قاعدة بيانات العملاء',
        'features' => ['البحث المتقدم', 'جداول تفاعلية', 'تصدير البيانات']
    ],
    [
        'name' => 'تسجيل عميل جديد',
        'url' => 'register.php',
        'icon' => 'fas fa-user-plus',
        'description' => 'إضافة عميل جديد إلى النظام',
        'features' => ['نموذج تفاعلي', 'حساب الضرائب', 'رفع الملفات']
    ],
    [
        'name' => 'رفع ملف العملاء',
        'url' => 'customer_upload.php',
        'icon' => 'fas fa-upload',
        'description' => 'رفع ملف Excel للعملاء',
        'features' => ['رفع ملفات Excel', 'معاينة البيانات', 'استيراد جماعي']
    ],
    [
        'name' => 'إدارة الصرفيات',
        'url' => 'Expense_Management.php',
        'icon' => 'fas fa-money-bill-wave',
        'description' => 'إدارة الصرفيات والمصروفات',
        'features' => ['إضافة صرفيات', 'تتبع المصروفات', 'تصنيف الصرفيات']
    ],
    [
        'name' => 'بحث الصرفيات',
        'url' => 'expense_search.php',
        'icon' => 'fas fa-receipt',
        'description' => 'البحث في الصرفيات والمصروفات',
        'features' => ['بحث متقدم', 'فلترة حسب التاريخ', 'تقارير مفصلة']
    ],
    [
        'name' => 'إدارة الموازنات',
        'url' => 'Budget_Management.php',
        'icon' => 'fas fa-wallet',
        'description' => 'إدارة الموازنات والأرصدة',
        'features' => ['تتبع الموازنة', 'إضافة أرصدة', 'تقارير مالية']
    ],
    [
        'name' => 'إضافة موازنة',
        'url' => 'add_budget.php',
        'icon' => 'fas fa-plus-circle',
        'description' => 'إضافة موازنة جديدة',
        'features' => ['إضافة رصيد', 'تحديد الفئة', 'إرفاق مستندات']
    ],
    [
        'name' => 'إدارة المهام',
        'url' => 'Add_Notification2.php',
        'icon' => 'fas fa-tasks',
        'description' => 'إدارة المهام والتنبيهات',
        'features' => ['إضافة مهام', 'تتبع التقدم', 'تنبيهات يومية']
    ],
    [
        'name' => 'إدارة المستخدمين',
        'url' => 'manage_users.php',
        'icon' => 'fas fa-users-cog',
        'description' => 'إدارة المستخدمين والصلاحيات',
        'features' => ['إضافة مستخدمين', 'تحديد الأدوار', 'إدارة الصلاحيات']
    ],
    [
        'name' => 'الملف الشخصي',
        'url' => 'profile.php',
        'icon' => 'fas fa-user-circle',
        'description' => 'إدارة الملف الشخصي للمستخدم',
        'features' => ['تحديث البيانات', 'تغيير كلمة المرور', 'رفع صورة شخصية']
    ]
];

// اختبار حالة الصفحات
$page_status = [];
foreach ($real_pages as $page) {
    $file_path = __DIR__ . '/' . $page['url'];
    $page_status[$page['url']] = [
        'exists' => file_exists($file_path),
        'readable' => file_exists($file_path) && is_readable($file_path),
        'size' => file_exists($file_path) ? filesize($file_path) : 0
    ];
}
?>

<?php include 'header.php'; ?>

<div class="container mt-4">
    <div class="page-header">
        <h1 class="text-center">
            <i class="fas fa-vial me-3"></i>
            اختبار الصفحات الحقيقية
        </h1>
        <p class="text-center text-muted">
            اختبار شامل لجميع صفحات النظام الحقيقية والتأكد من عمل الفوتر الموحد
        </p>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card text-center">
                <div class="stat-icon">
                    <i class="fas fa-file-code text-primary"></i>
                </div>
                <div class="stat-number"><?= count($real_pages) ?></div>
                <div class="stat-label">إجمالي الصفحات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card text-center">
                <div class="stat-icon">
                    <i class="fas fa-check-circle text-success"></i>
                </div>
                <div class="stat-number"><?= count(array_filter($page_status, function($status) { return $status['exists']; })) ?></div>
                <div class="stat-label">صفحات موجودة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card text-center">
                <div class="stat-icon">
                    <i class="fas fa-eye text-info"></i>
                </div>
                <div class="stat-number"><?= count(array_filter($page_status, function($status) { return $status['readable']; })) ?></div>
                <div class="stat-label">صفحات قابلة للقراءة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card text-center">
                <div class="stat-icon">
                    <i class="fas fa-weight text-warning"></i>
                </div>
                <div class="stat-number"><?= round(array_sum(array_column($page_status, 'size')) / 1024, 1) ?></div>
                <div class="stat-label">حجم الملفات (KB)</div>
            </div>
        </div>
    </div>

    <!-- قائمة الصفحات للاختبار -->
    <div class="row">
        <?php foreach ($real_pages as $index => $page): ?>
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h6 class="card-title mb-0">
                            <i class="<?= $page['icon'] ?> me-2"></i>
                            <?= $page['name'] ?>
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="card-text small text-muted mb-3">
                            <?= $page['description'] ?>
                        </p>
                        
                        <!-- حالة الملف -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="small">حالة الملف:</span>
                                <?php if ($page_status[$page['url']]['exists']): ?>
                                    <span class="badge bg-success">موجود</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير موجود</span>
                                <?php endif; ?>
                            </div>
                            <?php if ($page_status[$page['url']]['exists']): ?>
                                <div class="small text-muted">
                                    الحجم: <?= round($page_status[$page['url']]['size'] / 1024, 1) ?> KB
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- الميزات -->
                        <div class="mb-3">
                            <h6 class="small fw-bold">الميزات:</h6>
                            <ul class="list-unstyled small">
                                <?php foreach ($page['features'] as $feature): ?>
                                    <li><i class="fas fa-check text-success me-1"></i><?= $feature ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                    <div class="card-footer">
                        <?php if ($page_status[$page['url']]['exists']): ?>
                            <a href="<?= $page['url'] ?>" class="btn btn-primary btn-sm w-100" target="_blank">
                                <i class="fas fa-external-link-alt me-1"></i>
                                اختبار الصفحة
                            </a>
                        <?php else: ?>
                            <button class="btn btn-secondary btn-sm w-100" disabled>
                                <i class="fas fa-times me-1"></i>
                                الصفحة غير متاحة
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- تعليمات الاختبار -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-info-circle me-2"></i>تعليمات الاختبار</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">اختبار الفوتر الموحد:</h6>
                            <ol class="small">
                                <li>انقر على "اختبار الصفحة" لكل صفحة</li>
                                <li>تحقق من وجود الفوتر في أسفل كل صفحة</li>
                                <li>تأكد من أن الفوتر يحتوي على الروابط الصحيحة</li>
                                <li>اختبر الروابط السريعة في الفوتر</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">اختبار الوظائف:</h6>
                            <ol class="small">
                                <li>تأكد من عمل القائمة الجانبية</li>
                                <li>اختبر النماذج والبحث</li>
                                <li>تحقق من التصميم المتجاوب</li>
                                <li>اختبر على متصفحات مختلفة</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'footer.php'; ?>

<script>
// إضافة JavaScript لتتبع الاختبارات
document.addEventListener('DOMContentLoaded', function() {
    // تتبع النقرات على روابط الاختبار
    const testLinks = document.querySelectorAll('a[href$=".php"]');
    testLinks.forEach(link => {
        link.addEventListener('click', function() {
            const pageName = this.closest('.card').querySelector('.card-title').textContent.trim();
            console.log('اختبار الصفحة:', pageName);
            
            // إضافة مؤشر بصري
            const button = this;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاختبار...';
            button.classList.add('disabled');
            
            // إعادة تعيين الزر بعد ثانيتين
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('disabled');
            }, 2000);
        });
    });
    
    // إضافة معلومات إضافية عن الفوتر
    console.log('تم تحميل صفحة اختبار الصفحات الحقيقية');
    console.log('الفوتر الموحد يجب أن يظهر في أسفل هذه الصفحة');
});
</script>
