<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لعبة XO متعددة اللاعبين</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            background-color: #f0f0f0;
            margin: 0;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        #game-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 20px;
        }
        #board {
            display: grid;
            grid-template-columns: repeat(3, 100px);
            grid-template-rows: repeat(3, 100px);
            gap: 5px;
            margin: 20px auto;
        }
        .cell {
            width: 100px;
            height: 100px;
            background-color: #fff;
            border: 2px solid #333;
            font-size: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .cell:hover {
            background-color: #e0e0e0;
        }
        .player-x {
            color: #f44336;
        }
        .player-o {
            color: #2196f3;
        }
        .status {
            font-size: 24px;
            margin: 20px 0;
            font-weight: bold;
        }
        .game-info {
            background-color: #fff;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            max-width: 400px;
        }
        .room-info {
            background-color: #e8f5e9;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        input {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            width: 200px;
        }
        .center {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .players-list {
            text-align: right;
            margin: 10px 0;
        }
        #chat-container {
            margin-top: 20px;
            width: 100%;
            max-width: 400px;
        }
        #chat-box {
            height: 200px;
            border: 1px solid #ddd;
            overflow-y: auto;
            padding: 10px;
            background-color: white;
            text-align: right;
            margin-bottom: 10px;
        }
        .chat-input {
            display: flex;
            width: 100%;
        }
        #chat-input {
            flex-grow: 1;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 5px;
        }
        .system {
            background-color: #fffde7;
            text-align: center;
            font-style: italic;
        }
        .player {
            background-color: #f5f5f5;
        }
        .my-message {
            background-color: #e1f5fe;
            text-align: left;
        }
        .other-message {
            background-color: #f5f5f5;
            text-align: right;
        }
        .connection-status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .connected {
            background-color: #e8f5e9;
            color: #2e7d32;
        }
        .disconnected {
            background-color: #ffebee;
            color: #c62828;
        }
    </style>
</head>
<body>
    <h1>لعبة XO متعددة اللاعبين</h1>
    
    <div id="connection-status" class="connection-status disconnected">غير متصل بالخادم</div>
    
    <div id="game-container">
        <div id="join-game" class="center">
            <div class="game-info">
                <h2>انضم إلى لعبة</h2>
                <div>
                    <label for="player-name">اسم اللاعب:</label>
                    <input type="text" id="player-name" placeholder="أدخل اسمك">
                </div>
                <div>
                    <label for="room-id">رقم الغرفة:</label>
                    <input type="text" id="room-id" placeholder="أدخل رقم الغرفة">
                </div>
                <div>
                    <button id="create-btn">إنشاء غرفة جديدة</button>
                    <button id="join-btn">انضم إلى غرفة</button>
                </div>
            </div>
        </div>
        
        <div id="game-board" style="display: none;">
            <div class="game-info">
                <div class="room-info" id="room-display"></div>
                <div class="players-list">
                    <div id="player-x">X: انتظار اللاعب...</div>
                    <div id="player-o">O: انتظار اللاعب...</div>
                </div>
                <div class="status" id="status">انتظار انضمام اللاعبين...</div>
            </div>
            
            <div id="board"></div>
            
            <button id="restart-btn">إعادة اللعب</button>
            <button id="leave-btn">مغادرة اللعبة</button>
            
            <div id="chat-container">
                <h3>الدردشة</h3>
                <div id="chat-box"></div>
                <div class="chat-input">
                    <input type="text" id="chat-input" placeholder="اكتب رسالة...">
                    <button id="send-btn">إرسال</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.5.1/socket.io.min.js"></script>
    <script>
        // اتصال Socket.io بالخادم
        const socket = io(); // للاتصال بنفس الخادم، أو حدد عنوان الخادم: io('http://your-server:3000');
        
        // عناصر واجهة المستخدم
        const connectionStatus = document.getElementById('connection-status');
        const joinGameDiv = document.getElementById('join-game');
        const gameBoard = document.getElementById('game-board');
        const boardElement = document.getElementById('board');
        const statusElement = document.getElementById('status');
        const playerXDisplay = document.getElementById('player-x');
        const playerODisplay = document.getElementById('player-o');
        const roomDisplay = document.getElementById('room-display');
        const chatBox = document.getElementById('chat-box');
        const chatInput = document.getElementById('chat-input');
        
        // أزرار وحقول الإدخال
        const playerNameInput = document.getElementById('player-name');
        const roomIdInput = document.getElementById('room-id');
        const createBtn = document.getElementById('create-btn');
        const joinBtn = document.getElementById('join-btn');
        const restartBtn = document.getElementById('restart-btn');
        const leaveBtn = document.getElementById('leave-btn');
        const sendBtn = document.getElementById('send-btn');
        
        // معلومات اللعبة
        let playerSymbol = '';
        let roomId = '';
        
        // إعداد لوحة اللعب
        function initializeBoard() {
            boardElement.innerHTML = '';
            for (let i = 0; i < 9; i++) {
                const cell = document.createElement('div');
                cell.classList.add('cell');
                cell.dataset.index = i;
                cell.addEventListener('click', () => handleCellClick(i));
                boardElement.appendChild(cell);
            }
        }
        
        // تحديث لوحة اللعب
        function renderBoard(board) {
            const cells = document.querySelectorAll('.cell');
            cells.forEach((cell, index) => {
                cell.textContent = board[index];
                cell.classList.remove('player-x', 'player-o');
                if (board[index] === 'X') {
                    cell.classList.add('player-x');
                } else if (board[index] === 'O') {
                    cell.classList.add('player-o');
                }
            });
        }
        
        // التعامل مع نقرة الخلية
        function handleCellClick(index) {
            socket.emit('makeMove', { index });
        }
        
        // إظهار شاشة الانضمام
        function showJoinScreen() {
            joinGameDiv.style.display = 'flex';
            gameBoard.style.display = 'none';
        }
        
        // إظهار لوحة اللعب
        function showGameBoard() {
            joinGameDiv.style.display = 'none';
            gameBoard.style.display = 'block';
        }
        
        // إضافة رسالة للدردشة
        function addChatMessage(message, type) {
            const msgElement = document.createElement('div');
            msgElement.classList.add('message', type);
            
            if (type === 'system') {
                msgElement.textContent = message;
            } else if (type === 'player') {
                if (message.senderId === socket.id) {
                    msgElement.classList.add('my-message');
                } else {
                    msgElement.classList.add('other-message');
                }
                msgElement.textContent = `${message.sender}: ${message.text}`;
            }
            
            chatBox.appendChild(msgElement);
            chatBox.scrollTop = chatBox.scrollHeight;
        }
        
        // إعداد أحداث الأزرار
        createBtn.addEventListener('click', () => {
            const playerName = playerNameInput.value.trim();
            if (playerName) {
                socket.emit('createRoom', { playerName }, (response) => {
                    if (response.success) {
                        roomId = response.roomId;
                        roomDisplay.textContent = `رقم الغرفة: ${roomId}`;
                        showGameBoard();
                        addChatMessage('تم إنشاء الغرفة. شارك رقم الغرفة مع صديقك للانضمام.', 'system');
                    }
                });
            } else {
                alert('الرجاء إدخال اسم اللاعب');
            }
        });
        
        joinBtn.addEventListener('click', () => {
            const playerName = playerNameInput.value.trim();
            const joinRoomId = roomIdInput.value.trim();
            
            if (playerName && joinRoomId) {
                socket.emit('joinRoom', { playerName, roomId: joinRoomId }, (response) => {
                    if (response.success) {
                        roomId = joinRoomId;
                        roomDisplay.textContent = `رقم الغرفة: ${roomId}`;
                        showGameBoard();
                    } else {
                        alert(response.message || 'فشل الانضمام إلى الغرفة');
                    }
                });
            } else {
                alert('الرجاء إدخال اسم اللاعب ورقم الغرفة');
            }
        });
        
        restartBtn.addEventListener('click', () => {
            socket.emit('restartGame');
        });
        
        leaveBtn.addEventListener('click', () => {
            socket.emit('leaveGame');
            showJoinScreen();
        });
        
        sendBtn.addEventListener('click', () => {
            const message = chatInput.value.trim();
            if (message) {
                socket.emit('sendMessage', { message });
                chatInput.value = '';
            }
        });
        
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendBtn.click();
            }
        });
        
        // أحداث Socket.io
        socket.on('connect', () => {
            connectionStatus.textContent = 'متصل بالخادم';
            connectionStatus.classList.replace('disconnected', 'connected');
        });
        
        socket.on('disconnect', () => {
            connectionStatus.textContent = 'انقطع الاتصال بالخادم';
            connectionStatus.classList.replace('connected', 'disconnected');
        });
        
        socket.on('gameState', (data) => {
            // تحديث لوحة اللعب
            renderBoard(data.board);
            
            // تحديث معلومات اللاعبين
            playerXDisplay.textContent = data.players['X'] ? `X: ${data.players['X']}` : 'X: انتظار اللاعب...';
            playerODisplay.textContent = data.players['O'] ? `O: ${data.players['O']}` : 'O: انتظار اللاعب...';
            
            // تحديث حالة اللعبة
            if (!data.gameActive) {
                if (data.result && data.result.gameOver) {
                    if (data.result.winner) {
                        statusElement.textContent = `الفائز: ${data.result.winner} (${data.players[data.result.winner]})`;
                    } else {
                        statusElement.textContent = 'تعادل!';
                    }
                } else {
                    statusElement.textContent = 'انتظار انضمام اللاعبين...';
                }
            } else {
                statusElement.textContent = `دور اللاعب: ${data.currentPlayer} (${data.players[data.currentPlayer]})`;
            }
        });
        
        socket.on('newMessage', (message) => {
            if (message.type === 'system') {
                addChatMessage(message.text, 'system');
            } else {
                addChatMessage(message, 'player');
            }
        });
        
        // تهيئة اللعبة
        initializeBoard();
        showJoinScreen();
    </script>
</body>
</html>