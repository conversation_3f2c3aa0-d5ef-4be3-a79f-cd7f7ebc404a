/* ===== تحسينات إضافية لصفحة إدارة التنبيهات ===== */

/* تحسين الخطوط والألوان العامة */
.notifications-container * {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تحسين الأيقونات */
.fas, .far {
    margin-left: 8px;
}

/* تحسين الجدول للأجهزة الكبيرة */
@media (min-width: 1200px) {
    .notifications-table .table {
        font-size: 1rem;
    }
    
    .notifications-table .table th,
    .notifications-table .table td {
        padding: 20px 15px;
    }
    
    .action-buttons .btn {
        min-width: 160px;
        padding: 10px 15px;
    }
}

/* تحسين الأنيميشن */
.notifications-table .table tbody tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notifications-table .table tbody tr:hover {
    transform: translateY(-3px) scale(1.01);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

/* تحسين الشارات */
.status-badge {
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-badge:hover {
    transform: scale(1.05);
}

/* تحسين الأزرار */
.btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* تحسين النموذج */
.add-notification-form {
    position: relative;
    overflow: hidden;
}

.add-notification-form::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 123, 255, 0.05) 0%, transparent 70%);
    animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

/* تحسين التنقل */
.month-navigation {
    position: relative;
    overflow: hidden;
}

.month-navigation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 123, 255, 0.05), rgba(0, 123, 255, 0.1));
    z-index: -1;
}

/* تحسين الفلتر */
.status-filter {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
}

/* تحسين رسائل التنبيه */
.alert-success {
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* تحسين زر العودة لأعلى */
#scrollBtn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

#scrollBtn:hover {
    background: linear-gradient(135deg, #0056b3, #007bff);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

/* تحسين الاستجابة للأجهزة الصغيرة */
@media (max-width: 576px) {
    .page-header h4 {
        font-size: 1.4rem;
    }
    
    .page-header .subtitle {
        font-size: 0.85rem;
    }
    
    .add-notification-form {
        padding: 15px;
    }
    
    .month-navigation {
        padding: 15px;
    }
    
    .month-navigation h5 {
        font-size: 1.1rem;
    }
    
    .nav-btn {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
    
    .notifications-table .table th,
    .notifications-table .table td {
        padding: 8px 5px;
        font-size: 0.8rem;
    }
    
    .action-buttons .btn {
        min-width: 100px;
        padding: 5px 8px;
        font-size: 0.75rem;
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 4px 8px;
    }
}

/* تحسين الطباعة */
@media print {
    .add-notification-form,
    .month-navigation,
    .status-filter,
    .action-buttons,
    #scrollBtn {
        display: none !important;
    }
    
    .notifications-table {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .notifications-table .table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .page-header {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
}

/* تحسين إمكانية الوصول */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* تحسين التباين للنصوص */
.notifications-table .table td {
    color: #212529;
}

.status-completed {
    color: #155724 !important;
}

.status-in-progress {
    color: #0c5460 !important;
}

.status-not-started {
    color: #721c24 !important;
}

/* تحسين التمرير السلس */
html {
    scroll-behavior: smooth;
}

/* تحسين التحميل */
.notifications-container {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسين التفاعل مع الماوس */
.table tbody tr {
    cursor: pointer;
}

.btn {
    cursor: pointer;
}

.btn:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

/* تحسين الألوان للوضع المظلم (إذا كان مطلوباً لاحقاً) */
@media (prefers-color-scheme: dark) {
    .notifications-container {
        color-scheme: light; /* فرض الوضع الفاتح */
    }
}
