<?php
session_start();
require 'db.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo "<script>alert('ليس لديك صلاحية الوصول إلى هذه الصفحة.'); window.location.href = '/e-finance/index';</script>";
    exit;
}



// إضافة ميزات الفلترة
$conditions = [];
$params = [];

if (!empty($_GET['from_date'])) {
    $conditions[] = "e.Date >= :from_date";
    $params[':from_date'] = $_GET['from_date'];
}
if (!empty($_GET['to_date'])) {
    $conditions[] = "e.Date <= :to_date";
    $params[':to_date'] = $_GET['to_date'];
}
if (!empty($_GET['customer_search'])) {
    $conditions[] = "c.Name LIKE :customer_search";
    $params[':customer_search'] = "%" . $_GET['customer_search'] . "%";
}

$where = $conditions ? "WHERE " . implode(" AND ", $conditions) : "";

$sql = "SELECT e.*, c.Name AS CustomerName, b.Title AS BudgetTitle 
        FROM Expenses e 
        JOIN Customers c ON e.CustomerID = c.ID 
        JOIN Budgets b ON e.BudgetID = b.ID 
        $where
        ORDER BY e.CreatedAt DESC";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);

$budgets = $conn->query("SELECT ID, Title, TotalAmount, SpentAmount FROM Budgets")->fetchAll(PDO::FETCH_ASSOC);
$customers = $conn->query("SELECT ID, Name FROM Customers")->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // بدء المعاملة
        $conn->beginTransaction();

        $customerID = $_POST['customer_id'];
        $budgetID = $_POST['budget_id'];
        $categoryID = $_POST['category_id'] ?? null;
        $amount = floatval($_POST['amount']);
        $tax = floatval($_POST['tax'] ?? 0);
        $netAmount = $amount - $tax;

        // التحقق من صحة البيانات
        if (!$customerID || !$budgetID || $amount <= 0) {
            throw new Exception("الرجاء ملء جميع الحقول المطلوبة بشكل صحيح.");
        }

        // التحقق من الموازنة
        $budgetStmt = $conn->prepare("SELECT TotalAmount, SpentAmount FROM Budgets WHERE ID = ?");
        $budgetStmt->execute([$budgetID]);
        $budget = $budgetStmt->fetch(PDO::FETCH_ASSOC);

        if (!$budget) {
            throw new Exception("الموازنة المحددة غير موجودة.");
        }

        $budgetRemaining = $budget['TotalAmount'] - $budget['SpentAmount'];

        if ($netAmount > $budgetRemaining) {
            throw new Exception("المبلغ الصافي ({$netAmount} جنيه) يتجاوز الموازنة المتاحة ({$budgetRemaining} جنيه).");
        }

        // التحقق من البند إذا تم تحديده
        if ($categoryID) {
            $categoryStmt = $conn->prepare("SELECT Amount, SpentAmount FROM Categories WHERE ID = ? AND BudgetID = ?");
            $categoryStmt->execute([$categoryID, $budgetID]);
            $category = $categoryStmt->fetch(PDO::FETCH_ASSOC);

            if (!$category) {
                throw new Exception("البند المحدد غير موجود أو لا ينتمي للموازنة المختارة.");
            }

            // التحقق من رصيد البند إذا كان له مبلغ محدد
            if ($category['Amount'] > 0) {
                $categoryRemaining = $category['Amount'] - $category['SpentAmount'];
                if ($netAmount > $categoryRemaining) {
                    throw new Exception("المبلغ الصافي ({$netAmount} جنيه) يتجاوز رصيد البند المتاح ({$categoryRemaining} جنيه).");
                }
            }
        }

        // إدراج الصرفية
        $stmt = $conn->prepare("INSERT INTO Expenses
            (CustomerID, AuthorizationNumber, AccountNumber, Amount, Tax, NetAmount, Date, PaymentDate,
            TransferNumber, TransferType, ExpenseNumber, File, Notes, CreatedAt, Description, BudgetID, CategoryID)
            VALUES
            (:customerID, :authorizationNumber, :accountNumber, :amount, :tax, :netAmount, :date, :paymentDate,
            :transferNumber, :transferType, :expenseNumber, :file, :notes, :createdAt, :description, :budgetID, :categoryID)");

        $stmt->execute([
            ':customerID' => $customerID,
            ':authorizationNumber' => $_POST['authorization_number'],
            ':accountNumber' => $_POST['account_number'],
            ':amount' => $amount,
            ':tax' => $tax,
            ':netAmount' => $netAmount,
            ':date' => $_POST['date'],
            ':paymentDate' => $_POST['payment_date'],
            ':transferNumber' => $_POST['transfer_number'],
            ':transferType' => $_POST['transfer_type'],
            ':expenseNumber' => 'EXP' . time(),
            ':file' => null,
            ':notes' => $_POST['notes'] ?? null,
            ':createdAt' => date('Y-m-d H:i:s'),
            ':description' => $_POST['description'],
            ':budgetID' => $budgetID,
            ':categoryID' => $categoryID
        ]);

        // تحديث رصيد الموازنة
        $updateBudget = $conn->prepare("UPDATE Budgets SET SpentAmount = SpentAmount + ? WHERE ID = ?");
        $updateBudget->execute([$netAmount, $budgetID]);

        // تحديث رصيد البند إذا تم تحديده
        if ($categoryID) {
            $updateCategory = $conn->prepare("UPDATE Categories SET SpentAmount = SpentAmount + ? WHERE ID = ?");
            $updateCategory->execute([$netAmount, $categoryID]);
        }

        // تأكيد المعاملة
        $conn->commit();

        $_SESSION['success'] = "تمت إضافة الصرفية بنجاح وتم خصم المبلغ من الموازنة" . ($categoryID ? " والبند" : "");
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;

    } catch (Exception $e) {
        // التراجع عن المعاملة في حالة الخطأ
        $conn->rollBack();
        $_SESSION['error'] = $e->getMessage();
    }
}
// جلب العملاء
$customersStmt = $conn->query("SELECT ID, Name FROM Customers");
$customers = $customersStmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة الصرفيات</title>
    <!-- jQuery و jQuery UI للـ Autocomplete -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }

        .form-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .stats-card {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }

        .filter-section {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .table th {
            background: linear-gradient(135deg, #343a40 0%, #495057 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px 10px;
        }

        .table td {
            padding: 12px 10px;
            vertical-align: middle;
            border-color: #dee2e6;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .btn-action {
            margin: 2px;
            border-radius: 8px;
            font-size: 0.85rem;
            padding: 5px 10px;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        .customer-list {
            position: absolute;
            z-index: 1000;
            width: 100%;
            max-height: 200px;
            overflow-y: auto;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .page-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            border-radius: 0 0 20px 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
<?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?= $_SESSION['success']; unset($_SESSION['success']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?= $_SESSION['error']; unset($_SESSION['error']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php include 'header.php'; ?>

<!-- عنوان الصفحة -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-1"><i class="fas fa-money-bill-wave me-2"></i>إدارة الصرفيات</h2>
                <p class="mb-0 opacity-75">إضافة وإدارة الصرفيات والمدفوعات</p>
            </div>
            <div class="col-md-4">
                <?php
                $totalExpenses = count($expenses);
                $totalAmount = array_sum(array_column($expenses, 'Amount'));
                $totalTax = array_sum(array_column($expenses, 'Tax'));
                $totalNet = array_sum(array_column($expenses, 'NetAmount'));
                ?>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="stat-value"><?= $totalExpenses ?></div>
                        <div class="stat-label">إجمالي الصرفيات</div>
                    </div>
                    <div class="col-6">
                        <div class="stat-value"><?= number_format($totalNet, 0) ?></div>
                        <div class="stat-label">إجمالي المبلغ</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container mt-4">

    <!-- نموذج إضافة صرفية جديدة -->
    <div class="form-section">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4 class="mb-0"><i class="fas fa-plus-circle me-2 text-primary"></i>إضافة صرفية جديدة</h4>
            <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                <i class="fas fa-redo me-1"></i>إعادة تعيين
            </button>
        </div>

        <form method="POST" id="expenseForm">
            <div class="row g-3">
                <!-- معلومات العميل -->
                <div class="col-12">
                    <h6 class="text-muted mb-3"><i class="fas fa-user me-2"></i>معلومات العميل</h6>
                </div>
                <div class="col-md-4">
                    <label class="form-label fw-bold">اسم العميل</label>
                    <div class="position-relative">
                        <input type="text" id="customer_name" class="form-control" autocomplete="off" placeholder="ابحث عن العميل...">
                        <div id="customer_list" class="list-group customer-list" style="display: none;"></div>
                        <input type="hidden" name="customer_id" id="customer_id">
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label fw-bold">رقم الإذن</label>
                    <input type="text" name="authorization_number" id="authorization_number" class="form-control" placeholder="أدخل رقم الإذن" required>
                </div>
                <div class="col-md-4">
                    <label class="form-label fw-bold">رقم الحساب</label>
                    <input type="text" name="account_number" id="account_number" class="form-control" placeholder="أدخل رقم الحساب" required>
                </div>

                <!-- المعلومات المالية -->
                <div class="col-12 mt-4">
                    <h6 class="text-muted mb-3"><i class="fas fa-calculator me-2"></i>المعلومات المالية</h6>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">المبلغ</label>
                    <div class="input-group">
                        <input type="number" name="amount" id="amount" class="form-control" placeholder="0.00" step="0.01" min="0" required>
                        <span class="input-group-text">جنيه</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">الضريبة</label>
                    <div class="input-group">
                        <input type="number" name="tax" id="tax" class="form-control" placeholder="0.00" step="0.01" min="0" required>
                        <span class="input-group-text">جنيه</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">الصافي</label>
                    <div class="input-group">
                        <input type="number" id="net_amount" class="form-control" placeholder="0.00" readonly>
                        <span class="input-group-text">جنيه</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">التاريخ</label>
                    <input type="date" name="date" id="date" class="form-control" value="<?= date('Y-m-d') ?>" required>
                </div>

                <!-- معلومات التحويل -->
                <div class="col-12 mt-4">
                    <h6 class="text-muted mb-3"><i class="fas fa-exchange-alt me-2"></i>معلومات التحويل</h6>
                </div>
                <div class="col-md-4">
                    <label class="form-label fw-bold">تاريخ الدفع</label>
                    <input type="date" name="payment_date" id="payment_date" class="form-control" value="<?= date('Y-m-d') ?>" required>
                </div>
                <div class="col-md-4">
                    <label class="form-label fw-bold">رقم التحويل</label>
                    <input type="text" name="transfer_number" id="transfer_number" class="form-control" placeholder="أدخل رقم التحويل" required>
                </div>
                <div class="col-md-4">
                    <label class="form-label fw-bold">نوع التحويل</label>
                    <select name="transfer_type" id="transfer_type" class="form-control" required>
                        <option value="">اختر نوع التحويل</option>
                        <option value="تحويل بنكي">تحويل بنكي</option>
                        <option value="شيك">شيك</option>
                        <option value="نقدي">نقدي</option>
                        <option value="فيزا">فيزا</option>
                    </select>
                </div>

                <!-- الموازنة والبند -->
                <div class="col-12 mt-4">
                    <h6 class="text-muted mb-3"><i class="fas fa-tags me-2"></i>التصنيف</h6>
                </div>
                <div class="col-md-6">
                    <label class="form-label fw-bold">الموازنة</label>
                    <select name="budget_id" id="budget" class="form-control" required>
                        <option value="">اختر الموازنة</option>
                        <?php foreach ($budgets as $b):
                            $remaining = $b['TotalAmount'] - $b['SpentAmount'];
                        ?>
                        <option value="<?= $b['ID'] ?>" data-remaining="<?= $remaining ?>">
                            <?= $b['Title'] ?> (متبقي: <?= number_format($remaining, 0) ?> جنيه)
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label fw-bold">البند</label>
                    <select name="category_id" id="category" class="form-control" required>
                        <option value="">اختر الموازنة أولاً</option>
                    </select>
                </div>

                <!-- الوصف والملاحظات -->
                <div class="col-12 mt-4">
                    <h6 class="text-muted mb-3"><i class="fas fa-sticky-note me-2"></i>تفاصيل إضافية</h6>
                </div>
                <div class="col-md-6">
                    <label class="form-label fw-bold">الوصف</label>
                    <textarea name="description" id="description" class="form-control" rows="3" placeholder="أدخل وصف الصرفية..." required></textarea>
                </div>
                <div class="col-md-6">
                    <label class="form-label fw-bold">ملاحظات</label>
                    <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="ملاحظات إضافية (اختياري)..."></textarea>
                </div>

                <!-- أزرار التحكم -->
                <div class="col-12 mt-4 text-center">
                    <button type="submit" id="submitExpense" class="btn btn-primary btn-lg px-5">
                        <i class="fas fa-save me-2"></i>إضافة الصرفية
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-lg px-5 ms-3" onclick="resetForm()">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                </div>
            </div>
        </form>
    </div>
    </form>

    <!-- قسم الفلاتر والبحث -->
    <div class="filter-section">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0"><i class="fas fa-filter me-2 text-primary"></i>فلاتر البحث</h5>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()">
                <i class="fas fa-eraser me-1"></i>مسح الفلاتر
            </button>
        </div>

        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label fw-bold">من تاريخ</label>
                <input type="date" name="from_date" value="<?= $_GET['from_date'] ?? '' ?>" class="form-control">
            </div>
            <div class="col-md-3">
                <label class="form-label fw-bold">إلى تاريخ</label>
                <input type="date" name="to_date" value="<?= $_GET['to_date'] ?? '' ?>" class="form-control">
            </div>
            <div class="col-md-4">
                <label class="form-label fw-bold">اسم العميل</label>
                <input type="text" name="customer_search" value="<?= $_GET['customer_search'] ?? '' ?>" class="form-control" placeholder="ابحث باسم العميل...">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>

    <!-- عنوان الجدول والإحصائيات -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h4 class="mb-1"><i class="fas fa-table me-2 text-primary"></i>سجل الصرفيات</h4>
            <p class="text-muted mb-0">عدد الصرفيات: <span class="fw-bold"><?= count($expenses) ?></span> | إجمالي المبلغ: <span class="fw-bold"><?= number_format($totalNet, 2) ?> جنيه</span></p>
        </div>
        <div>
            <button id="exportBtn" class="btn btn-success">
                <i class="fas fa-file-excel me-1"></i>تصدير إلى Excel
            </button>
        </div>
    </div>

    <!-- جدول الصرفيات -->
    <div class="table-container">
        <div class="table-responsive">
            <table id="expenseTable" class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th><i class="fas fa-user me-1"></i>اسم العميل</th>
                        <th><i class="fas fa-file-alt me-1"></i>رقم الإذن</th>
                        <th><i class="fas fa-money-bill me-1"></i>المبلغ</th>
                        <th><i class="fas fa-percent me-1"></i>الضريبة</th>
                        <th><i class="fas fa-calculator me-1"></i>الصافي</th>
                        <th><i class="fas fa-calendar me-1"></i>التاريخ</th>
                        <th><i class="fas fa-wallet me-1"></i>الموازنة</th>
                        <th><i class="fas fa-exchange-alt me-1"></i>نوع التحويل</th>
                        <th><i class="fas fa-cogs me-1"></i>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($expenses)): ?>
                        <tr>
                            <td colspan="9" class="text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted mb-0">لا توجد صرفيات مسجلة</p>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($expenses as $index => $exp): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                            <?= strtoupper(substr($exp['CustomerName'], 0, 1)) ?>
                                        </div>
                                        <strong><?= htmlspecialchars($exp['CustomerName']) ?></strong>
                                    </div>
                                </td>
                                <td><span class="badge bg-info"><?= htmlspecialchars($exp['AuthorizationNumber']) ?></span></td>
                                <td><span class="fw-bold text-primary"><?= number_format($exp['Amount'], 2) ?></span></td>
                                <td><span class="text-warning"><?= number_format($exp['Tax'], 2) ?></span></td>
                                <td><span class="fw-bold text-success"><?= number_format($exp['NetAmount'], 2) ?></span></td>
                                <td><?= date('Y/m/d', strtotime($exp['Date'])) ?></td>
                                <td><span class="badge bg-secondary"><?= htmlspecialchars($exp['BudgetTitle']) ?></span></td>
                                <td><?= htmlspecialchars($exp['TransferType']) ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="edit_expense.php?id=<?= $exp['ID'] ?>" class="btn btn-warning btn-action" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="delete_expense.php?id=<?= $exp['ID'] ?>" class="btn btn-danger btn-action"
                                           onclick="return confirm('هل أنت متأكد من حذف الصرفية؟')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php include 'footer.php'; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
$(document).ready(function(){
  $('#customer_name').keyup(function(){
    var query = $(this).val();
    if (query != '') {
      $.ajax({
        url: "autocomplete.php",
        method: "GET",
        data: { query: query },
        dataType: "json",
        success: function(data) {
          let list = $('#customer_list');
          list.empty();
          if (data.length > 0) {
            data.forEach(function(customer) {
              list.append('<a href="#" class="list-group-item list-group-item-action" data-id="' + customer.ID + '">' + customer.Name + '</a>');
            });
            list.show();
          } else {
            list.hide();
          }
        }
      });
    } else {
      $('#customer_list').hide();
    }
  });

    $(document).on("click", "#customer_list a", function (e) {
        e.preventDefault();
        $("#customer_name").val($(this).text());
        $("#customer_id").val($(this).data("id"));
        $("#customer_list").hide();
        $.ajax({
    url: "get_customer.php",
    method: "GET",
    data: { id: $(this).data("id") },
    success: function (data) {
        let customer = JSON.parse(data);
        $("#account_number").val(customer.AccountNumber);
        $("#authorization_number").val(customer.AuthorizationNumber);
        $("#amount").val(customer.Amount);
        $("#tax").val(customer.Tax);
        $("#notes").val(customer.Notes);
        // يمكن إضافة المزيد حسب الحقول المتوفرة مثل:
        // $("#transfer_number").val(customer.TransferNumber);
        // $("#category").val(customer.Category);
        // $("#card_expiry").val(customer.CardExpiry); ← لو أضفته في النموذج
    }
});
    });

    $(document).click(function (e) {
        if (!$(e.target).closest("#customer_name, #customer_list").length) {
            $("#customer_list").hide();
        }
    });
});
</script>
<script>
        $(document).ready(function () {
            // تحديث البنود عند تغيير الموازنة
            $('#budget').on('change', function () {
                const budgetId = $(this).val();
                const $category = $('#category');
                $category.html('<option value="">جاري التحميل...</option>');

                if (budgetId) {
                    $.ajax({
                        url: 'get_categories.php',
                        method: 'GET',
                        data: { budget_id: budgetId },
                        dataType: 'json',
                        success: function (data) {
                            $category.empty();
                            if (data.length === 0) {
                                $category.append('<option value="">لا توجد بنود لهذه الموازنة</option>');
                            } else {
                                $category.append('<option value="">اختر البند (اختياري)</option>');
                                data.forEach(function (item) {
                                    const optionText = item.DisplayName || item.Name;
                                    const option = $('<option></option>')
                                        .attr('value', item.ID)
                                        .attr('data-remaining', item.RemainingAmount)
                                        .attr('data-amount', item.Amount)
                                        .text(optionText);
                                    $category.append(option);
                                });
                            }
                        },
                        error: function (xhr) {
                            console.error('خطأ في جلب البنود:', xhr.responseText);
                            $category.html('<option value="">فشل في تحميل البنود</option>');
                        }
                    });
                } else {
                    $category.html('<option value="">اختر الموازنة أولاً</option>');
                }
            });

            // التحقق من رصيد البند عند تغييره
            $('#category').on('change', function () {
                const netAmount = parseFloat($('#net_amount').val()) || 0;
                if (netAmount > 0) {
                    checkCategoryAvailability(netAmount);
                }
            });

            // حساب المبلغ الصافي تلقائياً
            $('#amount, #tax').on('input', function () {
                const amount = parseFloat($('#amount').val()) || 0;
                const tax = parseFloat($('#tax').val()) || 0;
                const netAmount = amount - tax;
                $('#net_amount').val(netAmount.toFixed(2));

                // التحقق من الموازنة والبند المتاحين
                checkAvailability(netAmount);
            });

            // التحقق من الموازنة والبند المتاحين
            function checkAvailability(netAmount) {
                const selectedBudget = $('#budget option:selected');
                const selectedCategory = $('#category option:selected');
                const submitBtn = $('#submitExpense');

                let isValid = true;
                let errorMessage = '';

                // التحقق من الموازنة
                if (selectedBudget.val()) {
                    const budgetRemaining = parseFloat(selectedBudget.data('remaining')) || 0;
                    if (netAmount > budgetRemaining) {
                        isValid = false;
                        errorMessage = `المبلغ يتجاوز الموازنة المتاحة (${budgetRemaining.toFixed(2)} جنيه)`;
                    }
                }

                // التحقق من البند إذا تم اختياره
                if (isValid && selectedCategory.val()) {
                    const categoryAmount = parseFloat(selectedCategory.data('amount')) || 0;
                    const categoryRemaining = parseFloat(selectedCategory.data('remaining')) || 0;

                    if (categoryAmount > 0 && netAmount > categoryRemaining) {
                        isValid = false;
                        errorMessage = `المبلغ يتجاوز رصيد البند المتاح (${categoryRemaining.toFixed(2)} جنيه)`;
                    }
                }

                // تحديث حالة الزر
                if (isValid) {
                    submitBtn.prop('disabled', false)
                        .removeClass('btn-danger')
                        .addClass('btn-primary')
                        .html('<i class="fas fa-save me-2"></i>إضافة الصرفية');
                } else {
                    submitBtn.prop('disabled', true)
                        .removeClass('btn-primary')
                        .addClass('btn-danger')
                        .html('<i class="fas fa-exclamation-triangle me-2"></i>' + errorMessage);
                }
            }

            // التحقق من رصيد البند
            function checkCategoryAvailability(netAmount) {
                checkAvailability(netAmount);
            }

            // إعادة تعيين النموذج
            window.resetForm = function() {
                $('#expenseForm')[0].reset();
                $('#customer_list').hide();
                $('#category').html('<option value="">اختر الموازنة أولاً</option>');
                $('#net_amount').val('');
                $('#submitExpense').prop('disabled', false).removeClass('btn-danger').addClass('btn-primary');
                $('#submitExpense').html('<i class="fas fa-save me-2"></i>إضافة الصرفية');
            };

            // مسح الفلاتر
            window.clearFilters = function() {
                $('input[name="from_date"]').val('');
                $('input[name="to_date"]').val('');
                $('input[name="customer_search"]').val('');
                window.location.href = window.location.pathname;
            };
        });
    </script>
</body>
</html>
