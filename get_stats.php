<?php
require_once 'db.php';
require_once 'functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

try {
    // حساب الإحصائيات
    $stats = [
        'total' => 0,
        'completed' => 0,
        'in_progress' => 0,
        'pending' => 0,
        'overdue' => 0,
        'today' => 0,
        'this_week' => 0,
        'this_month' => 0,
        'high_priority' => 0,
        'by_category' => [],
        'by_user' => [],
        'completion_rate' => 0,
        'avg_completion_time' => 0,
        'productivity_trend' => []
    ];

    $today = date('Y-m-d');
    $weekStart = date('Y-m-d', strtotime('monday this week'));
    $weekEnd = date('Y-m-d', strtotime('sunday this week'));
    $monthStart = date('Y-m-01');
    $monthEnd = date('Y-m-t');

    // جلب جميع المهام
    $statsQuery = "SELECT n.*, 
                          u1.Username as CreatedByName,
                          u2.Username as AssignedToName,
                          tc.Name as CategoryName,
                          DATEDIFF(COALESCE(n.LastUpdated, NOW()), n.CreatedAt) as CompletionDays
                   FROM Notifications n 
                   LEFT JOIN users u1 ON n.CreatedBy = u1.ID 
                   LEFT JOIN users u2 ON n.AssignedTo = u2.ID 
                   LEFT JOIN task_categories tc ON n.Category = tc.Name";
    $statsStmt = $conn->prepare($statsQuery);
    $statsStmt->execute();
    $allTasks = $statsStmt->fetchAll(PDO::FETCH_ASSOC);

    $totalCompletionDays = 0;
    $completedTasksCount = 0;

    foreach ($allTasks as $task) {
        $stats['total']++;
        
        // إحصائيات الحالة
        switch ($task['Status']) {
            case 'تم':
                $stats['completed']++;
                $completedTasksCount++;
                if ($task['CompletionDays']) {
                    $totalCompletionDays += $task['CompletionDays'];
                }
                break;
            case 'تحت التنفيذ':
                $stats['in_progress']++;
                break;
            case 'لم تنفذ':
                // التحقق من انتهاء الموعد
                if ($task['ToDate'] && $task['ToDate'] < $today) {
                    $stats['overdue']++;
                } else {
                    $stats['pending']++;
                }
                break;
        }
        
        // إحصائيات التواريخ
        if ($task['NotifyDate'] == $today || $task['ToDate'] == $today) {
            $stats['today']++;
        }
        
        if ($task['NotifyDate'] >= $weekStart && $task['NotifyDate'] <= $weekEnd) {
            $stats['this_week']++;
        }
        
        if ($task['NotifyDate'] >= $monthStart && $task['NotifyDate'] <= $monthEnd) {
            $stats['this_month']++;
        }
        
        // إحصائيات الأولوية
        if (in_array($task['Priority'], ['عالية', 'عاجلة'])) {
            $stats['high_priority']++;
        }
        
        // إحصائيات التصنيفات
        $category = $task['CategoryName'] ?? 'غير مصنف';
        if (!isset($stats['by_category'][$category])) {
            $stats['by_category'][$category] = 0;
        }
        $stats['by_category'][$category]++;
        
        // إحصائيات المستخدمين
        $assignedTo = $task['AssignedToName'] ?? 'غير معين';
        if (!isset($stats['by_user'][$assignedTo])) {
            $stats['by_user'][$assignedTo] = ['total' => 0, 'completed' => 0];
        }
        $stats['by_user'][$assignedTo]['total']++;
        if ($task['Status'] === 'تم') {
            $stats['by_user'][$assignedTo]['completed']++;
        }
    }

    // حساب معدل الإنجاز
    if ($stats['total'] > 0) {
        $stats['completion_rate'] = round(($stats['completed'] / $stats['total']) * 100, 1);
    }

    // حساب متوسط وقت الإنجاز
    if ($completedTasksCount > 0) {
        $stats['avg_completion_time'] = round($totalCompletionDays / $completedTasksCount, 1);
    }

    // حساب اتجاه الإنتاجية (آخر 7 أيام)
    for ($i = 6; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $dayQuery = "SELECT COUNT(*) as completed FROM Notifications WHERE Status = 'تم' AND DATE(LastUpdated) = ?";
        $dayStmt = $conn->prepare($dayQuery);
        $dayStmt->execute([$date]);
        $dayResult = $dayStmt->fetch(PDO::FETCH_ASSOC);
        
        $stats['productivity_trend'][] = [
            'date' => $date,
            'completed' => $dayResult['completed'] ?? 0
        ];
    }

    // إحصائيات إضافية للمستخدم الحالي
    $userId = $_SESSION['user_id'];
    $userStatsQuery = "SELECT 
                        COUNT(*) as my_total,
                        SUM(CASE WHEN Status = 'تم' THEN 1 ELSE 0 END) as my_completed,
                        SUM(CASE WHEN Status = 'تحت التنفيذ' THEN 1 ELSE 0 END) as my_in_progress,
                        SUM(CASE WHEN Status = 'لم تنفذ' AND ToDate < CURDATE() THEN 1 ELSE 0 END) as my_overdue
                       FROM Notifications 
                       WHERE AssignedTo = ? OR CreatedBy = ?";
    $userStatsStmt = $conn->prepare($userStatsQuery);
    $userStatsStmt->execute([$userId, $userId]);
    $userStats = $userStatsStmt->fetch(PDO::FETCH_ASSOC);

    $stats['user_stats'] = [
        'total' => $userStats['my_total'] ?? 0,
        'completed' => $userStats['my_completed'] ?? 0,
        'in_progress' => $userStats['my_in_progress'] ?? 0,
        'overdue' => $userStats['my_overdue'] ?? 0,
        'completion_rate' => $userStats['my_total'] > 0 ? 
            round(($userStats['my_completed'] / $userStats['my_total']) * 100, 1) : 0
    ];

    echo json_encode([
        'success' => true, 
        'stats' => $stats,
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false, 
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ]);
}
?>
