<?php
session_start();
require 'db.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

error_reporting(E_ALL);
ini_set('display_errors', 1);

if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo "<script>alert('ليس لديك صلاحية الوصول إلى هذه الصفحة.'); window.location.href = '/e-finance/index';</script>";
    exit;
}

// جلب قائمة العملاء
$customers = $conn->query("SELECT ID, Name FROM Customers")->fetchAll(PDO::FETCH_ASSOC);

// جلب قائمة الموازنات
$budgets = $conn->query("SELECT ID, Title, TotalAmount, SpentAmount FROM Budgets")->fetchAll(PDO::FETCH_ASSOC);

// جلب سجل الصرفيات
$expenses = $conn->query("SELECT e.*, c.Name AS CustomerName, b.Title AS BudgetTitle
                          FROM Expenses e
                          JOIN Customers c ON e.CustomerID = c.ID
                          JOIN Budgets b ON e.BudgetID = b.ID
                          ORDER BY e.CreatedAt DESC
                          LIMIT 50")->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصرفيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<?php include 'header.php'; ?>
<div class="container py-4">
    <h2 class="text-center mb-4">إنشاء صرفية جديدة</h2>

    <form method="POST" action="Save_Expense.php" enctype="multipart/form-data">
        <div class="row g-3">
            <div class="col-md-4">
                <label for="customer_id" class="form-label">اسم العميل</label>
                <select name="customer_id" id="customer_id" class="form-select" required>
                    <option value="">-- اختر العميل --</option>
                    <?php foreach ($customers as $customer): ?>
                        <option value="<?= $customer['ID'] ?>"><?= $customer['Name'] ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4">
                <label for="budget_id" class="form-label">اختر الموازنة</label>
                <select name="budget_id" id="budget_id" class="form-select" required>
                    <option value="">-- اختر الموازنة --</option>
                    <?php foreach ($budgets as $budget): ?>
                        <option value="<?= $budget['ID'] ?>">
                            <?= $budget['Title'] ?> (المتبقي: <?= $budget['TotalAmount'] - $budget['SpentAmount'] ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4">
                <label for="amount" class="form-label">المبلغ</label>
                <input type="number" name="amount" id="amount" class="form-control" required>
            </div>
        </div>

        <div class="row g-3 mt-3">
            <div class="col-md-4">
                <label for="authorization_number" class="form-label">رقم الإذن</label>
                <input type="text" name="authorization_number" id="authorization_number" class="form-control">
            </div>
            <div class="col-md-4">
                <label for="account_number" class="form-label">رقم الحساب</label>
                <input type="text" name="account_number" id="account_number" class="form-control">
            </div>
            <div class="col-md-4">
                <label for="transfer_type" class="form-label">نوع التحويل</label>
                <input type="text" name="transfer_type" id="transfer_type" class="form-control">
            </div>
        </div>

        <div class="row g-3 mt-3">
            <div class="col-md-4">
                <label for="transfer_number" class="form-label">رقم التحويل</label>
                <input type="text" name="transfer_number" id="transfer_number" class="form-control">
            </div>
            <div class="col-md-4">
                <label for="date" class="form-label">التاريخ</label>
                <input type="date" name="date" id="date" class="form-control">
            </div>
            <div class="col-md-4">
                <label for="payment_date" class="form-label">تاريخ الدفع</label>
                <input type="date" name="payment_date" id="payment_date" class="form-control">
            </div>
        </div>

        <div class="mt-3">
            <label for="description" class="form-label">وصف</label>
            <textarea name="description" id="description" class="form-control" rows="2"></textarea>
        </div>

        <div class="mt-3">
            <label for="file" class="form-label">ملف (اختياري)</label>
            <input type="file" name="file" id="file" class="form-control">
        </div>

        <div class="mt-3">
            <label for="notes" class="form-label">ملاحظات</label>
            <textarea name="notes" id="notes" class="form-control" rows="2"></textarea>
        </div>

        <button type="submit" class="btn btn-success mt-4">حفظ الصرفية</button>
    </form>

    <hr class="my-5">

    <h4 class="mb-3">سجل الصرفيات</h4>
    <div class="table-responsive">
        <table class="table table-bordered text-center">
            <thead class="table-light">
                <tr>
                    <th>العميل</th>
                    <th>الموازنة</th>
                    <th>المبلغ</th>
                    <th>التاريخ</th>
                    <th>الوصف</th>
                    <th>تاريخ الإنشاء</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($expenses as $expense): ?>
                    <tr>
                        <td><?= $expense['CustomerName'] ?></td>
                        <td><?= $expense['BudgetTitle'] ?></td>
                        <td><?= $expense['Amount'] ?></td>
                        <td><?= $expense['Date'] ?></td>
                        <td><?= $expense['Description'] ?></td>
                        <td><?= $expense['CreatedAt'] ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div></body>
</html>
