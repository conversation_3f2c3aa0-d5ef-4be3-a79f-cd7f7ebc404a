<?php
session_start();
require 'db.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo "<script>alert('ليس لديك صلاحية الوصول إلى هذه الصفحة.'); window.location.href = '/e-finance/index';</script>";
    exit;
}

// إضافة ميزات الفلترة
$conditions = [];
$params = [];

if (!empty($_GET['from_date'])) {
    $conditions[] = "e.Date >= :from_date";
    $params[':from_date'] = $_GET['from_date'];
}
if (!empty($_GET['to_date'])) {
    $conditions[] = "e.Date <= :to_date";
    $params[':to_date'] = $_GET['to_date'];
}
if (!empty($_GET['customer_search'])) {
    $conditions[] = "c.Name LIKE :customer_search";
    $params[':customer_search'] = "%" . $_GET['customer_search'] . "%";
}

$where = $conditions ? "WHERE " . implode(" AND ", $conditions) : "";

$sql = "SELECT e.*, c.Name AS CustomerName, b.Title AS BudgetTitle 
        FROM Expenses e 
        JOIN Customers c ON e.CustomerID = c.ID 
        JOIN Budgets b ON e.BudgetID = b.ID 
        $where
        ORDER BY e.CreatedAt DESC";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);

$budgets = $conn->query("SELECT ID, Title, TotalAmount, SpentAmount FROM Budgets")->fetchAll(PDO::FETCH_ASSOC);
$customers = $conn->query("SELECT ID, Name FROM Customers")->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $customerID = $_POST['customer_id'];
        $budgetID = $_POST['budget_id'];
        $amount = $_POST['amount'];
        $tax = $_POST['tax'] ?? 0;
        $netAmount = $amount - $tax;

        $budgetStmt = $conn->prepare("SELECT TotalAmount, SpentAmount FROM Budgets WHERE ID = ?");
        $budgetStmt->execute([$budgetID]);
        $budget = $budgetStmt->fetch(PDO::FETCH_ASSOC);
        $remaining = $budget['TotalAmount'] - $budget['SpentAmount'];

        if ($netAmount > $remaining) {
            throw new Exception("المبلغ يتجاوز الموازنة المتاحة.");
        }

        $stmt = $conn->prepare("INSERT INTO Expenses 
            (CustomerID, AuthorizationNumber, AccountNumber, Amount, Tax, NetAmount, Date, PaymentDate, 
            TransferNumber, TransferType, ExpenseNumber, File, Notes, CreatedAt, Description, BudgetID) 
            VALUES 
            (:customerID, :authorizationNumber, :accountNumber, :amount, :tax, :netAmount, :date, :paymentDate, 
            :transferNumber, :transferType, :expenseNumber, :file, :notes, :createdAt, :description, :budgetID)");

        $stmt->execute([
            ':customerID' => $customerID,
            ':authorizationNumber' => $_POST['authorization_number'],
            ':accountNumber' => $_POST['account_number'],
            ':amount' => $amount,
            ':tax' => $tax,
            ':netAmount' => $netAmount,
            ':date' => $_POST['date'],
            ':paymentDate' => $_POST['payment_date'],
            ':transferNumber' => $_POST['transfer_number'],
            ':transferType' => $_POST['transfer_type'],
            ':expenseNumber' => 'EXP' . time(),
            ':file' => null,
            ':notes' => $_POST['notes'] ?? null,
            ':createdAt' => date('Y-m-d H:i:s'),
            ':description' => $_POST['description'],
            ':budgetID' => $budgetID
        ]);

        $update = $conn->prepare("UPDATE Budgets SET SpentAmount = SpentAmount + ? WHERE ID = ?");
        $update->execute([$netAmount, $budgetID]);

        $_SESSION['success'] = "تمت إضافة الصرفية بنجاح";
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;

    } catch (Exception $e) {
        echo "<div class='alert alert-danger mt-3'>خطأ: " . $e->getMessage() . "</div>";
    }
}
// جلب العملاء
$customersStmt = $conn->query("SELECT ID, Name FROM Customers");
$customers = $customersStmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة الصرفيات</title>
    <!-- jQuery و jQuery UI للـ Autocomplete -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        .form-section { background: #f9f9f9; padding: 20px; border-radius: 8px; }
    </style>
</head>
<body>
<?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success text-center">
        <?= $_SESSION['success']; unset($_SESSION['success']); ?>
    </div>
<?php endif; ?>

<?php include 'header.php'; ?>

<div class="container mt-4">
  <!--  <h2 class="text-center mb-4">إضافة صرفية جديدة</h2>-->

    <form method="POST" id="expenseForm" class="form-section">
        <div class="row g-3">
            <div class="col-md-4">
                <label>اسم العميل</label>
                <input type="text" id="customer_name" class="form-control" autocomplete="off">
                <div id="customer_list" class="list-group"></div>
                <input type="hidden" name="customer_id" id="customer_id">
            </div>
            <div class="col-md-4">
                <label>رقم الإذن</label>
                <input type="text" name="authorization_number" id="authorization_number" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label>رقم الحساب</label>
                <input type="text" name="account_number" id="account_number" class="form-control" required>
            </div>

            <div class="col-md-4">
                <label>المبلغ</label>
                <input type="number" name="amount" id="amount" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label>الضريبة</label>
                <input type="number" name="tax" id="tax" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label>التاريخ</label>
                <input type="date" name="date" id="date" class="form-control" required>
            </div>

            <div class="col-md-4">
                <label>تاريخ الدفع</label>
                <input type="date" name="payment_date" id="payment_date" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label>رقم التحويل</label>
                <input type="text" name="transfer_number" id="transfer_number" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label>نوع التحويل</label>
                <input type="text" name="transfer_type" id="transfer_type" class="form-control" required>
            </div>

            <div class="col-md-12">
                <label>الوصف</label>
                <textarea name="description" id="description" class="form-control" rows="2" required></textarea>
            </div>
            <div class="col-md-12">
                <label>ملاحظات</label>
                <textarea name="notes" id="notes" class="form-control" rows="2"></textarea>
            </div>
            <div class="col-md-4">
                <label>الموازنة:</label>
                <select name="budget_id" id="budget" required>
                <option value="">اختر الموازنة</option>
                <?php foreach ($budgets as $b): ?>
                <option value="<?= $b['ID'] ?>"><?= $b['Title'] ?></option>
                <?php endforeach; ?>
        </select><br><br>

        <label>البند:</label>
        <select name="category_id" id="category" required>
            <option value="">اختر الموازنة أولاً</option>
        </select><br><br>
            </div>
        </div>
        <button type="submit" id="submitExpense" class="btn btn-primary mt-4">إضافة صرفية</button>
    </form>

    <hr class="my-5">

    <form method="GET" class="row g-3 mb-4">
        <!-- فلاتر البحث -->
        <div class="col-md-3">
            <label>من تاريخ</label>
            <input type="date" name="from_date" value="<?= $_GET['from_date'] ?? '' ?>" class="form-control">
        </div>
        <div class="col-md-3">
            <label>إلى تاريخ</label>
            <input type="date" name="to_date" value="<?= $_GET['to_date'] ?? '' ?>" class="form-control">
        </div>
        <div class="col-md-4">
            <label>اسم العميل</label>
            <input type="text" name="customer_search" value="<?= $_GET['customer_search'] ?? '' ?>" class="form-control">
        </div>
        <div class="col-md-2 d-flex align-items-end">
            <button type="submit" class="btn btn-primary w-100">بحث</button>
        </div>
    </form>

    <h3 class="text-center mb-4">سجل الصرفيات</h3>

    <div class="mb-3 text-end">
        <button id="exportBtn" class="btn btn-success">📤 تصدير إلى Excel</button>
    </div>

    <div class="table-responsive">
        <table id="expenseTable" class="table table-bordered table-striped text-center">
            <thead class="table-dark">
                <tr>
                    <th>العميل</th>
                    <th>المبلغ</th>
                    <th>الضريبة</th>
                    <th>الصافي</th>
                    <th>التاريخ</th>
                    <th>الموازنة</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($expenses as $exp): ?>
                    <tr>
                        <td><?= $exp['CustomerName'] ?></td>
                        <td><?= $exp['Amount'] ?></td>
                        <td><?= $exp['Tax'] ?></td>
                        <td><?= $exp['NetAmount'] ?></td>
                        <td><?= $exp['Date'] ?></td>
                        <td><?= $exp['BudgetTitle'] ?></td>
                        <td>
                        <a href="edit_expense.php?id=<?= $exp['ID'] ?>" class="btn btn-sm btn-warning">تعديل</a>
                        <a href="delete_expense.php?id=<?= $exp['ID'] ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف الصرفية؟')">حذف</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<?php include 'footer.php'; ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
$(document).ready(function(){
  $('#customer_name').keyup(function(){
    var query = $(this).val();
    if (query != '') {
      $.ajax({
        url: "autocomplete.php",
        method: "GET",
        data: { query: query },
        dataType: "json",
        success: function(data) {
          let list = $('#customer_list');
          list.empty();
          if (data.length > 0) {
            data.forEach(function(customer) {
              list.append('<a href="#" class="list-group-item list-group-item-action" data-id="' + customer.ID + '">' + customer.Name + '</a>');
            });
            list.show();
          } else {
            list.hide();
          }
        }
      });
    } else {
      $('#customer_list').hide();
    }
  });

    $(document).on("click", "#customer_list a", function (e) {
        e.preventDefault();
        $("#customer_name").val($(this).text());
        $("#customer_id").val($(this).data("id"));
        $("#customer_list").hide();
        $.ajax({
    url: "get_customer.php",
    method: "GET",
    data: { id: $(this).data("id") },
    success: function (data) {
        let customer = JSON.parse(data);
        $("#account_number").val(customer.AccountNumber);
        $("#authorization_number").val(customer.AuthorizationNumber);
        $("#amount").val(customer.Amount);
        $("#tax").val(customer.Tax);
        $("#notes").val(customer.Notes);
        // يمكن إضافة المزيد حسب الحقول المتوفرة مثل:
        // $("#transfer_number").val(customer.TransferNumber);
        // $("#category").val(customer.Category);
        // $("#card_expiry").val(customer.CardExpiry); ← لو أضفته في النموذج
    }
});
    });

    $(document).click(function (e) {
        if (!$(e.target).closest("#customer_name, #customer_list").length) {
            $("#customer_list").hide();
        }
    });
});
</script>
</body>
</html>
