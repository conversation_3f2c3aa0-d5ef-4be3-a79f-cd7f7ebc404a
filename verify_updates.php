<?php
/**
 * ملف التحقق من التحديثات
 * يتحقق من أن جميع الصفحات تعمل بشكل صحيح مع التصميم الجديد
 */

// محاكاة بيانات المستخدم للاختبار
session_start();
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'admin';
}

$user = [
    'Username' => 'مستخدم تجريبي',
    'Role' => 'مدير النظام',
    'ProfileImage' => 'default-avatar.png'
];

include 'header.php';
?>

<div class="page-header">
    <h1 class="text-center">
        <i class="fas fa-check-double me-3"></i>التحقق من التحديثات
    </h1>
    <p class="text-center text-muted">فحص شامل لجميع صفحات الموقع بعد التحديث</p>
</div>

<!-- قسم حالة التحديثات -->
<div class="hero-section">
    <h2 class="text-center mb-4">
        <i class="fas fa-clipboard-check me-2"></i>حالة التحديثات
    </h2>
    
    <div class="row">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-check-circle text-success"></i>
                </div>
                <div class="stat-number" id="successCount">0</div>
                <div class="stat-label">صفحات محدثة</div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                </div>
                <div class="stat-number" id="warningCount">0</div>
                <div class="stat-label">تحذيرات</div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-times-circle text-danger"></i>
                </div>
                <div class="stat-number" id="errorCount">0</div>
                <div class="stat-label">أخطاء</div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-file-code text-info"></i>
                </div>
                <div class="stat-number" id="totalCount">0</div>
                <div class="stat-label">إجمالي الصفحات</div>
            </div>
        </div>
    </div>
</div>

<!-- قسم اختبار الصفحات -->
<div class="tasks-container">
    <h3 class="text-center mb-4">
        <i class="fas fa-play-circle me-2"></i>اختبار الصفحات
    </h3>
    
    <div class="text-center mb-4">
        <button class="btn btn-primary btn-lg me-3" onclick="startVerification()">
            <i class="fas fa-play me-2"></i>بدء الفحص
        </button>
        
        <button class="btn btn-success btn-lg me-3" onclick="testSidebar()">
            <i class="fas fa-sidebar me-2"></i>اختبار القائمة الجانبية
        </button>
        
        <button class="btn btn-warning btn-lg" onclick="testResponsive()">
            <i class="fas fa-mobile-alt me-2"></i>اختبار التجاوب
        </button>
    </div>
    
    <div class="progress mb-4" style="display: none;" id="progressContainer">
        <div class="progress-bar progress-bar-striped progress-bar-animated" 
             role="progressbar" style="width: 0%" id="progressBar"></div>
    </div>
    
    <div id="verificationResults"></div>
</div>

<!-- قسم روابط الصفحات -->
<div class="budget-container">
    <h3 class="text-center mb-4">
        <i class="fas fa-link me-2"></i>روابط سريعة للصفحات
    </h3>
    
    <div class="row" id="pageLinks">
        <!-- سيتم ملؤها بـ JavaScript -->
    </div>
</div>

<!-- قسم التوصيات -->
<div class="hero-section">
    <h3 class="text-center mb-4">
        <i class="fas fa-lightbulb me-2"></i>التوصيات والنصائح
    </h3>
    
    <div class="row">
        <div class="col-md-6">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>نصائح للاستخدام</h5>
                <ul class="mb-0">
                    <li>استخدم الزر الأزرق الدائري لفتح القائمة الجانبية</li>
                    <li>تأكد من اختبار جميع الصفحات على أجهزة مختلفة</li>
                    <li>راقب أداء الموقع بعد التحديث</li>
                    <li>تحقق من عمل جميع الروابط والأزرار</li>
                </ul>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>المميزات الجديدة</h5>
                <ul class="mb-0">
                    <li>مساحة أكبر للمحتوى (إزالة القائمة العلوية)</li>
                    <li>قائمة جانبية محسنة مع معلومات المستخدم</li>
                    <li>تصميم متجاوب محسن</li>
                    <li>أداء أفضل وسرعة تحميل محسنة</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// قائمة الصفحات للفحص
const pagesToCheck = [
    { name: 'الصفحة الرئيسية', url: 'index.php', icon: 'fas fa-home' },
    { name: 'لوحة التقارير', url: 'dashboard.php', icon: 'fas fa-chart-line' },
    { name: 'بحث العملاء', url: 'search.php', icon: 'fas fa-search' },
    { name: 'تسجيل عميل جديد', url: 'register.php', icon: 'fas fa-user-plus' },
    { name: 'رفع ملف العملاء', url: 'customer_upload.php', icon: 'fas fa-upload' },
    { name: 'إدارة الصرفيات', url: 'Expense_Management.php', icon: 'fas fa-money-bill-wave' },
    { name: 'إدارة الموازنات', url: 'Budget_Management.php', icon: 'fas fa-wallet' },
    { name: 'إدارة المستخدمين', url: 'manage_users.php', icon: 'fas fa-users-cog' },
    { name: 'الملف الشخصي', url: 'profile.php', icon: 'fas fa-user-circle' },
    { name: 'إدارة المهام', url: 'Add_Notification2.php', icon: 'fas fa-tasks' }
];

let verificationResults = {
    success: 0,
    warning: 0,
    error: 0,
    total: 0
};

// إنشاء روابط الصفحات
function createPageLinks() {
    const container = document.getElementById('pageLinks');
    
    pagesToCheck.forEach(page => {
        const col = document.createElement('div');
        col.className = 'col-md-4 mb-3';
        
        col.innerHTML = `
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="${page.icon} fa-2x text-primary mb-3"></i>
                    <h6 class="card-title">${page.name}</h6>
                    <a href="${page.url}" class="btn btn-outline-primary btn-sm" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i>فتح
                    </a>
                </div>
            </div>
        `;
        
        container.appendChild(col);
    });
}

// بدء عملية الفحص
async function startVerification() {
    const progressContainer = document.getElementById('progressContainer');
    const progressBar = document.getElementById('progressBar');
    const resultsContainer = document.getElementById('verificationResults');
    
    progressContainer.style.display = 'block';
    resultsContainer.innerHTML = '';
    
    verificationResults = { success: 0, warning: 0, error: 0, total: pagesToCheck.length };
    updateCounters();
    
    for (let i = 0; i < pagesToCheck.length; i++) {
        const page = pagesToCheck[i];
        const progress = ((i + 1) / pagesToCheck.length) * 100;
        
        progressBar.style.width = progress + '%';
        progressBar.textContent = `${i + 1}/${pagesToCheck.length}`;
        
        await checkPage(page);
        await new Promise(resolve => setTimeout(resolve, 500)); // تأخير للتأثير البصري
    }
    
    progressContainer.style.display = 'none';
    showFinalResults();
}

// فحص صفحة واحدة
async function checkPage(page) {
    const resultsContainer = document.getElementById('verificationResults');
    
    try {
        // محاكاة فحص الصفحة
        const response = await fetch(page.url, { method: 'HEAD' });
        
        let status, icon, className;
        
        if (response.ok) {
            status = 'متاحة وتعمل بشكل صحيح';
            icon = 'fas fa-check-circle';
            className = 'success';
            verificationResults.success++;
        } else {
            status = `خطأ HTTP: ${response.status}`;
            icon = 'fas fa-exclamation-triangle';
            className = 'warning';
            verificationResults.warning++;
        }
        
        const resultDiv = document.createElement('div');
        resultDiv.className = `alert alert-${className === 'success' ? 'success' : 'warning'} mb-2`;
        resultDiv.innerHTML = `
            <i class="${icon} me-2"></i>
            <strong>${page.name}</strong>: ${status}
        `;
        
        resultsContainer.appendChild(resultDiv);
        
    } catch (error) {
        verificationResults.error++;
        
        const resultDiv = document.createElement('div');
        resultDiv.className = 'alert alert-danger mb-2';
        resultDiv.innerHTML = `
            <i class="fas fa-times-circle me-2"></i>
            <strong>${page.name}</strong>: خطأ في الاتصال
        `;
        
        resultsContainer.appendChild(resultDiv);
    }
    
    updateCounters();
}

// تحديث العدادات
function updateCounters() {
    document.getElementById('successCount').textContent = verificationResults.success;
    document.getElementById('warningCount').textContent = verificationResults.warning;
    document.getElementById('errorCount').textContent = verificationResults.error;
    document.getElementById('totalCount').textContent = verificationResults.total;
}

// عرض النتائج النهائية
function showFinalResults() {
    const resultsContainer = document.getElementById('verificationResults');
    
    const summaryDiv = document.createElement('div');
    summaryDiv.className = 'alert alert-info mt-4';
    summaryDiv.innerHTML = `
        <h5><i class="fas fa-chart-pie me-2"></i>ملخص النتائج</h5>
        <div class="row text-center">
            <div class="col-md-3">
                <h4 class="text-success">${verificationResults.success}</h4>
                <small>صفحات تعمل بشكل صحيح</small>
            </div>
            <div class="col-md-3">
                <h4 class="text-warning">${verificationResults.warning}</h4>
                <small>تحذيرات</small>
            </div>
            <div class="col-md-3">
                <h4 class="text-danger">${verificationResults.error}</h4>
                <small>أخطاء</small>
            </div>
            <div class="col-md-3">
                <h4 class="text-info">${verificationResults.total}</h4>
                <small>إجمالي الصفحات</small>
            </div>
        </div>
    `;
    
    resultsContainer.appendChild(summaryDiv);
}

// اختبار القائمة الجانبية
function testSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    
    if (sidebar && sidebarToggle) {
        sidebarToggle.click();
        
        setTimeout(() => {
            alert('تم فتح القائمة الجانبية بنجاح! ✅');
            
            setTimeout(() => {
                sidebarToggle.click();
            }, 2000);
        }, 500);
    } else {
        alert('خطأ: لم يتم العثور على القائمة الجانبية ❌');
    }
}

// اختبار التجاوب
function testResponsive() {
    const width = window.innerWidth;
    let deviceType;
    
    if (width <= 768) {
        deviceType = 'جهاز محمول';
    } else if (width <= 1024) {
        deviceType = 'جهاز لوحي';
    } else {
        deviceType = 'جهاز سطح مكتب';
    }
    
    alert(`عرض الشاشة: ${width}px\nنوع الجهاز: ${deviceType}\nالتصميم متجاوب ✅`);
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    createPageLinks();
    updateCounters();
    
    // إضافة تأثيرات بصرية
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 15px 35px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
});
</script>