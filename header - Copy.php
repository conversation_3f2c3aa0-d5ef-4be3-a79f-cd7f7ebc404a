<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// استدعاء معلومات المستخدم من الجلسة
require_once 'db.php';
$userID = $_SESSION['user_id'] ?? null;
$user = [];

if ($userID) {
    $stmt = $conn->prepare("SELECT Username, Role, ProfileImage FROM Users WHERE ID = :id");
    $stmt->bindParam(':id', $userID, PDO::PARAM_INT);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Finance</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        .navbar-profile-img {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            object-fit: cover;
            margin-left: 8px;
        }
    </style>
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container-fluid">
     <!--   <a class="navbar-brand" href="/e-finance/index">الصفحة الرئيسية</a>-->

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar" aria-controls="mainNavbar" aria-expanded="false" aria-label="تبديل القائمة">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse justify-content-between" id="mainNavbar">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                <li class="nav-item"><a class="nav-link" href="/e-finance/index">الرئيسية</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/Add_Notification1">ادارة التنبيهات</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/register">تسجيل العملاء</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/search">بحث العملاء</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/Expense_Management">ادارة الصرفيات </a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/Budget_Management">ادارة الموازنات </a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/expenses">إنشاء صرفية</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/search_exp">بحث الصرفيات</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/add_budget">إضافة رصيد</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/dashboard">التقارير</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/manage_users">الصلاحيات</a></li>
                <li class="nav-item"><a class="nav-link text-danger" href="/e-finance/logout">تسجيل الخروج</a></li>
            </ul>

            <!-- معلومات المستخدم -->
            <?php if (!empty($user)): ?>
                <div class="d-flex align-items-center text-white">
                    <span class="me-2"><?= htmlspecialchars($user['Username']) ?></span>
                    <a href="/e-finance/profile.php">
                        <img src="<?= !empty($user['ProfileImage']) ? 'uploads/' . $user['ProfileImage'] : 'default-user.png' ?>" class="navbar-profile-img" alt="الصورة الشخصية">
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</nav>
<div class="container mt-4">
