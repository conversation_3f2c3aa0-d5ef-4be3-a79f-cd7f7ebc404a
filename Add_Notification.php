<?php
session_start();
require 'db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = $_POST['title'];
    $message = $_POST['message'];
    $notifyDate = $_POST['notify_date'];

    $stmt = $conn->prepare("INSERT INTO Notifications (Title, Message, NotifyDate) VALUES (?, ?, ?)");
    $stmt->execute([$title, $message, $notifyDate]);

    $_SESSION['success'] = "تمت إضافة التنبيه بنجاح";
    header("Location: Add_Notification.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إضافة تنبيه</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
</head>
<body>
<?php include 'header.php'; ?>
<div class="container mt-5">
    <h2 class="text-center mb-4">إضافة تنبيه جديد</h2>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success text-center"><?= $_SESSION['success']; unset($_SESSION['success']); ?></div>
    <?php endif; ?>

    <form method="POST" class="form-section">
        <div class="mb-3">
            <label>عنوان التنبيه</label>
            <input type="text" name="title" class="form-control" required>
        </div>
        <div class="mb-3">
            <label>الرسالة</label>
            <textarea name="message" class="form-control" rows="3"></textarea>
        </div>
        <div class="mb-3">
            <label>تاريخ التنبيه</label>
            <input type="date" name="notify_date" class="form-control" required>
        </div>
        <button type="submit" class="btn btn-primary">إضافة</button>
    </form>
</div>
</body>
</html>
