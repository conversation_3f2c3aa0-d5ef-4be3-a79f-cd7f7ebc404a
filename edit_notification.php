<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

// التحقق من صلاحيات الأدمن فقط
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: Add_Notification1.php");
    exit;
}

$id = $_GET['id'] ?? null;

// تأكيد id صالح
if (!$id || !is_numeric($id)) {
    $_SESSION['error'] = "معرف التنبيه غير صالح.";
    header("Location: notifications_management.php");
    exit;
}

// جلب بيانات التنبيه
$stmt = $conn->prepare("SELECT * FROM Notifications WHERE ID = ?");
$stmt->execute([$id]);
$toDate = $notification['ToDate'] ?? '';
$notification = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$notification) {
    $_SESSION['error'] = "التنبيه غير موجود.";
    header("Location: Add_Notification2.php");
    exit;
}

// تحديث بيانات التنبيه
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = $_POST['title'];
    $message = $_POST['message'];
    $fromDate = $_POST['from_date'];
    $toDate = $_POST['to_date'] ?? $notifyDate;
    $status = $_POST['status'];

    // تحديث التاريخ فقط في خانة NotifyDate إلى "من تاريخ"
   $update = $conn->prepare("UPDATE Notifications SET Title = ?, Message = ?, NotifyDate = ?, ToDate = ? WHERE ID = ?");
   $update->execute([$title, $message, $notifyDate, $toDate, $id]);
   $notifyDate = $_POST['NotifyDate'] ?? null;

    $_SESSION['success'] = "تم تعديل التنبيه بنجاح.";
    header("Location: Add_Notification2.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تعديل المهمه</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        .center-box { max-width: 700px; margin: auto; margin-top: 40px; }
    </style>
</head>
<body>

<?php include 'header.php'; ?>

<div class="center-box">
    <div class="card shadow">
        <div class="card-header bg-warning text-white text-center">
            <h4 class="mb-0">تعديل المهمه</h4>
        </div>
        <div class="card-body">

            <form method="POST" class="row g-3">
                <div class="col-md-6">
                    <label>العنوان</label>
                    <input type="text" name="title" class="form-control" value="<?= htmlspecialchars($notification['Title']) ?>" required>
                </div>

                <div class="col-md-6">
                    <label>من تاريخ</label>
                    <input type="date" name="from_date" class="form-control" value="<?= $notification['NotifyDate'] ?>" required>
                </div>

	       <div class="col-md-6">
   		 <label>إلى تاريخ</label>
   		 <input type="date" name="to_date" class="form-control" value="<?= $notification['ToDate'] ?>">
		</div>


                <div class="col-md-6">
                    <label>الحالة</label>
                    <select name="status" class="form-select" required>
                        <option value="تم" <?= $notification['Status'] === 'تم' ? 'selected' : '' ?>>تم</option>
                        <option value="تحت التنفيذ" <?= $notification['Status'] === 'تحت التنفيذ' ? 'selected' : '' ?>>تحت التنفيذ</option>
                        <option value="لم تنفذ" <?= $notification['Status'] === 'لم تنفذ' ? 'selected' : '' ?>>لم تنفذ</option>
                    </select>
                </div>

                <div class="col-md-12">
                    <label>الوصف</label>
                    <textarea name="message" class="form-control" rows="3" required><?= htmlspecialchars($notification['Message']) ?></textarea>
                </div>

                <div class="col-md-12 text-center">
                    <button type="submit" class="btn btn-success">حفظ التعديل</button>
                    <a href="Add_Notification2.php" class="btn btn-secondary">رجوع</a>
                </div>
            </form>

        </div>
    </div>
</div></body>
</html>
