<?php
require 'db.php';
session_start();

// التحقق من صلاحية المستخدم
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: /e-finance/login");
    exit;
}

// جلب جميع الأنشطة من قاعدة البيانات
$stmt = $conn->prepare("
    SELECT ua.*, u.Username 
    FROM UserActivity ua
    JOIN Users u ON ua.UserID = u.ID
    ORDER BY ua.Timestamp DESC
");
$stmt->execute();
$activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>لوحة تحكم النشاطات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <?php include 'header.php'; ?>

    <div class="container mt-5">
        <h2 class="text-center mb-4">📊 سجل نشاطات المستخدمين</h2>

        <?php if (count($activities) > 0): ?>
            <div class="table-responsive">
                <table class="table table-bordered table-striped text-center">
                    <thead class="table-dark">
                        <tr>
                            <th>المستخدم</th>
                            <th>الصفحة</th>
                            <th>النشاط</th>
                            <th>التفاصيل</th>
                            <th>المدة (ثانية)</th>
                            <th>الوقت</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($activities as $activity): ?>
                            <tr>
                                <td><?= htmlspecialchars($activity['Username']) ?></td>
                                <td><?= htmlspecialchars($activity['Page']) ?></td>
                                <td><?= htmlspecialchars($activity['Action']) ?></td>
                                <td><?= !empty($activity['Details']) ? nl2br(htmlspecialchars($activity['Details'])) : 'لا توجد تفاصيل' ?></td>
                                <td>
                                <?php 
                                    if (!empty($activity['StartTime']) && !empty($activity['EndTime'])) {
                                    $startTime = new DateTime($activity['StartTime']);
                                    $endTime = new DateTime($activity['EndTime']);
                                    $interval = $startTime->diff($endTime);
                                    echo $interval->format("%i:%s"); // دقائق:ثواني
                                    } else {
                                    echo '-';
                                           }
                                ?>
</td>


                                <td><?= $activity['Timestamp'] ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="alert alert-warning text-center">لا توجد نشاطات مسجلة حتى الآن.</div>
        <?php endif; ?>
    </div></body>
</html>
