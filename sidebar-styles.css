/* تصميم القائمة الجانبية المحسن - بدون قائمة علوية */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-left: 3px solid #007bff;
    box-shadow: -8px 0 30px rgba(0, 123, 255, 0.15);
    z-index: 1000;
    overflow-y: auto;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateX(100%);
}

.sidebar.active {
    transform: translateX(0);
}

.sidebar-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 25px 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

/* تصميم قسم معلومات المستخدم */
.user-profile-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.user-avatar {
    margin-bottom: 15px;
}

.profile-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.profile-img:hover {
    transform: scale(1.1);
    border-color: white;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.user-details {
    text-align: center;
}

.user-name {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.user-role {
    font-size: 0.85rem;
    opacity: 0.9;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 15px;
    font-weight: 500;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.sidebar-header h4 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.sidebar-menu {
    padding: 0;
    margin: 0;
    list-style: none;
}

.sidebar-menu li {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 18px 25px;
    color: #2c3e50;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    font-weight: 500;
    font-size: 0.95rem;
}

.sidebar-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.08), transparent);
    transition: right 0.6s ease;
}

.sidebar-menu a:hover::before {
    right: 100%;
}

.sidebar-menu a::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    transition: width 0.3s ease;
}

.sidebar-menu a:hover::after {
    width: 4px;
}

.sidebar-menu a:hover {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 86, 179, 0.05));
    color: #007bff;
    transform: translateX(-8px);
    padding-right: 33px;
}

.sidebar-menu a.active {
    background: linear-gradient(135deg, #000000, #333333);
    color: white;
    font-weight: 700;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2);
    border-right: 4px solid #007bff;
}

.sidebar-menu a.active::after {
    width: 4px;
    background: #007bff;
}

.sidebar-menu a.active:hover {
    background: linear-gradient(135deg, #333333, #000000);
    transform: translateX(-5px);
}

.sidebar-menu i {
    margin-left: 18px;
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.sidebar-menu a:hover i {
    transform: scale(1.1);
    color: #007bff;
}

.sidebar-menu a.active i {
    color: #007bff;
    text-shadow: 0 0 8px rgba(0, 123, 255, 0.5);
}

.sidebar-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    transform: none;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
    z-index: 1001;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    font-size: 1.2rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.sidebar-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(0, 123, 255, 0.6);
    background: linear-gradient(135deg, #0056b3, #004085);
    border-color: rgba(255, 255, 255, 0.4);
}

.sidebar-toggle.active {
    background: linear-gradient(135deg, #000000, #333333);
    transform: rotate(180deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    border-color: rgba(255, 255, 255, 0.3);
}

.sidebar-toggle.active:hover {
    background: linear-gradient(135deg, #333333, #555555);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.6);
    transform: rotate(180deg) scale(1.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(2px);
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* تحسين المحتوى الرئيسي */
.main-content {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    min-height: calc(100vh - 90px);
}

.main-content.sidebar-open {
    margin-right: 280px;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        top: 0;
        height: 100vh;
        border-left: none;
        border-top: 3px solid #007bff;
    }

    .main-content.sidebar-open {
        margin-right: 0;
    }

    .sidebar-toggle {
        top: 15px;
        right: 15px;
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }

    .sidebar-menu a {
        padding: 20px 25px;
        font-size: 1rem;
    }

    .sidebar-menu i {
        font-size: 1.3rem;
        margin-left: 20px;
    }
}

@media (max-width: 480px) {
    .sidebar-toggle {
        top: 10px;
        right: 10px;
        width: 45px;
        height: 45px;
        font-size: 0.9rem;
    }
    
    .sidebar-menu a {
        padding: 18px 20px;
    }
}

/* تحسينات شريط التمرير */
.sidebar::-webkit-scrollbar {
    width: 8px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.sidebar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.3), rgba(0, 86, 179, 0.5));
    border-radius: 4px;
    transition: all 0.3s ease;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(0, 123, 255, 0.5), rgba(0, 86, 179, 0.7));
}

/* تأثيرات إضافية */
.sidebar-menu a {
    position: relative;
}

.sidebar-menu a:not(.active):hover {
    box-shadow: inset 0 0 15px rgba(0, 123, 255, 0.1);
}

/* تحسين الخط والنصوص */
.sidebar-menu span {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    letter-spacing: 0.3px;
}

/* تأثير النبضة للزر */
@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}

.sidebar-toggle:not(.active):hover {
    animation: pulse 1.5s infinite;
}

/* تأثير إضافي للزر في الأعلى */
.sidebar-toggle::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #007bff, #0056b3, #007bff);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sidebar-toggle:hover::after {
    opacity: 0.3;
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسين الظل للزر */
.sidebar-toggle {
    filter: drop-shadow(0 4px 8px rgba(0, 123, 255, 0.3));
}

.sidebar-toggle:hover {
    filter: drop-shadow(0 6px 12px rgba(0, 123, 255, 0.5));
}

.sidebar-toggle.active {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.sidebar-toggle.active:hover {
    filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.5));
}
