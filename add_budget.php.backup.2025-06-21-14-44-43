<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول وصلاحيات المستخدم
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $amount = $_POST['amount'];
    $description = $_POST['description'];
    $date = $_POST['date'];
    $file = NULL;

    if (isset($_FILES['file']) && $_FILES['file']['error'] == 0) {
        $target_dir = "uploads/";
        $file_name = basename($_FILES["file"]["name"]);
        $target_file = $target_dir . $file_name;
        move_uploaded_file($_FILES["file"]["tmp_name"], $target_file);
        $file = $file_name;
    }

    try {
        // تسجيل الدعم في جدول BudgetSupport
        $stmt = $conn->prepare("INSERT INTO BudgetSupport (Amount, Date, Description, File) VALUES (?, ?, ?, ?)");
        $stmt->execute([$amount, $date, $description, $file]);

        // تحديث الإجمالي في جدول BudgetTotal
        $stmt = $conn->prepare("UPDATE BudgetTotal SET Total = Total + ? WHERE ID = 1");
        $stmt->execute([$amount]);

        echo "<script>alert('تم إضافة الموازنة بنجاح!'); window.location.href='index.php';</script>";
    } catch (PDOException $e) {
        echo "حدث خطأ: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>زيادة الموازنة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
<?php include 'header.php'; ?>
<div class="container mt-5">
    <!--<h1 class="text-center">زيادة الموازنة</h1>-->
    <form method="POST" enctype="multipart/form-data" class="mt-4">
        <div class="mb-3">
            <label for="amount" class="form-label">المبلغ</label>
            <input type="number" name="amount" class="form-control" required>
        </div>
        <div class="mb-3">
            <label for="date" class="form-label">التاريخ</label>
            <input type="date" name="date" class="form-control" required>
        </div>
        <div class="mb-3">
            <label for="description" class="form-label">الشرح</label>
            <textarea name="description" class="form-control" rows="3" required></textarea>
        </div>
        <div class="mb-3">
            <label for="file" class="form-label">الملف</label>
            <input type="file" name="file" class="form-control">
        </div>
        <button type="submit" class="btn btn-primary">إضافة</button>
    </form>
</div>
<?php include 'footer.php'; ?>
</body>
</html>
