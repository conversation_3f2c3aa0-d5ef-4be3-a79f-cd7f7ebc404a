<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}
error_reporting(E_ALL);
ini_set('display_errors', 1);

// التحقق من صلاحيات الوصول
$role = $_SESSION['role'];
if (!in_array($role, ['admin', 'viewer'])) {
    echo "<script>alert('ليس لديك صلاحية الوصول إلى هذه الصفحة.'); window.location.href = 'index.php';</script>";
    exit;
}

// جلب البيانات الإجمالية
$totalCustomers = $conn->query("SELECT COUNT(*) AS total FROM Customers")->fetchColumn();
$totalExpenses = $conn->query("SELECT COUNT(*) AS total FROM Expenses")->fetchColumn();
$totalAmount = $conn->query("SELECT SUM(Amount) AS total FROM Expenses")->fetchColumn();
$totalTax = $conn->query("SELECT SUM(Tax) AS total FROM Expenses")->fetchColumn();

// جلب البيانات للفترة الزمنية
$fromDate = $_GET['from_date'] ?? null;
$toDate = $_GET['to_date'] ?? null;

if ($fromDate && $toDate) {
    $stmt = $conn->prepare("SELECT COUNT(*) AS totalExpenses, SUM(Amount) AS totalAmount, SUM(Tax) AS totalTax 
                            FROM Expenses 
                            WHERE PaymentDate BETWEEN :fromDate AND :toDate");
    $stmt->execute(['fromDate' => $fromDate, 'toDate' => $toDate]);
    $filteredData = $stmt->fetch(PDO::FETCH_ASSOC);
} else {
    $filteredData = [
        'totalExpenses' => $totalExpenses,
        'totalAmount' => $totalAmount,
        'totalTax' => $totalTax,
    ];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التقارير</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e3f2fd 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .container {
            margin-top: 20px;
            padding: 0 15px;
        }

        .dashboard-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #0056b3);
        }

        .dashboard-header h1 {
            color: #000000;
            font-weight: 700;
            margin: 0;
            font-size: 2.5rem;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #0056b3);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #000000;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .chart-title {
            color: #000000;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }

        canvas {
            max-width: 100%;
            border-radius: 10px;
        }

        .filter-form {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .filter-form h4 {
            color: #000000;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 123, 255, 0.4);
        }

        @media (max-width: 768px) {
            .dashboard-header h1 {
                font-size: 2rem;
            }

            .stats-container {
                grid-template-columns: 1fr;
            }

            .stat-card {
                padding: 20px;
            }

            .chart-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
<?php include 'header.php'; ?>
    <!-- عنوان الصفحة -->
    <div class="dashboard-header">
        <h1><i class="fas fa-chart-line me-3"></i>لوحة التقارير والإحصائيات</h1>
        <p class="mb-0" style="color: #7f8c8d; font-size: 1.1rem;">عرض شامل لجميع البيانات والإحصائيات</p>
    </div>

    <!-- نموذج الفلترة -->
    <div class="filter-form">
        <h4><i class="fas fa-filter me-2"></i>فلترة البيانات</h4>
        <form method="GET">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="from_date" class="form-label"><i class="fas fa-calendar-alt me-2"></i>من تاريخ</label>
                    <input type="date" name="from_date" id="from_date" class="form-control" value="<?php echo htmlspecialchars($fromDate); ?>">
                </div>
                <div class="col-md-4">
                    <label for="to_date" class="form-label"><i class="fas fa-calendar-alt me-2"></i>إلى تاريخ</label>
                    <input type="date" name="to_date" id="to_date" class="form-control" value="<?php echo htmlspecialchars($toDate); ?>">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>فلترة البيانات
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- الإحصائيات -->
    <div class="main-content" id="mainContent">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-number"><?php echo number_format($totalCustomers); ?></div>
            <div class="stat-label">إجمالي العملاء</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-file-invoice-dollar"></i>
            </div>
            <div class="stat-number"><?php echo number_format($filteredData['totalExpenses']); ?></div>
            <div class="stat-label">إجمالي الصرفيات</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-number"><?php echo number_format($filteredData['totalAmount'] ?? 0, 2); ?></div>
            <div class="stat-label">إجمالي المنصرف (جنيه)</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="stat-number"><?php echo number_format($filteredData['totalTax'] ?? 0, 2); ?></div>
            <div class="stat-label">إجمالي الضرائب (جنيه)</div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="main-content" id="mainContent">
        <div class="chart-title">
            <i class="fas fa-chart-bar me-2"></i>الرسم البياني للإحصائيات
        </div>
        <canvas id="expensesChart"></canvas>
    </div>
</div>

<script>
    const ctx = document.getElementById('expensesChart').getContext('2d');
    const expensesChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['إجمالي العملاء', 'إجمالي الصرفيات', 'إجمالي المنصرف', 'إجمالي الضرائب'],
            datasets: [{
                label: 'الإحصائيات',
                data: [<?php echo $totalCustomers; ?>, <?php echo $filteredData['totalExpenses']; ?>, <?php echo $filteredData['totalAmount']; ?>, <?php echo $filteredData['totalTax']; ?>],
                backgroundColor: [
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(255, 206, 86, 0.7)'
                ],
                borderColor: [
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 206, 86, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    enabled: true
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
</script>