<?php
session_start();
require 'db.php'; // الاتصال بقاعدة البيانات

// التحقق من تسجيل الدخول كمسؤول
//if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
 //   header("Location: /e-finance/login");
//    exit;
//}

// التحقق من الصلاحيات
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo "<script>alert('ليس لديك صلاحية الوصول إلى هذه الصفحة.'); window.location.href = '/e-finance/index';</script>";
    exit;
}

// إضافة مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_user'])) {
    $username = $_POST['username'];
    $password = password_hash($_POST['password'], PASSWORD_BCRYPT);
    $role = $_POST['role'];

    $stmt = $conn->prepare("INSERT INTO Users (Username, Password, Role) VALUES (:username, :password, :role)");
    $stmt->bindParam(':username', $username);
    $stmt->bindParam(':password', $password);
    $stmt->bindParam(':role', $role);

    if ($stmt->execute()) {
        echo "<script>alert('تم إضافة المستخدم بنجاح!');</script>";
    } else {
        echo "<script>alert('حدث خطأ أثناء إضافة المستخدم.');</script>";
    }
}

// حذف مستخدم
if (isset($_POST['delete_user'])) {
    $userID = intval($_POST['user_id']);

    $stmt = $conn->prepare("DELETE FROM Users WHERE ID = :userID");
    $stmt->bindParam(':userID', $userID, PDO::PARAM_INT);

    if ($stmt->execute()) {
        echo "<script>alert('تم حذف المستخدم بنجاح!');</script>";
    } else {
        echo "<script>alert('حدث خطأ أثناء حذف المستخدم.');</script>";
    }
}

// جلب جميع المستخدمين
$stmt = $conn->query("SELECT * FROM Users");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
<?php include 'header.php'; ?>
<div class="container mt-5">
    <h1 class="text-center">إدارة المستخدمين</h1><br>
</div>
    <!-- نموذج إضافة مستخدم جديد -->
    <form method="POST" class="mb-5">
        <div class="row">
            <div class="col-md-4">
                <label for="username" class="form-label">اسم المستخدم</label>
                <input type="text" id="username" name="username" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="password" class="form-label">كلمة المرور</label>
                <input type="password" id="password" name="password" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="role" class="form-label">الدور</label>
                <select id="role" name="role" class="form-select" required>
                    <option value="viewer">مشاهدة فقط</option>
                    <option value="editor">تعديل وإضافة</option>
                    <option value="admin">مسؤول</option>
                </select>
            </div>
        </div>
        <button type="submit" name="add_user" class="btn btn-primary mt-3">إضافة المستخدم</button>
    </form>

    <!-- جدول المستخدمين -->
    <table class="table table-bordered text-center">
        <thead class="table-dark">
        <tr>
            <th>#</th>
            <th>اسم المستخدم</th>
            <th>الدور</th>
            <th>الإجراءات</th>
        </tr>
        </thead>
        <tbody>
        <?php if ($users): ?>
            <?php foreach ($users as $index => $user): ?>
                <tr>
                    <td><?php echo $index + 1; ?></td>
                    <td><?php echo htmlspecialchars($user['Username']); ?></td>
                    <td>
                        <?php
                        if ($user['Role'] === 'admin') {
                            echo 'مسؤول';
                        } elseif ($user['Role'] === 'editor') {
                            echo 'تعديل وإضافة';
                        } else {
                            echo 'مشاهدة فقط';
                        }
                        ?>
                    </td>
                    <td>
                        <!-- زر حذف -->
                        <form method="POST" style="display:inline-block;">
                            <input type="hidden" name="user_id" value="<?php echo $user['ID']; ?>">
                            <button type="submit" name="delete_user" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟')">حذف</button>
                        </form>
                    </td>
                </tr>
            <?php endforeach; ?>
        <?php else: ?>
            <tr>
                <td colspan="4">لا يوجد مستخدمون مسجلون.</td>
            </tr>
        <?php endif; ?>
        </tbody>
    </table>
</div>
<?php include 'footer.php'; ?>
</body>
</html>
