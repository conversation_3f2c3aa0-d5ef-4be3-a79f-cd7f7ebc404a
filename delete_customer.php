<?php
session_start();
require 'db.php'; // الاتصال بقاعدة البيانات

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /login");
    exit;
}

// التحقق من صلاحيات المستخدم
if ($_SESSION['role'] !== 'admin') {
    header("Location: /search");
    exit;
}

// التحقق من وجود ID للحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_id'])) {
    $delete_id = intval($_POST['delete_id']);
    if ($delete_id > 0) {
        // تنفيذ عملية الحذف
        $stmt = $conn->prepare("DELETE FROM Customers WHERE ID = :id");
        $stmt->bindParam(':id', $delete_id, PDO::PARAM_INT);
        if ($stmt->execute()) {
            // إعادة التوجيه بعد الحذف
            echo "<script>alert('تم حذف العميل بنجاح!'); window.location.href = 'search.php';</script>";
        } else {
            echo "<script>alert('حدث خطأ أثناء الحذف!'); window.location.href = 'search.php';</script>";
        }
    } else {
        echo "<script>alert('رقم العميل غير صالح!'); window.location.href = 'search.php';</script>";
    }
} else {
    header("Location: search.php");
    exit;
}
?>
