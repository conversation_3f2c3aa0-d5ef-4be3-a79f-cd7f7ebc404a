<?php

session_start();

require 'db.php';



// التحقق من تسجيل الدخول

if (!isset($_SESSION['user_id'])) {

    header("Location: /login");

    exit;

}



$role = $_SESSION['role'];

if (!in_array($role, ['admin', 'viewer'])) {

    header("Location: /index");

    exit;

}



// البحث

$query = "SELECT e.*, c.Name AS CustomerName 

          FROM Expenses e 

          JOIN Customers c ON e.CustomerID = c.ID";



if (!empty($search)) {

    $query .= " WHERE c.Name LIKE :search 

                OR e.AuthorizationNumber LIKE :search 

                OR e.AccountNumber LIKE :search 

                OR CAST(e.Amount AS CHAR) LIKE :search 

                OR CAST(e.Tax AS CHAR) LIKE :search 

                OR e.TransferNumber LIKE :search 

                OR e.ExpenseNumber LIKE :search";

}

$query .= " ORDER BY e.PaymentDate DESC";



$stmt = $conn->prepare($query);



if (!empty($search)) {

    $stmt->bindValue(':search', "%$search%");

}

$stmt->execute();



$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);



?>

<!DOCTYPE html>

<html lang="ar" dir="rtl">

<head>

    <meta charset="UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>إدارة الصرفيات</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

</head>

<body>

<div class="container mt-5">

    <a href="/index" class="btn btn-secondary mb-3">العودة إلى الرئيسية</a>

</div>

<div class="container">

    <h1 class="text-center mb-4">إدارة الصرفيات</h1>



    <!-- البحث -->

    <form class="mb-4">

        <div class="input-group">

            <input type="text" name="query" class="form-control" placeholder="ابحث باستخدام اسم العميل، رقم الإذن، المبلغ..." value="<?= $search ?>">

            <button type="submit" class="btn btn-primary">بحث</button>

        </div>

    </form>



    <!-- عرض الصرفيات -->

    <table class="table table-bordered text-center">

        <thead class="table-dark">

            <tr>

                <th>#</th>

                <th>العميل</th>

                <th>رقم الإذن</th>

                <th>رقم الحساب</th>

                <th>المبلغ</th>

                <th>الضريبة</th>

                <th>الصافي</th>

                <th>تاريخ الصرف</th>

                <th>الوصف</th>

                <th>الإجراءات</th>

            </tr>

        </thead>

        <tbody>

        <?php if (!empty($expenses)): ?>

            <?php foreach ($expenses as $index => $expense): ?>

                <tr>

                    <td><?= $index + 1 ?></td>

                    <td><?= htmlspecialchars($expense['CustomerName']) ?></td>

                    <td><?= htmlspecialchars($expense['AuthorizationNumber']) ?></td>

                    <td><?= htmlspecialchars($expense['AccountNumber']) ?></td>

                    <td><?= number_format($expense['Amount'], 2) ?></td>

                    <td><?= number_format($expense['Tax'], 2) ?></td>

                    <td><?= number_format($expense['NetAmount'], 2) ?></td>

                    <td><?= htmlspecialchars($expense['PaymentDate']) ?></td>

                    <td><?= htmlspecialchars($expense['Description']) ?></td>

                    <td>

                        <?php if ($role === 'admin'): ?>

                            <a href="edit_expense.php?id=<?= $expense['ID'] ?>" class="btn btn-warning btn-sm">تعديل</a>

                            <form method="POST" action="delete_expense.php" style="display:inline-block;">

                                <input type="hidden" name="delete_id" value="<?= $expense['ID'] ?>">

                                <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟')">حذف</button>

                            </form>

                        <?php else: ?>

                            <span class="text-muted">لا صلاحية</span>

                        <?php endif; ?>

                    </td>

                </tr>

            <?php endforeach; ?>

        <?php else: ?>

            <tr><td colspan="10" class="text-center">لا توجد نتائج.</td></tr>

        <?php endif; ?>

        </tbody>

    </table>

</div>



<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>

</html>

