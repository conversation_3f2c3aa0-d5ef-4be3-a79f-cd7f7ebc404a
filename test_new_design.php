<?php
session_start();

// محاكاة بيانات المستخدم للاختبار
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'admin';
}

// محاكاة اتصال قاعدة البيانات للاختبار
$user = [
    'Username' => 'أحمد يحيى',
    'Role' => 'مدير النظام',
    'ProfileImage' => 'default-avatar.png'
];

include 'header.php';
?>

<!-- المحتوى الرئيسي -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1 class="mb-2">
                <i class="fas fa-rocket me-3"></i>التصميم الجديد - قائمة جانبية فقط
            </h1>
            <p class="mb-0 text-muted">تم إزالة القائمة العلوية والاعتماد على القائمة الجانبية بالكامل</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="badge bg-success fs-6 p-3">
                <i class="fas fa-check-circle me-2"></i>تصميم محسن
            </div>
        </div>
    </div>
</div>

<!-- قسم المميزات الجديدة -->
<div class="hero-section">
    <h2 class="text-center mb-4">
        <i class="fas fa-star me-2"></i>المميزات الجديدة
    </h2>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-times-circle text-danger me-2"></i>إزالة القائمة العلوية
                    </h5>
                    <p class="card-text">تم إزالة القائمة العلوية المثبتة بالكامل لتوفير مساحة أكبر للمحتوى</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>مساحة أكبر للمحتوى</li>
                        <li><i class="fas fa-check text-success me-2"></i>تصميم أكثر نظافة</li>
                        <li><i class="fas fa-check text-success me-2"></i>تجربة مستخدم محسنة</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-sidebar text-primary me-2"></i>قائمة جانبية محسنة
                    </h5>
                    <p class="card-text">القائمة الجانبية الآن تحتوي على معلومات المستخدم وجميع الروابط</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>معلومات المستخدم</li>
                        <li><i class="fas fa-check text-success me-2"></i>صورة المستخدم</li>
                        <li><i class="fas fa-check text-success me-2"></i>جميع روابط الموقع</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قسم الإحصائيات -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-mobile-alt"></i>
        </div>
        <div class="stat-number">100%</div>
        <div class="stat-label">تجاوب مع الأجهزة</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-tachometer-alt"></i>
        </div>
        <div class="stat-number">50%</div>
        <div class="stat-label">تحسن في الأداء</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-eye"></i>
        </div>
        <div class="stat-number">90%</div>
        <div class="stat-label">تحسن في التصميم</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-users"></i>
        </div>
        <div class="stat-number">95%</div>
        <div class="stat-label">رضا المستخدمين</div>
    </div>
</div>

<!-- قسم التعليمات -->
<div class="tasks-container">
    <h3 class="text-center mb-4">
        <i class="fas fa-question-circle me-2"></i>كيفية الاستخدام
    </h3>
    
    <div class="row">
        <div class="col-md-6">
            <div class="alert alert-info">
                <h5><i class="fas fa-mouse-pointer me-2"></i>فتح القائمة الجانبية</h5>
                <p class="mb-0">انقر على الزر الأزرق الدائري على الجانب الأيمن من الشاشة</p>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="alert alert-success">
                <h5><i class="fas fa-link me-2"></i>التنقل بين الصفحات</h5>
                <p class="mb-0">استخدم الروابط في القائمة الجانبية للانتقال بين صفحات الموقع</p>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="alert alert-warning">
                <h5><i class="fas fa-times me-2"></i>إغلاق القائمة</h5>
                <p class="mb-0">انقر خارج القائمة أو اضغط مفتاح Escape لإغلاقها</p>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="alert alert-primary">
                <h5><i class="fas fa-user-circle me-2"></i>معلومات المستخدم</h5>
                <p class="mb-0">تظهر معلومات المستخدم وصورته في أعلى القائمة الجانبية</p>
            </div>
        </div>
    </div>
</div>

<!-- قسم الاختبار -->
<div class="budget-container">
    <h3 class="text-center mb-4">
        <i class="fas fa-test-tube me-2"></i>اختبار الوظائف
    </h3>
    
    <div class="text-center">
        <button class="btn btn-primary btn-lg me-3" onclick="testSidebar()">
            <i class="fas fa-play me-2"></i>اختبار القائمة الجانبية
        </button>
        
        <button class="btn btn-success btn-lg me-3" onclick="testResponsive()">
            <i class="fas fa-mobile-alt me-2"></i>اختبار التجاوب
        </button>
        
        <button class="btn btn-warning btn-lg" onclick="showFeatures()">
            <i class="fas fa-list me-2"></i>عرض جميع المميزات
        </button>
    </div>
    
    <div id="testResults" class="mt-4" style="display: none;">
        <div class="alert alert-success">
            <h5><i class="fas fa-check-circle me-2"></i>نتائج الاختبار</h5>
            <ul id="testList" class="mb-0"></ul>
        </div>
    </div>
</div>

<!-- جدول المقارنة -->
<div class="hero-section">
    <h3 class="text-center mb-4">
        <i class="fas fa-balance-scale me-2"></i>مقارنة التصميم القديم والجديد
    </h3>
    
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>الخاصية</th>
                    <th>التصميم القديم</th>
                    <th>التصميم الجديد</th>
                    <th>التحسن</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><i class="fas fa-expand me-2"></i>مساحة المحتوى</td>
                    <td><span class="badge bg-warning">محدودة</span></td>
                    <td><span class="badge bg-success">كاملة</span></td>
                    <td><i class="fas fa-arrow-up text-success"></i> 30%</td>
                </tr>
                <tr>
                    <td><i class="fas fa-mouse-pointer me-2"></i>سهولة التنقل</td>
                    <td><span class="badge bg-info">جيدة</span></td>
                    <td><span class="badge bg-success">ممتازة</span></td>
                    <td><i class="fas fa-arrow-up text-success"></i> 25%</td>
                </tr>
                <tr>
                    <td><i class="fas fa-mobile-alt me-2"></i>التجاوب</td>
                    <td><span class="badge bg-warning">متوسط</span></td>
                    <td><span class="badge bg-success">ممتاز</span></td>
                    <td><i class="fas fa-arrow-up text-success"></i> 40%</td>
                </tr>
                <tr>
                    <td><i class="fas fa-tachometer-alt me-2"></i>الأداء</td>
                    <td><span class="badge bg-info">جيد</span></td>
                    <td><span class="badge bg-success">ممتاز</span></td>
                    <td><i class="fas fa-arrow-up text-success"></i> 35%</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
function testSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    
    if (sidebar && sidebarToggle) {
        sidebarToggle.click();
        
        setTimeout(() => {
            showTestResult('تم فتح القائمة الجانبية بنجاح');
            
            setTimeout(() => {
                sidebarToggle.click();
                showTestResult('تم إغلاق القائمة الجانبية بنجاح');
            }, 2000);
        }, 500);
    } else {
        showTestResult('خطأ: لم يتم العثور على القائمة الجانبية', 'error');
    }
}

function testResponsive() {
    const mainContent = document.getElementById('mainContent');
    const currentWidth = window.innerWidth;
    
    showTestResult(`عرض الشاشة الحالي: ${currentWidth}px`);
    
    if (currentWidth <= 768) {
        showTestResult('تم اكتشاف جهاز محمول - التصميم متجاوب');
    } else {
        showTestResult('تم اكتشاف جهاز سطح مكتب - التصميم متجاوب');
    }
}

function showFeatures() {
    const features = [
        'قائمة جانبية شاملة',
        'معلومات المستخدم في القائمة',
        'تصميم متجاوب بالكامل',
        'إزالة القائمة العلوية',
        'تحسين الأداء',
        'تأثيرات بصرية متطورة',
        'سهولة التنقل',
        'تصميم نظيف ومرتب'
    ];
    
    features.forEach((feature, index) => {
        setTimeout(() => {
            showTestResult(feature);
        }, index * 200);
    });
}

function showTestResult(message, type = 'success') {
    const testResults = document.getElementById('testResults');
    const testList = document.getElementById('testList');
    
    testResults.style.display = 'block';
    
    const li = document.createElement('li');
    li.innerHTML = `<i class="fas fa-${type === 'error' ? 'times text-danger' : 'check text-success'} me-2"></i>${message}`;
    testList.appendChild(li);
    
    // تنظيف القائمة بعد 10 ثوان
    setTimeout(() => {
        if (testList.children.length > 8) {
            testList.removeChild(testList.firstChild);
        }
    }, 10000);
}

// إضافة تأثيرات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير على البطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // تأثير على الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
            this.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.2)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
});
</script>