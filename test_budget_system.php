<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

// اختبار إدراج موازنة تجريبية
function testBudgetInsertion($conn) {
    try {
        $stmt = $conn->prepare("INSERT INTO Budgets (Title, TotalAmount, SpentAmount) VALUES (?, ?, 0)");
        $result = $stmt->execute(['موازنة تجريبية', 100000]);
        return $result ? $conn->lastInsertId() : false;
    } catch (Exception $e) {
        return false;
    }
}

// اختبار إدراج بند تجريبي
function testCategoryInsertion($conn, $budgetId) {
    try {
        $stmt = $conn->prepare("INSERT INTO Categories (Name, BudgetID, Amount, SpentAmount) VALUES (?, ?, ?, 0)");
        $result = $stmt->execute(['بند تجريبي', $budgetId, 50000]);
        return $result ? $conn->lastInsertId() : false;
    } catch (Exception $e) {
        return false;
    }
}

// اختبار خصم من الموازنة والبند
function testExpenseDeduction($conn, $budgetId, $categoryId, $amount) {
    try {
        $conn->beginTransaction();
        
        // التحقق من الرصيد المتاح
        $budgetStmt = $conn->prepare("SELECT TotalAmount, SpentAmount FROM Budgets WHERE ID = ?");
        $budgetStmt->execute([$budgetId]);
        $budget = $budgetStmt->fetch(PDO::FETCH_ASSOC);
        
        $categoryStmt = $conn->prepare("SELECT Amount, SpentAmount FROM Categories WHERE ID = ?");
        $categoryStmt->execute([$categoryId]);
        $category = $categoryStmt->fetch(PDO::FETCH_ASSOC);
        
        $budgetRemaining = $budget['TotalAmount'] - $budget['SpentAmount'];
        $categoryRemaining = $category['Amount'] - $category['SpentAmount'];
        
        if ($amount > $budgetRemaining || $amount > $categoryRemaining) {
            throw new Exception("المبلغ يتجاوز الرصيد المتاح");
        }
        
        // خصم من الموازنة
        $updateBudget = $conn->prepare("UPDATE Budgets SET SpentAmount = SpentAmount + ? WHERE ID = ?");
        $updateBudget->execute([$amount, $budgetId]);
        
        // خصم من البند
        $updateCategory = $conn->prepare("UPDATE Categories SET SpentAmount = SpentAmount + ? WHERE ID = ?");
        $updateCategory->execute([$amount, $categoryId]);
        
        $conn->commit();
        return true;
        
    } catch (Exception $e) {
        $conn->rollBack();
        return false;
    }
}

// تنظيف البيانات التجريبية
function cleanupTestData($conn, $budgetId) {
    try {
        $conn->prepare("DELETE FROM Categories WHERE BudgetID = ?")->execute([$budgetId]);
        $conn->prepare("DELETE FROM Budgets WHERE ID = ?")->execute([$budgetId]);
        return true;
    } catch (Exception $e) {
        return false;
    }
}

$testResults = [];

// تشغيل الاختبارات
if (isset($_POST['run_tests'])) {
    // اختبار 1: إدراج موازنة
    $budgetId = testBudgetInsertion($conn);
    $testResults['budget_insertion'] = $budgetId !== false;
    
    if ($budgetId) {
        // اختبار 2: إدراج بند
        $categoryId = testCategoryInsertion($conn, $budgetId);
        $testResults['category_insertion'] = $categoryId !== false;
        
        if ($categoryId) {
            // اختبار 3: خصم صحيح
            $testResults['valid_deduction'] = testExpenseDeduction($conn, $budgetId, $categoryId, 10000);
            
            // اختبار 4: خصم يتجاوز الرصيد
            $testResults['invalid_deduction'] = !testExpenseDeduction($conn, $budgetId, $categoryId, 100000);
        }
        
        // تنظيف البيانات
        $testResults['cleanup'] = cleanupTestData($conn, $budgetId);
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار نظام الموازنات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
<?php include 'header.php'; ?>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-vial me-2"></i>اختبار نظام الموازنات والصرفيات</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted">هذا الاختبار يتحقق من صحة عمل نظام خصم الرصيد من الموازنات والبنود.</p>
                    
                    <form method="POST">
                        <button type="submit" name="run_tests" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-play me-2"></i>تشغيل الاختبارات
                        </button>
                    </form>
                    
                    <?php if (!empty($testResults)): ?>
                        <hr class="my-4">
                        <h5>نتائج الاختبارات:</h5>
                        
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-plus-circle me-2"></i>إدراج موازنة تجريبية</span>
                                <?php if ($testResults['budget_insertion']): ?>
                                    <span class="badge bg-success"><i class="fas fa-check"></i> نجح</span>
                                <?php else: ?>
                                    <span class="badge bg-danger"><i class="fas fa-times"></i> فشل</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-tag me-2"></i>إدراج بند تجريبي</span>
                                <?php if ($testResults['category_insertion']): ?>
                                    <span class="badge bg-success"><i class="fas fa-check"></i> نجح</span>
                                <?php else: ?>
                                    <span class="badge bg-danger"><i class="fas fa-times"></i> فشل</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-minus-circle me-2"></i>خصم صحيح من الرصيد</span>
                                <?php if ($testResults['valid_deduction']): ?>
                                    <span class="badge bg-success"><i class="fas fa-check"></i> نجح</span>
                                <?php else: ?>
                                    <span class="badge bg-danger"><i class="fas fa-times"></i> فشل</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-shield-alt me-2"></i>منع خصم يتجاوز الرصيد</span>
                                <?php if ($testResults['invalid_deduction']): ?>
                                    <span class="badge bg-success"><i class="fas fa-check"></i> نجح</span>
                                <?php else: ?>
                                    <span class="badge bg-danger"><i class="fas fa-times"></i> فشل</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-trash me-2"></i>تنظيف البيانات التجريبية</span>
                                <?php if ($testResults['cleanup']): ?>
                                    <span class="badge bg-success"><i class="fas fa-check"></i> نجح</span>
                                <?php else: ?>
                                    <span class="badge bg-danger"><i class="fas fa-times"></i> فشل</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php 
                        $allPassed = array_reduce($testResults, function($carry, $result) {
                            return $carry && $result;
                        }, true);
                        ?>
                        
                        <div class="alert <?= $allPassed ? 'alert-success' : 'alert-warning' ?> mt-4">
                            <?php if ($allPassed): ?>
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>ممتاز!</strong> جميع الاختبارات نجحت. النظام يعمل بشكل صحيح.
                            <?php else: ?>
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>تحذير!</strong> بعض الاختبارات فشلت. يرجى مراجعة الكود.
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div></body>
</html>
