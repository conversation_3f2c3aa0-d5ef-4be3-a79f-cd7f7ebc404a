-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: May 14, 2025 at 02:13 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `efinance`
--

-- --------------------------------------------------------

--
-- Table structure for table `budget`
--

CREATE TABLE `budget` (
  `ID` int(11) NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `Date` date NOT NULL,
  `Description` text DEFAULT NULL,
  `File` varchar(255) DEFAULT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `budgetitems`
--

CREATE TABLE `budgetitems` (
  `ID` int(11) NOT NULL,
  `BudgetID` int(11) DEFAULT NULL,
  `Category` varchar(255) NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `CreatedAt` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `budgets`
--

CREATE TABLE `budgets` (
  `ID` int(11) NOT NULL,
  `Title` varchar(255) NOT NULL,
  `TotalAmount` decimal(12,2) NOT NULL,
  `SpentAmount` decimal(12,2) DEFAULT 0.00,
  `Notes` text DEFAULT NULL,
  `CreatedAt` datetime DEFAULT current_timestamp(),
  `UsedAmount` decimal(10,2) NOT NULL DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `budgets`
--

INSERT INTO `budgets` (`ID`, `Title`, `TotalAmount`, `SpentAmount`, `Notes`, `CreatedAt`, `UsedAmount`) VALUES
(8, 'موازنة ١', 100000.00, 25000.00, NULL, '2025-05-13 08:17:44', 0.00),
(9, 'موازنة ٢', 25000.00, 0.00, NULL, '2025-05-13 08:17:52', 0.00);

-- --------------------------------------------------------

--
-- Table structure for table `budgetsupport`
--

CREATE TABLE `budgetsupport` (
  `ID` int(11) NOT NULL,
  `Amount` decimal(15,2) NOT NULL,
  `Date` date NOT NULL,
  `Description` text DEFAULT NULL,
  `File` varchar(255) DEFAULT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `budgetsupport`
--

INSERT INTO `budgetsupport` (`ID`, `Amount`, `Date`, `Description`, `File`, `CreatedAt`) VALUES
(10, 3794284.00, '2024-12-31', 'مبلغ الميزانية الحالية رصيد اول المدة في 2025', NULL, '2025-01-04 03:24:26');

-- --------------------------------------------------------

--
-- Table structure for table `budgettotal`
--

CREATE TABLE `budgettotal` (
  `ID` int(11) NOT NULL,
  `Total` decimal(15,2) NOT NULL DEFAULT 0.00
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `budgettotal`
--

INSERT INTO `budgettotal` (`ID`, `Total`) VALUES
(2, 0.00),
(1, 3794284.00);

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `ID` int(11) NOT NULL,
  `Name` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`ID`, `Name`) VALUES
(2, 'بند مساعدات'),
(3, 'بند تشغيل');

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `ID` int(11) NOT NULL,
  `Name` varchar(255) NOT NULL,
  `NationalID` varchar(20) NOT NULL,
  `AccountNumber` varchar(20) NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `Tax` decimal(10,2) NOT NULL,
  `Net` decimal(10,2) GENERATED ALWAYS AS (`Amount` - `Tax`) STORED,
  `PaymentDuration` int(11) NOT NULL,
  `StartDate` date NOT NULL,
  `EndDate` date NOT NULL,
  `FilePath` varchar(255) DEFAULT NULL,
  `AuthorizationNumber` varchar(50) NOT NULL,
  `TransferNumber` varchar(50) DEFAULT NULL,
  `DocumentCount` int(11) DEFAULT NULL,
  `Notes` text DEFAULT NULL,
  `Category` enum('مساعدات','تشغيل','تشغيل شهري') NOT NULL,
  `CardExpiryDate` date DEFAULT NULL,
  `CardExpiry` varchar(255) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`ID`, `Name`, `NationalID`, `AccountNumber`, `Amount`, `Tax`, `PaymentDuration`, `StartDate`, `EndDate`, `FilePath`, `AuthorizationNumber`, `TransferNumber`, `DocumentCount`, `Notes`, `Category`, `CardExpiryDate`, `CardExpiry`) VALUES
(5, 'سيد يحيي', '*********', '123321', 2000.00, 200.00, 12, '2025-01-04', '2025-01-04', 'uploads/customers_report.pdf', '2', '2', 12, '', 'مساعدات', NULL, ''),
(23, 'ahmed sayed', '**************', '**************', 50000.00, 5000.00, 12, '0000-00-00', '0000-00-00', NULL, 'AUTH124', 'TRF457', 1, 'no notes', 'مساعدات', NULL, '12/12/2025'),
(8, 'عمر سيد', '**********', '********', 10000.00, 1000.00, 12, '2025-01-07', '2025-01-07', '', '1', '1', 1, '', 'مساعدات', NULL, ''),
(9, 'نور عاطف', '**********', '********', 10000.00, 1000.00, 12, '2025-01-07', '2025-01-07', '', '1', '1', 1, '', 'تشغيل', NULL, NULL),
(10, 'احمد يحيي سيد', '***********', '********', 900.00, 0.00, 12, '2025-01-08', '2025-01-08', '', '12', '1222', 11, '', 'تشغيل', NULL, NULL),
(11, 'ياسر رمضان ', '2917124124', '12412414', 15000.00, 0.00, 6, '2025-01-01', '2025-06-30', '', '1245', '2500', 1, '', 'تشغيل شهري', NULL, NULL),
(19, 'ahmed tarek', '*********12345', '454545', 10000.00, 0.00, 12, '2025-03-18', '2025-03-18', '', '********', '********', 10, '', 'مساعدات', NULL, NULL),
(20, 'احمد عمر سيد', '********111111', '111111', 10000.00, 0.00, 12, '2025-04-01', '2025-04-30', '', '1111', '11111', 1, '', 'مساعدات', NULL, '2025-04-01'),
(21, 'محمد سيد ', '29710040101015', '555555555555', 10000.00, 1000.00, 12, '2025-04-01', '2025-04-30', 'uploads/customers_report.pdf', '0', '0', 12, '', 'مساعدات', NULL, '2025-05-14'),
(22, 'أحمد محمد', '29811223344556', '*********01234', 10000.00, 100.00, 12, '0000-00-00', '0000-00-00', NULL, 'AUTH123', 'TRF456', 3, 'مساعدة طبية', 'مساعدات', NULL, '12/1/2025'),
(24, 'نهى سيد عبدالمقصود', '20250689101036', '1000089', 20000.00, 2000.00, 10, '2025-05-01', '2026-04-30', '', '20389', '516', 10, 'راتب', 'تشغيل شهري', NULL, '2025-12-31');

--
-- Triggers `customers`
--
DELIMITER $$
CREATE TRIGGER `check_NationalID` BEFORE INSERT ON `customers` FOR EACH ROW BEGIN
    IF CHAR_LENGTH(NEW.NationalID) != 14 OR NEW.NationalID REGEXP '[^0-9]' THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'الرقم القومي يجب أن يكون 14 رقمًا فقط ويحتوي على أرقام فقط.';
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `expenditures`
--

CREATE TABLE `expenditures` (
  `ID` int(11) NOT NULL,
  `CustomerID` int(11) NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `ExpenditureDate` date NOT NULL,
  `Notes` text DEFAULT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expenses`
--

CREATE TABLE `expenses` (
  `ID` int(11) NOT NULL,
  `CustomerID` int(11) NOT NULL,
  `BudgetID` int(11) DEFAULT NULL,
  `AuthorizationNumber` varchar(50) NOT NULL,
  `AccountNumber` varchar(50) NOT NULL,
  `Amount` decimal(15,2) NOT NULL,
  `Tax` decimal(15,2) NOT NULL,
  `NetAmount` decimal(15,2) GENERATED ALWAYS AS (`Amount` - `Tax`) STORED,
  `Date` date NOT NULL,
  `PaymentDate` date DEFAULT NULL,
  `TransferNumber` varchar(50) NOT NULL,
  `TransferType` enum('Swift','Internal','Letter') NOT NULL,
  `ExpenseNumber` varchar(50) NOT NULL,
  `File` varchar(255) DEFAULT NULL,
  `Notes` text DEFAULT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT current_timestamp(),
  `Description` text DEFAULT NULL,
  `Name` varchar(255) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `expenses`
--

INSERT INTO `expenses` (`ID`, `CustomerID`, `BudgetID`, `AuthorizationNumber`, `AccountNumber`, `Amount`, `Tax`, `Date`, `PaymentDate`, `TransferNumber`, `TransferType`, `ExpenseNumber`, `File`, `Notes`, `CreatedAt`, `Description`, `Name`) VALUES
(619, 0, 8, '1222', '1000089', 25000.00, 0.00, '2025-05-13', '2025-05-13', '0', '', 'EXP1747166133', NULL, '', '2025-05-13 19:55:33', '0', ''),
(618, 10, NULL, '12222', '12222', 8000.00, 800.00, '2025-05-13', '2025-05-13', '1212', '', 'EXP1747166047', NULL, '', '2025-05-13 19:54:07', '1212', ''),
(617, 0, 7, '211', '********', 1000.00, 100.00, '2025-05-12', '2025-05-12', '*********', '', 'EXP1747014171', NULL, '', '2025-05-12 01:42:51', '3', ''),
(616, 0, 7, '2', '2', 2000.00, 0.00, '2025-04-21', '2025-04-21', '2', 'Internal', 'EXP1745246375', NULL, '', '2025-04-21 14:39:35', '2', '');

-- --------------------------------------------------------

--
-- Table structure for table `logs`
--

CREATE TABLE `logs` (
  `ID` int(11) NOT NULL,
  `UserID` int(11) NOT NULL,
  `Action` varchar(255) NOT NULL,
  `Timestamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `ID` int(11) NOT NULL,
  `Title` varchar(255) NOT NULL,
  `Message` text DEFAULT NULL,
  `NotifyDate` date NOT NULL,
  `ToDate` date DEFAULT NULL,
  `EndDate` date DEFAULT NULL,
  `CreatedAt` datetime DEFAULT current_timestamp(),
  `Status` enum('لم يتم الحل','معلق','تم الحل') DEFAULT 'لم يتم الحل'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`ID`, `Title`, `Message`, `NotifyDate`, `ToDate`, `EndDate`, `CreatedAt`, `Status`) VALUES
(5, 'تغيير لون الخلفيه', 'محتارين نغير اللون ', '2025-05-12', NULL, NULL, '2025-05-12 19:58:21', ''),
(6, 'تغيير نوع الخط ', 'هنستني صاحبه دعاء تقولنا ايه احسن خط ', '2025-05-13', NULL, NULL, '2025-05-12 19:58:48', ''),
(7, 'رفع العملاء مرة واحدة', 'تم تعديل صفحة رفع العملاء واصبحت جاهزة لرفع العملاء مجموعات بدل من الاضافة عميل عميل ', '2025-05-13', NULL, NULL, '2025-05-13 08:11:17', ''),
(8, 'تعديل انشاء صرفية ', 'جاري العمل علي صفحة انشاء صرفية \r\nرفع صرفيات مجمعه\r\nالخصم من ارصده البنود والميزانيات', '2025-05-13', NULL, NULL, '2025-05-13 08:12:40', ''),
(9, 'تعديل صفحة التنبيهات', 'اضافة من تاريخ لتاريخ في صفحة التنبيهات', '2025-05-13', NULL, NULL, '2025-05-13 08:13:11', 'لم يتم الحل'),
(10, 'تحسين شكل القائمة', 'اضافة وتجميع الصفحات الرئيسية داخل قوائم منسدلة لتحسين الشكل وسرعة الوصول للمطلوب ', '2025-05-13', NULL, NULL, '2025-05-13 08:14:51', 'لم يتم الحل'),
(11, 'صفحة بحث الصرفيات', 'تعديل الصفحة ودالة البحث والتأكد من الربط بين جداول البيانات وجلب المعلومات المطلوبه وتحسين الشكل العام ', '2025-05-13', NULL, NULL, '2025-05-13 08:16:59', 'لم يتم الحل'),
(12, 'ارشفة ', 'ايصال الضريبه لشهور قديمه', '2025-05-31', NULL, NULL, '2025-05-13 22:13:59', 'لم يتم الحل'),
(14, 'تيست', 'تيست', '0000-00-00', '0000-00-00', NULL, '2025-05-14 11:38:57', ''),
(18, 'jdddddd', 'dddd', '2025-05-14', '2025-05-14', NULL, '2025-05-14 12:21:13', '');

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `ID` int(11) NOT NULL,
  `Type` enum('add','deduct') NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `Description` text DEFAULT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `userroles`
--

CREATE TABLE `userroles` (
  `ID` int(11) NOT NULL,
  `UserID` int(11) NOT NULL,
  `Role` enum('admin','viewer','editor') NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `ID` int(11) NOT NULL,
  `Username` varchar(100) NOT NULL,
  `Password` varchar(255) NOT NULL,
  `Role` enum('admin','editor','viewer') NOT NULL DEFAULT 'viewer',
  `ProfileImage` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`ID`, `Username`, `Password`, `Role`, `ProfileImage`) VALUES
(1, 'admin_user', '123', 'admin', NULL),
(4, 'admin', '$2y$10$fvLX/AK33h5aQbD7.jM5MuDw6MooxANQjZRXJacLmgt1XMqCkMHZK', 'admin', NULL),
(5, 'ahmed', '$2y$10$nyLL4xu9LbfniBT4n4TjzOpIbxhwZO4rKnIM6rtNxEZ1VaxpgKRPm', 'admin', '1747203757_default-avatar.png'),
(6, 'test', '$2y$10$niMF3dWpGB2hrzQM7Z0E8OBLhwQ/VJDmDEd9/ubFxhEmbWFTkAWUC', 'viewer', '1747023535_FAYRUZ.png'),
(7, 'doaa', '$2y$10$LIjOtvJJvMrcKVpIvWKsm.8P3VRMNVAkjXIGXrBEx.l/Jc01dGQzi', 'admin', NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `budget`
--
ALTER TABLE `budget`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `budgetitems`
--
ALTER TABLE `budgetitems`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `BudgetID` (`BudgetID`);

--
-- Indexes for table `budgets`
--
ALTER TABLE `budgets`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `budgetsupport`
--
ALTER TABLE `budgetsupport`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `budgettotal`
--
ALTER TABLE `budgettotal`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `expenditures`
--
ALTER TABLE `expenditures`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `CustomerID` (`CustomerID`);

--
-- Indexes for table `expenses`
--
ALTER TABLE `expenses`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `CustomerID` (`CustomerID`);

--
-- Indexes for table `logs`
--
ALTER TABLE `logs`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `UserID` (`UserID`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `userroles`
--
ALTER TABLE `userroles`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `UserID` (`UserID`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`ID`),
  ADD UNIQUE KEY `Username` (`Username`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `budget`
--
ALTER TABLE `budget`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `budgetitems`
--
ALTER TABLE `budgetitems`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `budgets`
--
ALTER TABLE `budgets`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `budgetsupport`
--
ALTER TABLE `budgetsupport`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `budgettotal`
--
ALTER TABLE `budgettotal`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `expenditures`
--
ALTER TABLE `expenditures`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `expenses`
--
ALTER TABLE `expenses`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=620;

--
-- AUTO_INCREMENT for table `logs`
--
ALTER TABLE `logs`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `userroles`
--
ALTER TABLE `userroles`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `budgetitems`
--
ALTER TABLE `budgetitems`
  ADD CONSTRAINT `budgetitems_ibfk_1` FOREIGN KEY (`BudgetID`) REFERENCES `budgets` (`ID`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
