<?php
session_start();
require 'db.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';

$today = date('Y-m-d');
$currentMonth = isset($_GET['month']) ? $_GET['month'] : date('Y-m');
$monthStart = $currentMonth . '-01';
$monthEnd = date('Y-m-t', strtotime($monthStart));

$filter = '';
$params = [];

if (!empty($_GET['search'])) {
    $filter .= " AND (Title LIKE ? OR Message LIKE ?)";
    $params[] = '%' . $_GET['search'] . '%';
    $params[] = '%' . $_GET['search'] . '%';
}

if (!empty($_GET['status'])) {
    $filter .= " AND Status = ?";
    $params[] = $_GET['status'];
}

// جلب التنبيهات التي تقع ضمن الشهر المحدد
$sql = "SELECT * FROM Notifications 
        WHERE NotifyDate <= ? AND EndDate >= ? $filter
        ORDER BY NotifyDate ASC";
$params = array_merge([$monthEnd, $monthStart], $params);
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

// إضافة تنبيه جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_notification']) && $isAdmin) {
    $title = $_POST['title'];
    $message = $_POST['message'];
    $notifyDate = $_POST['notify_date'];
    $endDate = $_POST['end_date'];

    $stmt = $conn->prepare("INSERT INTO Notifications (Title, Message, NotifyDate, EndDate) VALUES (?, ?, ?, ?)");
    $stmt->execute([$title, $message, $notifyDate, $endDate]);

    $_SESSION['success'] = "تمت إضافة التنبيه بنجاح.";
    header("Location: Add_Notification1.php");
    exit;
}

// حذف تنبيه
if (isset($_GET['delete']) && $isAdmin) {
    $id = $_GET['delete'];
    $stmt = $conn->prepare("DELETE FROM Notifications WHERE ID = ?");
    $stmt->execute([$id]);
    $_SESSION['success'] = "تم حذف التنبيه.";
    header("Location: Add_Notification1.php");
    exit;
}

// تغيير الحالة
if (isset($_POST['update_status']) && $isAdmin) {
    $id = $_POST['id'];
    $newStatus = $_POST['status'];
    $stmt = $conn->prepare("UPDATE Notifications SET Status = ? WHERE ID = ?");
    $stmt->execute([$newStatus, $id]);
    header("Location: Add_Notification1.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة التنبيهات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        .center-box { max-width: 1000px; margin: auto; margin-top: 40px; }
        .today-highlight { background-color: #d1ecf1 !important; }
    </style>
</head>
<body>
<?php include 'header.php'; ?>

<div class="center-box">
    <div class="card shadow">
        <div class="card-header bg-primary text-white text-center">
            <h4 class="mb-0">إدارة التنبيهات</h4>
        </div>
        <div class="card-body">

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success text-center">
                    <?= $_SESSION['success']; unset($_SESSION['success']); ?>
                </div>
            <?php endif; ?>

            <?php if ($isAdmin): ?>
            <form method="POST" class="row g-3 mb-4">
                <input type="hidden" name="add_notification" value="1">
                <div class="col-md-6">
                    <label>العنوان</label>
                    <input type="text" name="title" class="form-control" required>
                </div>
                <div class="col-md-3">
                    <label>من تاريخ</label>
                    <input type="date" name="notify_date" class="form-control" required>
                </div>
                <div class="col-md-3">
                    <label>إلى تاريخ</label>
                    <input type="date" name="end_date" class="form-control" required>
                </div>
                <div class="col-md-12">
                    <label>الوصف</label>
                    <textarea name="message" class="form-control" rows="3" required></textarea>
                </div>
                <div class="col-md-12 text-center">
                    <button type="submit" class="btn btn-success">إضافة تنبيه جديد</button>
                </div>
            </form>
            <?php endif; ?>

            <hr>

            <!-- شريط التصفية -->
            <form class="row g-2 mb-4" method="GET">
                <input type="hidden" name="month" value="<?= htmlspecialchars($currentMonth) ?>">
                <div class="col-md-6">
                    <input type="text" name="search" class="form-control" placeholder="بحث..." value="<?= $_GET['search'] ?? '' ?>">
                </div>
                <div class="col-md-4">
                    <select name="status" class="form-select">
                        <option value="">كل الحالات</option>
                        <option value="لم تنفذ" <?= (($_GET['status'] ?? '') == 'لم تنفذ') ? 'selected' : '' ?>>لم تنفذ</option>
                        <option value="تحت التنفيذ" <?= (($_GET['status'] ?? '') == 'تحت التنفيذ') ? 'selected' : '' ?>>تحت التنفيذ</option>
                        <option value="تم التنفيذ" <?= (($_GET['status'] ?? '') == 'تم التنفيذ') ? 'selected' : '' ?>>تم التنفيذ</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-secondary w-100">تصفية</button>
                </div>
            </form>

            <!-- تنقل بين الشهور -->
            <div class="d-flex justify-content-between mb-3">
                <?php
                $prevMonth = date('Y-m', strtotime("$currentMonth -1 month"));
                $nextMonth = date('Y-m', strtotime("$currentMonth +1 month"));
                ?>
                <a href="?month=<?= $prevMonth ?>" class="btn btn-outline-primary btn-sm">&larr; الشهر السابق</a>
                <strong><?= date('F Y', strtotime($currentMonth . '-01')) ?></strong>
                <a href="?month=<?= $nextMonth ?>" class="btn btn-outline-primary btn-sm">الشهر التالي &rarr;</a>
            </div>

            <!-- جدول التنبيهات -->
            <div class="table-responsive">
                <table class="table table-bordered text-center align-middle">
                    <thead class="table-dark">
                        <tr>
                            <th>العنوان</th>
                            <th>الوصف</th>
                            <th>من تاريخ</th>
                            <th>إلى تاريخ</th>
                            <th>الحالة</th>
                            <?php if ($isAdmin): ?><th>الخيارات</th><?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($notifications) > 0): ?>
                            <?php foreach ($notifications as $note): ?>
                                <?php
                                $isToday = ($today >= $note['NotifyDate'] && $today <= $note['EndDate']);
                                ?>
                                <tr class="<?= $isToday ? 'today-highlight' : '' ?>">
                                    <td><?= htmlspecialchars($note['Title']) ?></td>
                                    <td><?= nl2br(htmlspecialchars($note['Message'])) ?></td>
                                    <td><?= $note['NotifyDate'] ?></td>
                                    <td><?= $note['EndDate'] ?></td>
                                    <td>
                                        <?php if ($isAdmin): ?>
                                            <form method="POST" class="d-flex justify-content-center align-items-center gap-1">
                                                <input type="hidden" name="id" value="<?= $note['ID'] ?>">
                                                <select name="status" class="form-select form-select-sm w-auto">
                                                    <option <?= $note['Status'] == 'لم تنفذ' ? 'selected' : '' ?>>لم تنفذ</option>
                                                    <option <?= $note['Status'] == 'تحت التنفيذ' ? 'selected' : '' ?>>تحت التنفيذ</option>
                                                    <option <?= $note['Status'] == 'تم التنفيذ' ? 'selected' : '' ?>>تم التنفيذ</option>
                                                </select>
                                                <button name="update_status" class="btn btn-sm btn-secondary">حفظ</button>
                                            </form>
                                        <?php else: ?>
                                            <?= htmlspecialchars($note['Status']) ?>
                                        <?php endif; ?>
                                    </td>
                                    <?php if ($isAdmin): ?>
                                    <td>
                                        <a href="?delete=<?= $note['ID'] ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من الحذف؟');">حذف</a>
                                    </td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr><td colspan="<?= $isAdmin ? '6' : '5' ?>">لا توجد تنبيهات.</td></tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

        </div>
    </div>
</div></body>
</html>
