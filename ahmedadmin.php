<?php
session_start();
require 'db.php';

$error = '';
$success = '';

// رفع الصورة
$targetDir = "uploads/";
if (!is_dir($targetDir)) {
    mkdir($targetDir, 0777, true); // أنشئ مجلد الصور إن لم يكن موجود
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $role = $_POST['role'];
    $imageName = '';

    // التحقق من صحة المدخلات
    if (empty($username) || empty($password) || empty($confirm_password) || empty($role)) {
        $error = "يرجى ملء جميع الحقول.";
    } elseif ($password !== $confirm_password) {
        $error = "كلمتا المرور غير متطابقتين.";
    } else {
        // التحقق من وجود اسم المستخدم مسبقًا
        $stmt = $conn->prepare("SELECT * FROM Users WHERE Username = :username");
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        if ($stmt->fetch()) {
            $error = "اسم المستخدم موجود بالفعل.";
        } else {
            // رفع الصورة إذا تم رفعها
            if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
                $imageName = basename($_FILES['profile_image']['name']);
                $targetFilePath = $targetDir . time() . "_" . $imageName;
                move_uploaded_file($_FILES['profile_image']['tmp_name'], $targetFilePath);
            }

            // تشفير كلمة المرور
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // إدخال المستخدم الجديد
            $stmt = $conn->prepare("INSERT INTO Users (Username, Password, Role, ProfileImage) 
                                    VALUES (:username, :password, :role, :profile_image)");
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':password', $hashed_password);
            $stmt->bindParam(':role', $role);
            $stmt->bindParam(':profile_image', $targetFilePath);

            if ($stmt->execute()) {
                $success = "تم إنشاء الحساب بنجاح.";
            } else {
                $error = "حدث خطأ أثناء إنشاء الحساب.";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تسجيل جديد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-5">
    <h2 class="text-center">تسجيل حساب جديد</h2>

    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php elseif ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
    <?php endif; ?>

    <form method="POST" action="" enctype="multipart/form-data">
        <div class="mb-3">
            <label for="username" class="form-label">اسم المستخدم</label>
            <input type="text" class="form-control" id="username" name="username" required>
        </div>
        <div class="mb-3">
            <label for="password" class="form-label">كلمة المرور</label>
            <input type="password" class="form-control" id="password" name="password" required>
        </div>
        <div class="mb-3">
            <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
        </div>
        <div class="mb-3">
            <label for="role" class="form-label">الدور</label>
            <select name="role" id="role" class="form-select" required>
                <option value="user">مستخدم</option>
                <option value="admin">مدير</option>
            </select>
        </div>
        <div class="mb-3">
            <label for="profile_image" class="form-label">صورة الملف الشخصي</label>
            <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
        </div>
        <button type="submit" class="btn btn-success w-100">تسجيل</button>
        <div class="text-center mt-3">
            <a href="login.php">لديك حساب؟ سجل الدخول</a>
        </div>
    </form>
</div>
</body>
</html>
