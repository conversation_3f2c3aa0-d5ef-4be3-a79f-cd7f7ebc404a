<?php
session_start();
require 'db.php';

header("Content-Type: application/vnd.ms-excel");
header("Content-Disposition: attachment; filename=customers_export.xls");
header("Pragma: no-cache");
header("Expires: 0");

$query = "SELECT * FROM Customers";
$params = [];

if (!empty($_SESSION['filter_sql'])) {
    $query .= " WHERE " . $_SESSION['filter_sql'];
    $params = $_SESSION['filter_params'];
}

$stmt = $conn->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

// إنشاء الجدول
echo "<table border='1'>";
echo "<tr>
        <th>ID</th>
        <th>الاسم</th>
        <th>الرقم القومي</th>
        <th>رقم الحساب</th>
        <th>المبلغ</th>
        <th>الضريبة</th>
        <th>الصافي</th>
        <th>مدة الصرف</th>
        <th>تاريخ البداية</th>
        <th>تاريخ النهاية</th>
        <th>رقم الإذن</th>
        <th>رقم التحويل</th>
        <th>عدد المستندات</th>
        <th>تاريخ انتهاء الكارت</th>
        <th>الملاحظات</th>
        <th>البند</th>
      </tr>";

foreach ($results as $row) {
    echo "<tr>";
    echo "<td>{$row['ID']}</td>";
    echo "<td>{$row['Name']}</td>";
    echo "<td>{$row['NationalID']}</td>";
    echo "<td>{$row['AccountNumber']}</td>";
    echo "<td>{$row['Amount']}</td>";
    echo "<td>{$row['Tax']}</td>";
    echo "<td>{$row['Net']}</td>";
    echo "<td>{$row['PaymentDuration']}</td>";
    echo "<td>{$row['StartDate']}</td>";
    echo "<td>{$row['EndDate']}</td>";
    echo "<td>{$row['AuthorizationNumber']}</td>";
    echo "<td>{$row['TransferNumber']}</td>";
    echo "<td>{$row['DocumentCount']}</td>";
    echo "<td>{$row['CardExpiryDate']}</td>";
    echo "<td>{$row['Notes']}</td>";
    echo "<td>{$row['Category']}</td>";
    echo "</tr>";
}
echo "</table>";
?>
