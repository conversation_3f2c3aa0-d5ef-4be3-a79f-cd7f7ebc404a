<?php
session_start();
require 'db.php';

// التحقق من الصلاحيات
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: /e-finance/index");
    exit;
}

// التحقق من وجود ID للحذف
$id = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_id'])) {
    $id = intval($_POST['delete_id']);
} elseif (isset($_GET['id'])) {
    $id = intval($_GET['id']);
}

if ($id) {
    try {
        // التأكد من أن الصرفية موجودة
        $stmt = $conn->prepare("SELECT * FROM Expenses WHERE ID = :id");
        $stmt->execute(['id' => $id]);
        $expense = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$expense) {
            echo "<script>alert('الصرفية غير موجودة.'); window.location.href='search_exp.php';</script>";
            exit;
        }

        // تنفيذ عملية الحذف
        $stmt = $conn->prepare("DELETE FROM Expenses WHERE ID = :id");
        $stmt->execute(['id' => $id]);

        // التحقق مما إذا كان الحذف ناجحًا
        if ($stmt->rowCount() > 0) {
            // حذف الملف المرفق إذا كان موجوداً
            if (!empty($expense['File']) && file_exists($expense['File'])) {
                unlink($expense['File']);
            }
            echo "<script>alert('تم حذف الصرفية بنجاح!'); window.location.href='search_exp.php';</script>";
        } else {
            echo "<script>alert('حدث خطأ أثناء حذف الصرفية.'); window.location.href='search_exp.php';</script>";
        }
    } catch (PDOException $e) {
        echo "<script>alert('خطأ في قاعدة البيانات: " . addslashes($e->getMessage()) . "'); window.location.href='search_exp.php';</script>";
    }
} else {
    header("Location: search_exp.php");
    exit;
}
