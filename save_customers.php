<?php

require 'db.php'; // ملف الاتصال بقاعدة البيانات



if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    $names = $_POST['name'];

    $national_ids = $_POST['national_id'];

    $account_numbers = $_POST['account_number'];



    $stmt = $conn->prepare("INSERT INTO Customers (Name, NationalID, AccountNumber) VALUES (?, ?, ?)");



    foreach ($names as $index => $name) {

        $stmt->execute([$name, $national_ids[$index], $account_numbers[$index]]);

    }



    echo "<script>alert('تم إدخال البيانات بنجاح!'); window.location.href='upload_excel.php';</script>";

}

?>

