<?php
session_start();
require 'db.php'; // ملف الاتصال بقاعدة البيانات

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /login");
    exit;
}

// التحقق من صلاحيات المسؤول
$is_admin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';

$search_results = [];

// البحث
// البحث أو العرض الكامل
$search_results = [];

$query_parts = [];
$params = [];

// لو فيه بيانات بحث
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!empty($_POST['name'])) {
        $query_parts[] = "Name LIKE :name";
        $params[':name'] = '%' . $_POST['name'] . '%';
    }
    if (!empty($_POST['national_id'])) {
        $query_parts[] = "NationalID LIKE :national_id";
        $params[':national_id'] = '%' . $_POST['national_id'] . '%';
    }
    if (!empty($_POST['account_number'])) {
        $query_parts[] = "AccountNumber LIKE :account_number";
        $params[':account_number'] = '%' . $_POST['account_number'] . '%';
    }
}

if (!empty($_POST['start_date']) && !empty($_POST['end_date'])) {
    $query_parts[] = "StartDate BETWEEN :start_date AND :end_date";
    $params[':start_date'] = $_POST['start_date'];
    $params[':end_date'] = $_POST['end_date'];
} elseif (!empty($_POST['start_date'])) {
    $query_parts[] = "StartDate >= :start_date";
    $params[':start_date'] = $_POST['start_date'];
} elseif (!empty($_POST['end_date'])) {
    $query_parts[] = "StartDate <= :end_date";
    $params[':end_date'] = $_POST['end_date'];
}
if (!empty($_POST['card_expiry_start']) && !empty($_POST['card_expiry_end'])) {
    $query_parts[] = "CardExpiryDate BETWEEN :card_expiry_start AND :card_expiry_end";
    $params[':card_expiry_start'] = $_POST['card_expiry_start'];
    $params[':card_expiry_end'] = $_POST['card_expiry_end'];
} elseif (!empty($_POST['card_expiry_start'])) {
    $query_parts[] = "CardExpiryDate >= :card_expiry_start";
    $params[':card_expiry_start'] = $_POST['card_expiry_start'];
} elseif (!empty($_POST['card_expiry_end'])) {
    $query_parts[] = "CardExpiryDate <= :card_expiry_end";
    $params[':card_expiry_end'] = $_POST['card_expiry_end'];
}


$query = "SELECT * FROM Customers";
if (!empty($query_parts)) {
    $query .= " WHERE " . implode(" AND ", $query_parts);
}
$sort_by = $_GET['sort_by'] ?? 'ID';
$allowed_sort = ['ID', 'Name', 'Amount'];
$sort_by = in_array($sort_by, $allowed_sort) ? $sort_by : 'ID';
$query .= " ORDER BY $sort_by DESC";


$stmt = $conn->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->execute();
$search_results = $stmt->fetchAll(PDO::FETCH_ASSOC);


// حذف العميل
if (isset($_GET['delete_id']) && $is_admin) {
    $stmt = $conn->prepare("DELETE FROM Customers WHERE ID = :id");
    $stmt->bindParam(':id', $_GET['delete_id']);
    $stmt->execute();
    header("Location: search.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
<style>
    .table thead th {
        background-color: #343a40;
        color: #fff;
        vertical-align: middle;
    }
    .action-buttons a {
        margin: 2px;
    }
    .highlight {
        background-color: #fff3cd !important;
    }
    select#sort_by.form-select{
	background-position: left .75rem center;
	text-align: center;
}
</style>
</head>
<body>
<?php include 'header.php'; ?>
<div class="container mt-5">
    <h1 class="text-center">إدارة العملاء</h1>
    <form method="POST" class="mb-4">
        <div class="row g-3">
            <div class="col-md-4">
                <input type="text" name="name" class="form-control" placeholder="بحث بالاسم">
            </div>
            <div class="col-md-4">
                <input type="text" name="national_id" class="form-control" placeholder="بحث بالرقم القومي">
            </div>
            <div class="col-md-4">
                <input type="text" name="account_number" class="form-control" placeholder="بحث برقم الحساب">
            </div>
        </div>
        <div class="text-center mt-3">
    <button type="submit" class="btn btn-primary w-100">بحث</button>
</div>

    </form>
    <?php if (!empty($search_results)): ?>
<!--  قبل الجدول مباشرة داخل الصفحة -->
<form method="GET" class="mb-3 d-flex justify-content-between align-items-center">
<div class="row g-3 mt-3">
    <div class="col-md-6">
        <label for="start_date" class="form-label">من تاريخ</label>
        <input type="date" name="start_date" id="start_date" class="form-control">
    </div>
    <div class="col-md-6">
        <label for="end_date" class="form-label">إلى تاريخ</label>
        <input type="date" name="end_date" id="end_date" class="form-control">
    </div>
    <div class="col-md-6">
        <label for="card_expiry_start" class="form-label">تاريخ انتهاء الكارت من</label>
        <input type="date" name="card_expiry_start" id="card_expiry_start" class="form-control">
    </div>
    <div class="col-md-6">
        <label for="card_expiry_end" class="form-label">تاريخ انتهاء الكارت إلى</label>
        <input type="date" name="card_expiry_end" id="card_expiry_end" class="form-control">
    </div>
</div>

    <div class="input-group w-25">
        <label class="input-group-text" for="sort_by">فرز حسب</label>
        <select name="sort_by" id="sort_by" class="form-select" onchange="this.form.submit()">
            <option value="ID" <?= ($_GET['sort_by'] ?? '') === 'ID' ? 'selected' : '' ?>>ID</option>
            <option value="Name" <?= ($_GET['sort_by'] ?? '') === 'Name' ? 'selected' : '' ?>>الاسم</option>
            <option value="Amount" <?= ($_GET['sort_by'] ?? '') === 'Amount' ? 'selected' : '' ?>>المبلغ</option>
        </select>
    </div>
    <a href="export_excel.php" class="btn btn-success">تحميل Excel</a>
</form>
    <div class="table-responsive">
    <table class="table table-custom text-center">
        <thead>
            <tr>
                <th>ID</th>
                <th>الاسم</th>
                <th>الرقم القومي</th>
                <th>رقم الحساب</th>
                <th>المبلغ</th>
                <th>الضريبة</th>
                <th>الصافي</th>
                <th>مدة الصرف</th>
                <th>تاريخ البداية</th>
                <th>تاريخ النهاية</th>
                <th>رقم الإذن</th>
                <th>رقم التحويل</th>
                <th>عدد المستندات</th>
                <th>تاريخ انتهاء الكارت</th>
                <th>الملاحظات</th>
                <th>البند</th>
                <th>الملف</th>
                <?php if ($is_admin): ?>
                    <th>إجراءات</th>
                <?php endif; ?>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($search_results as $result): ?>
                <?php
                    $expiry = $result['CardExpiryDate'] ?? null;
                    $is_expiring = false;
                    if ($expiry) {
                        $expiry_date = new DateTime($expiry);
                        $today = new DateTime();
                        $interval = $today->diff($expiry_date)->days;
                        $is_expiring = ($expiry_date > $today && $interval <= 30);
                    }
                ?>
                <tr class="<?= $is_expiring ? 'table-warning' : '' ?>">
                    <td><?= $result['ID'] ?></td>
                    <td><?= $result['Name'] ?></td>
                    <td><?= $result['NationalID'] ?></td>
                    <td><?= $result['AccountNumber'] ?></td>
                    <td><?= $result['Amount'] ?></td>
                    <td><?= $result['Tax'] ?></td>
                    <td><?= $result['Net'] ?></td>
                    <td><?= $result['PaymentDuration'] ?></td>
                    <td><?= $result['StartDate'] ?></td>
                    <td><?= $result['EndDate'] ?></td>
                    <td><?= $result['AuthorizationNumber'] ?></td>
                    <td><?= $result['TransferNumber'] ?></td>
                    <td><?= $result['DocumentCount'] ?></td>
                    <td><?= $expiry ? $expiry : '-' ?></td>
                    <td><?= $result['Notes'] ?></td>
                    <td><?= $result['Category'] ?></td>
                    <td>
                        <?php if (!empty($result['FilePath'])): ?>
                            <a href="<?= $result['FilePath'] ?>" target="_blank" class="btn btn-sm btn-success">عرض</a>
                        <?php else: ?>
                            -
                        <?php endif; ?>
                    </td>
                    <?php if ($is_admin): ?>
                        <td class="action-buttons">
                            <a href="edit.php?id=<?= $result['ID'] ?>" class="btn btn-sm btn-warning">تعديل</a>
                            <a href="?delete_id=<?= $result['ID'] ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من الحذف؟')">حذف</a>
                        </td>
                    <?php endif; ?>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>


<?php else: ?>
    <p class="text-center">لا توجد نتائج.</p>
<?php endif; ?>


</div>
<?php include 'footer.php'; ?>
</body>
</html>
