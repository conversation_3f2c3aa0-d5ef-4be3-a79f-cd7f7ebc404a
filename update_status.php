<?php
session_start();
require 'db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['id'], $_POST['action'])) {
    $id = (int)$_POST['id'];
    $action = $_POST['action'];

    if ($action === 'mark_done') {
        $stmt = $conn->prepare("UPDATE Notifications SET Status = 'تم التنفيذ' WHERE ID = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

    } elseif ($action === 'postpone') {
        // ترحيل التاريخ ليوم تالي
        $stmt = $conn->prepare("UPDATE Notifications SET NotifyDate = DATE_ADD(NotifyDate, INTERVAL 1 DAY) WHERE ID = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
    }

    // الرجوع إلى نفس التاريخ السابق بعد التعديل
    $redirectDate = $_GET['date'] ?? date('Y-m-d');
    header("Location: index.php?date={$redirectDate}");
    exit;

} else {
    echo "طلب غير صالح.";
}
?>
