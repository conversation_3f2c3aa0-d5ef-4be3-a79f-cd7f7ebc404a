<?php
/**
 * ملف تطبيق التحديثات على جميع صفحات الموقع
 * يقوم بتحديث جميع الصفحات لتعمل مع التصميم الجديد
 */

// قائمة الصفحات التي تحتاج تحديث
$pages_to_update = [
    'dashboard.php',
    'search.php',
    'register.php',
    'customer_upload.php',
    'customer_search.php',
    'Expense_Management.php',
    'expense_search.php',
    'Budget_Management.php',
    'add_budget.php',
    'manage_users.php',
    'profile.php',
    'Add_Notification2.php'
];

$updated_pages = [];
$failed_pages = [];

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تطبيق التحديثات</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
    <style>
        body { 
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e3f2fd 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 30px;
        }
        .update-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #007bff; }
    </style>
</head>
<body>";

echo "<div class='container'>
    <div class='update-card'>
        <h1 class='text-center mb-4'>
            <i class='fas fa-sync-alt me-2'></i>تطبيق التحديثات على جميع الصفحات
        </h1>
        <div class='progress mb-4'>
            <div class='progress-bar' role='progressbar' style='width: 0%'></div>
        </div>
        <div id='log'>";

// دالة لتحديث صفحة واحدة
function updatePage($filename) {
    if (!file_exists($filename)) {
        return ['status' => 'error', 'message' => 'الملف غير موجود'];
    }
    
    $content = file_get_contents($filename);
    $original_content = $content;
    $changes_made = false;
    
    // التحديثات المطلوبة
    $updates = [
        // إزالة padding-top من body
        [
            'search' => '/padding-top:\s*\d+px;?/',
            'replace' => 'padding: 0;',
            'description' => 'إزالة padding-top من body'
        ],
        
        // تحديث container classes
        [
            'search' => '/<div class="container mt-4">/',
            'replace' => '<div class="main-content" id="mainContent">',
            'description' => 'تحديث container class'
        ],
        
        // تحديث container classes البديلة
        [
            'search' => '/<div class="container">/',
            'replace' => '<div class="main-content" id="mainContent">',
            'description' => 'تحديث container class البديل'
        ],
        
        // إضافة main-content class إذا لم تكن موجودة
        [
            'search' => '/(?<!main-content)(?<!id="mainContent")(<div[^>]*class="[^"]*container[^"]*"[^>]*>)/',
            'replace' => '<div class="main-content" id="mainContent">',
            'description' => 'إضافة main-content class'
        ]
    ];
    
    foreach ($updates as $update) {
        if (isset($update['search']) && isset($update['replace'])) {
            $new_content = preg_replace($update['search'], $update['replace'], $content);
            if ($new_content !== $content) {
                $content = $new_content;
                $changes_made = true;
                echo "<div class='success'><i class='fas fa-check me-2'></i>{$update['description']} في $filename</div>";
            }
        }
    }
    
    // حفظ الملف إذا تم إجراء تغييرات
    if ($changes_made) {
        // إنشاء نسخة احتياطية
        $backup_filename = $filename . '.backup.' . date('Y-m-d-H-i-s');
        file_put_contents($backup_filename, $original_content);
        
        // حفظ الملف المحدث
        if (file_put_contents($filename, $content)) {
            return [
                'status' => 'success', 
                'message' => 'تم التحديث بنجاح',
                'backup' => $backup_filename
            ];
        } else {
            return ['status' => 'error', 'message' => 'فشل في حفظ الملف'];
        }
    } else {
        return ['status' => 'info', 'message' => 'لا توجد تحديثات مطلوبة'];
    }
}

// تطبيق التحديثات على كل صفحة
$total_pages = count($pages_to_update);
$current_page = 0;

foreach ($pages_to_update as $page) {
    $current_page++;
    $progress = ($current_page / $total_pages) * 100;
    
    echo "<script>
        document.querySelector('.progress-bar').style.width = '{$progress}%';
        document.querySelector('.progress-bar').textContent = '{$current_page}/{$total_pages}';
    </script>";
    
    echo "<div class='mb-3'>
        <h5><i class='fas fa-file-code me-2'></i>معالجة: $page</h5>";
    
    $result = updatePage($page);
    
    switch ($result['status']) {
        case 'success':
            echo "<div class='success'><i class='fas fa-check-circle me-2'></i>{$result['message']}</div>";
            if (isset($result['backup'])) {
                echo "<div class='info'><i class='fas fa-save me-2'></i>تم إنشاء نسخة احتياطية: {$result['backup']}</div>";
            }
            $updated_pages[] = $page;
            break;
            
        case 'error':
            echo "<div class='error'><i class='fas fa-times-circle me-2'></i>{$result['message']}</div>";
            $failed_pages[] = $page;
            break;
            
        case 'info':
            echo "<div class='info'><i class='fas fa-info-circle me-2'></i>{$result['message']}</div>";
            break;
    }
    
    echo "</div>";
    flush();
    ob_flush();
}

// عرض النتائج النهائية
echo "</div>
    <div class='mt-4'>
        <h3><i class='fas fa-chart-bar me-2'></i>ملخص النتائج</h3>
        
        <div class='row'>
            <div class='col-md-4'>
                <div class='card text-center'>
                    <div class='card-body'>
                        <h2 class='success'>" . count($updated_pages) . "</h2>
                        <p>صفحات تم تحديثها</p>
                    </div>
                </div>
            </div>
            
            <div class='col-md-4'>
                <div class='card text-center'>
                    <div class='card-body'>
                        <h2 class='error'>" . count($failed_pages) . "</h2>
                        <p>صفحات فشل تحديثها</p>
                    </div>
                </div>
            </div>
            
            <div class='col-md-4'>
                <div class='card text-center'>
                    <div class='card-body'>
                        <h2 class='info'>" . $total_pages . "</h2>
                        <p>إجمالي الصفحات</p>
                    </div>
                </div>
            </div>
        </div>";

if (!empty($updated_pages)) {
    echo "<div class='mt-4'>
        <h4 class='success'><i class='fas fa-check-circle me-2'></i>الصفحات المحدثة:</h4>
        <ul>";
    foreach ($updated_pages as $page) {
        echo "<li class='success'>$page</li>";
    }
    echo "</ul></div>";
}

if (!empty($failed_pages)) {
    echo "<div class='mt-4'>
        <h4 class='error'><i class='fas fa-times-circle me-2'></i>الصفحات التي فشل تحديثها:</h4>
        <ul>";
    foreach ($failed_pages as $page) {
        echo "<li class='error'>$page</li>";
    }
    echo "</ul></div>";
}

echo "<div class='mt-4 text-center'>
        <a href='test_new_design.php' class='btn btn-primary btn-lg me-3'>
            <i class='fas fa-test-tube me-2'></i>اختبار التصميم الجديد
        </a>
        
        <a href='index.php' class='btn btn-success btn-lg'>
            <i class='fas fa-home me-2'></i>العودة للصفحة الرئيسية
        </a>
    </div>
    
    <div class='mt-4'>
        <div class='alert alert-info'>
            <h5><i class='fas fa-info-circle me-2'></i>ملاحظات مهمة:</h5>
            <ul class='mb-0'>
                <li>تم إنشاء نسخ احتياطية من جميع الملفات المحدثة</li>
                <li>يمكنك استعادة النسخ الأصلية في أي وقت</li>
                <li>تأكد من اختبار جميع الصفحات بعد التحديث</li>
                <li>في حالة وجود مشاكل، تواصل مع فريق التطوير</li>
            </ul>
        </div>
    </div>
    
    </div>
</div>

<script>
// إضافة تأثيرات بصرية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير على البطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 15px 35px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
    
    // تأثير على الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>

</body>
</html>";
?>
