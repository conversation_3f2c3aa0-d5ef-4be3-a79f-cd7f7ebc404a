<?php
/**
 * أداة حذف الفوتر من جميع صفحات الموقع
 * تقوم بإزالة include 'footer.php' من جميع الملفات
 */

// قائمة الصفحات التي تحتوي على الفوتر
$pages_with_footer = [
    'index.php',
    'dashboard.php',
    'search.php',
    'register.php',
    'customer_upload.php',
    'customer_search.php',
    'Expense_Management.php',
    'expense_search.php',
    'Budget_Management.php',
    'add_budget.php',
    'manage_users.php',
    'profile.php',
    'Add_Notification2.php',
    'Add_Notification1.php',
    'admin_dashboard.php',
    'create_exp.php',
    'Edit_Budget1.php',
    'Edit_Category.php',
    'edit_expense.php',
    'edit_notification.php',
    'edit_user.php',
    'ex.php',
    'expenses.php',
    'expenses1.php',
    'Expense_Management1.php',
    'Expense_Management2.php',
    'test_improvements.php',
    'test_footer_position.php',
    'test_budget_system.php',
    'test_new_design.php',
    'verify_updates.php'
];

$removed_count = 0;
$failed_count = 0;
$results = [];

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>حذف الفوتر</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
</head>
<body class='bg-light'>
<div class='container mt-5'>
    <div class='row justify-content-center'>
        <div class='col-md-10'>
            <div class='card shadow'>
                <div class='card-header bg-danger text-white'>
                    <h3 class='mb-0'><i class='fas fa-trash me-2'></i>حذف الفوتر من جميع الصفحات</h3>
                </div>
                <div class='card-body'>";

foreach ($pages_with_footer as $page) {
    $file_path = __DIR__ . '/' . $page;
    
    if (!file_exists($file_path)) {
        $results[] = [
            'page' => $page,
            'status' => 'not_found',
            'message' => 'الملف غير موجود'
        ];
        continue;
    }
    
    // قراءة محتوى الملف
    $content = file_get_contents($file_path);
    
    if ($content === false) {
        $results[] = [
            'page' => $page,
            'status' => 'read_error',
            'message' => 'خطأ في قراءة الملف'
        ];
        $failed_count++;
        continue;
    }
    
    // البحث عن include footer.php وحذفه
    $patterns = [
        '/\s*<\?php\s+include\s+[\'"]footer\.php[\'"];\s*\?>\s*/i',
        '/\s*include\s+[\'"]footer\.php[\'"];\s*/i',
        '/\s*require\s+[\'"]footer\.php[\'"];\s*/i',
        '/\s*require_once\s+[\'"]footer\.php[\'"];\s*/i'
    ];
    
    $original_content = $content;
    $found_footer = false;
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, '', $content);
            $found_footer = true;
        }
    }
    
    if (!$found_footer) {
        $results[] = [
            'page' => $page,
            'status' => 'no_footer',
            'message' => 'لا يحتوي على فوتر'
        ];
        continue;
    }
    
    // كتابة المحتوى المحدث
    if (file_put_contents($file_path, $content) !== false) {
        $results[] = [
            'page' => $page,
            'status' => 'success',
            'message' => 'تم حذف الفوتر بنجاح'
        ];
        $removed_count++;
    } else {
        $results[] = [
            'page' => $page,
            'status' => 'write_error',
            'message' => 'خطأ في كتابة الملف'
        ];
        $failed_count++;
    }
}

// عرض النتائج
echo "<div class='alert alert-info'>
        <h5><i class='fas fa-info-circle me-2'></i>ملخص العملية</h5>
        <ul class='mb-0'>
            <li>تم حذف الفوتر من <strong>$removed_count</strong> صفحة</li>
            <li>فشل في <strong>$failed_count</strong> صفحة</li>
            <li>إجمالي الصفحات المفحوصة: <strong>" . count($pages_with_footer) . "</strong></li>
        </ul>
      </div>";

echo "<div class='table-responsive'>
        <table class='table table-striped'>
            <thead class='table-dark'>
                <tr>
                    <th>الصفحة</th>
                    <th>الحالة</th>
                    <th>الرسالة</th>
                </tr>
            </thead>
            <tbody>";

foreach ($results as $result) {
    $badge_class = '';
    $icon = '';
    
    switch ($result['status']) {
        case 'success':
            $badge_class = 'bg-success';
            $icon = 'fas fa-check';
            break;
        case 'no_footer':
            $badge_class = 'bg-warning';
            $icon = 'fas fa-exclamation-triangle';
            break;
        case 'not_found':
            $badge_class = 'bg-secondary';
            $icon = 'fas fa-file-slash';
            break;
        default:
            $badge_class = 'bg-danger';
            $icon = 'fas fa-times';
            break;
    }
    
    echo "<tr>
            <td><code>{$result['page']}</code></td>
            <td><span class='badge $badge_class'><i class='$icon me-1'></i>{$result['status']}</span></td>
            <td>{$result['message']}</td>
          </tr>";
}

echo "</tbody></table></div>";

// إضافة أزرار للإجراءات التالية
echo "<div class='mt-4'>
        <h5>الإجراءات التالية:</h5>
        <div class='btn-group' role='group'>
            <a href='index.php' class='btn btn-primary'>
                <i class='fas fa-home me-1'></i>العودة للرئيسية
            </a>
            <button class='btn btn-danger' onclick='deleteFooterFiles()'>
                <i class='fas fa-trash me-1'></i>حذف ملفات الفوتر
            </button>
            <button class='btn btn-success' onclick='location.reload()'>
                <i class='fas fa-redo me-1'></i>إعادة تشغيل
            </button>
        </div>
      </div>";

echo "</div></div></div></div></div>

<script>
function deleteFooterFiles() {
    if (confirm('هل أنت متأكد من حذف ملفات الفوتر؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        // إرسال طلب لحذف ملفات الفوتر
        fetch('delete_footer_files.php', {
            method: 'POST'
        })
        .then(response => response.text())
        .then(data => {
            alert('تم حذف ملفات الفوتر');
            location.reload();
        })
        .catch(error => {
            alert('حدث خطأ أثناء حذف الملفات');
        });
    }
}
</script>

</body>
</html>";
?>
