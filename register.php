<?php
session_start();
require 'db.php'; // ملف الاتصال بقاعدة البيانات

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'];
    $national_id = $_POST['national_id'];
    $account_number = $_POST['account_number'];
    $amount = $_POST['amount'];
    $tax = $_POST['tax'];
    $net = $_POST['net'];
    $duration = $_POST['duration'];
    $start_date = $_POST['start_date'];
    $end_date = $_POST['end_date'];
    $auth_number = $_POST['auth_number'];
    $transfer_number = $_POST['transfer_number'];
    $document_count = $_POST['document_count'];
    $notes = $_POST['notes'];
    $category = $_POST['category'];
    $file_path = '';
    $card_expiry = $_POST['card_expiry'];

    // رفع الملف إذا تم اختياره
    if (!empty($_FILES['file']['name'])) {
        $target_dir = "uploads/";
        $file_path = $target_dir . basename($_FILES["file"]["name"]);
        move_uploaded_file($_FILES["file"]["tmp_name"], $file_path);
    }

   $stmt = $conn->prepare("INSERT INTO Customers 
    (Name, NationalID, AccountNumber, Amount, Tax, Net, PaymentDuration, StartDate, EndDate, FilePath, AuthorizationNumber, TransferNumber, DocumentCount, Notes, Category, CardExpiry) 
    VALUES 
    (:name, :national_id, :account_number, :amount, :tax, :net, :duration, :start_date, :end_date, :file_path, :auth_number, :transfer_number, :document_count, :notes, :category, :card_expiry)");

    
    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':national_id', $national_id);
    $stmt->bindParam(':account_number', $account_number);
    $stmt->bindParam(':amount', $amount);
    $stmt->bindParam(':tax', $tax);
    $stmt->bindParam(':net', $net);
    $stmt->bindParam(':duration', $duration);
    $stmt->bindParam(':start_date', $start_date);
    $stmt->bindParam(':end_date', $end_date);
    $stmt->bindParam(':file_path', $file_path);
    $stmt->bindParam(':auth_number', $auth_number);
    $stmt->bindParam(':transfer_number', $transfer_number);
    $stmt->bindParam(':document_count', $document_count);
    $stmt->bindParam(':notes', $notes);
    $stmt->bindParam(':category', $category);
    $stmt->bindParam(':card_expiry', $card_expiry);
	
    if ($stmt->execute()) {
        echo "<script>alert('تم تسجيل العميل بنجاح!');</script>";
    } else {
        echo "<script>alert('خطأ أثناء التسجيل.');</script>";
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل العملاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .registration-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e3f2fd 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .registration-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .registration-header {
            background: linear-gradient(135deg, #000000, #333333);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .registration-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .registration-header h2 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
            position: relative;
            z-index: 1;
        }

        .registration-header .subtitle {
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1.1rem;
            position: relative;
            z-index: 1;
        }

        .form-floating {
            position: relative;
            margin-bottom: 20px;
        }

        .form-floating .form-control {
            height: 60px;
            padding: 20px 15px 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        .form-floating .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            background: white;
            transform: translateY(-2px);
        }

        .form-floating label {
            padding: 20px 15px 10px 15px;
            color: #6c757d;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .form-floating .form-control:focus ~ label,
        .form-floating .form-control:not(:placeholder-shown) ~ label {
            transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
            color: #007bff;
        }

        .submit-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            border-radius: 12px;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .submit-btn:hover::before {
            left: 100%;
        }

        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 123, 255, 0.4);
        }

        .form-section {
            padding: 30px;
        }

        .section-title {
            color: #000000;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 50px;
            height: 2px;
            background: linear-gradient(135deg, #007bff, #0056b3);
        }
    </style>
</head>
<body>
<?php include 'header.php'; ?>
<div class="main-content" id="mainContent">
    <div class="main-content" id="mainContent">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="registration-card">
                    <div class="registration-header">
                        <h2><i class="fas fa-user-plus me-3"></i>تسجيل عميل جديد</h2>
                        <p class="subtitle">أدخل بيانات العميل بدقة لضمان سلامة السجلات</p>
                    </div>

                    <div class="form-section">
                        <form method="POST" enctype="multipart/form-data" id="customerForm">
                            <!-- القسم الأول: البيانات الأساسية -->
                            <div class="section-title">
                                <i class="fas fa-user me-2"></i>البيانات الأساسية
                            </div>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" name="name" id="name" class="form-control" placeholder="الاسم" required>
                                        <label for="name"><i class="fas fa-user me-2"></i>الاسم</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" id="national_id" name="national_id" pattern="\d{14}" title="الرقم القومي يجب أن يحتوي على 14 رقم فقط" class="form-control" placeholder="الرقم القومي" required>
                                        <label for="national_id"><i class="fas fa-id-card me-2"></i>الرقم القومي</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" name="account_number" id="account_number" class="form-control" placeholder="رقم الحساب" required>
                                        <label for="account_number"><i class="fas fa-credit-card me-2"></i>رقم الحساب</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select name="category" id="category" class="form-select" required>
                                            <option value="">اختر البند</option>
                                            <option value="مساعدات">مساعدات</option>
                                            <option value="تشغيل">تشغيل</option>
                                            <option value="تشغيل شهري">تشغيل شهري</option>
                                        </select>
                                        <label for="category"><i class="fas fa-tags me-2"></i>البند</label>
                                    </div>
                                </div>
                            </div>
                            <!-- القسم الثاني: البيانات المالية -->
                            <div class="section-title mt-4">
                                <i class="fas fa-money-bill-wave me-2"></i>البيانات المالية
                            </div>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="number" name="amount" id="amount" class="form-control" placeholder="المبلغ" required>
                                        <label for="amount"><i class="fas fa-dollar-sign me-2"></i>المبلغ</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <select name="tax_rate" id="tax_rate" class="form-select" required>
                                            <option value="">اختر النسبة</option>
                                            <option value="1">1%</option>
                                            <option value="3">3%</option>
                                            <option value="5">5%</option>
                                            <option value="10">10%</option>
                                        </select>
                                        <label for="tax_rate"><i class="fas fa-percentage me-2"></i>نسبة الضريبة</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="number" name="tax" id="tax" class="form-control" placeholder="قيمة الضريبة" readonly>
                                        <label for="tax"><i class="fas fa-calculator me-2"></i>قيمة الضريبة</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="number" name="net" id="net" class="form-control" placeholder="الصافي" readonly>
                                        <label for="net"><i class="fas fa-coins me-2"></i>الصافي</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="number" name="duration" id="duration" class="form-control" placeholder="مدة الصرف" required>
                                        <label for="duration"><i class="fas fa-clock me-2"></i>مدة الصرف (بالأشهر)</label>
                                    </div>
                                </div>
                            </div>
                            <!-- القسم الثالث: بيانات التواريخ -->
                            <div class="section-title mt-4">
                                <i class="fas fa-calendar-alt me-2"></i>بيانات التواريخ
                            </div>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="date" name="start_date" id="start_date" class="form-control" placeholder="تاريخ البداية" required>
                                        <label for="start_date"><i class="fas fa-play me-2"></i>تاريخ البداية</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="date" name="end_date" id="end_date" class="form-control" placeholder="تاريخ النهاية" required>
                                        <label for="end_date"><i class="fas fa-stop me-2"></i>تاريخ النهاية</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="date" name="card_expiry" id="card_expiry" class="form-control" placeholder="تاريخ انتهاء كارت الميزة" required>
                                        <label for="card_expiry"><i class="fas fa-calendar-times me-2"></i>تاريخ انتهاء كارت الميزة</label>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <label for="file" class="form-label"><i class="fas fa-upload me-2"></i>رفع ملف</label>
                                    <input type="file" name="file" id="file" class="form-control" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                                    <div class="form-text">يمكنك رفع ملفات PDF, صور, أو مستندات Word</div>
                                </div>
                            </div>
                            <!-- القسم الرابع: بيانات إضافية -->
                            <div class="section-title mt-4">
                                <i class="fas fa-file-alt me-2"></i>بيانات إضافية
                            </div>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="text" name="auth_number" id="auth_number" class="form-control" placeholder="رقم الإذن" required>
                                        <label for="auth_number"><i class="fas fa-key me-2"></i>رقم الإذن</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="text" name="transfer_number" id="transfer_number" class="form-control" placeholder="رقم التحويل" required>
                                        <label for="transfer_number"><i class="fas fa-exchange-alt me-2"></i>رقم التحويل</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input type="number" name="document_count" id="document_count" class="form-control" placeholder="عدد المستندات" required>
                                        <label for="document_count"><i class="fas fa-file-invoice me-2"></i>عدد المستندات</label>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-floating">
                                        <textarea name="notes" id="notes" class="form-control" placeholder="ملاحظات" style="height: 100px"></textarea>
                                        <label for="notes"><i class="fas fa-sticky-note me-2"></i>ملاحظات</label>
                                    </div>
                                </div>
                            </div>
<script>
document.getElementById('card_expiry').addEventListener('change', function () {
    const expiryDate = new Date(this.value);
    const today = new Date();
    const oneMonthAhead = new Date();
    oneMonthAhead.setMonth(oneMonthAhead.getMonth() + 1);

    if (expiryDate < oneMonthAhead && expiryDate > today) {
        alert("⚠️ تنبيه: تاريخ انتهاء كارت الميزة أقل من شهر!");
    } else if (expiryDate <= today) {
        alert("❌ كارت الميزة منتهي بالفعل!");
    }
});
</script>

                            <!-- زر الإرسال -->
                            <div class="text-center mt-4">
                                <button type="submit" class="submit-btn">
                                    <i class="fas fa-user-plus me-2"></i>تسجيل العميل
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div><script>
    const amountInput = document.getElementById('amount');
    const taxRateSelect = document.getElementById('tax_rate');
    const taxInput = document.getElementById('tax');
    const netInput = document.getElementById('net');

    function calculateTax() {
        const amount = parseFloat(amountInput.value) || 0;
        const rate = parseFloat(taxRateSelect.value) || 0;
        const tax = (amount * rate) / 100;
        const net = amount - tax;

        taxInput.value = tax.toFixed(2);
        netInput.value = net.toFixed(2);
    }

    amountInput.addEventListener('input', calculateTax);
    taxRateSelect.addEventListener('change', calculateTax);
</script>

</body>
</html>
