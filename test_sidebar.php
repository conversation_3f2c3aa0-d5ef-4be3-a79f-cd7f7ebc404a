<?php
session_start();

// محاكاة بيانات المستخدم للاختبار
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'admin';
}

// محاكاة اتصال قاعدة البيانات للاختبار
$user = [
    'Username' => 'مستخدم تجريبي',
    'Role' => 'admin',
    'ProfileImage' => 'default-avatar.png'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القائمة الجانبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e3f2fd 100%);
            padding: 0;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .main-content {
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            min-height: 100vh;
            padding: 30px;
        }

        .main-content.sidebar-open {
            margin-right: 280px;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 2px solid #007bff;
            box-shadow: 0 4px 20px rgba(0, 123, 255, 0.1);
            transition: all 0.3s ease;
            padding: 15px 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #007bff, #0056b3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .test-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .test-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #0056b3);
        }

        .test-content {
            position: relative;
            overflow: hidden;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list i {
            color: #007bff;
            margin-left: 15px;
            width: 20px;
        }

        .btn-test {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 123, 255, 0.4);
            color: white;
        }
    </style>
</head>
<body>

<!-- تم إزالة القائمة العلوية - الاعتماد على القائمة الجانبية فقط -->

<?php include 'sidebar.php'; ?>

<div class="main-content" id="mainContent">
    <div class="test-content">
        <h1 class="text-center mb-4">
            <i class="fas fa-test-tube me-3"></i>اختبار القائمة الجانبية
        </h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3><i class="fas fa-list-check me-2"></i>المميزات المتاحة:</h3>
                <ul class="feature-list">
                    <li><i class="fas fa-bars"></i>زر تبديل القائمة الجانبية</li>
                    <li><i class="fas fa-mobile-alt"></i>تصميم متجاوب للأجهزة المحمولة</li>
                    <li><i class="fas fa-mouse-pointer"></i>إغلاق القائمة بالنقر خارجها</li>
                    <li><i class="fas fa-keyboard"></i>إغلاق القائمة بمفتاح Escape</li>
                    <li><i class="fas fa-eye"></i>تمييز الصفحة النشطة</li>
                    <li><i class="fas fa-paint-brush"></i>تأثيرات بصرية متقدمة</li>
                    <li><i class="fas fa-arrows-alt-h"></i>انزلاق سلس للقائمة</li>
                    <li><i class="fas fa-palette"></i>تصميم متناسق مع الموقع</li>
                </ul>
            </div>
            
            <div class="col-md-6">
                <h3><i class="fas fa-instructions me-2"></i>تعليمات الاستخدام:</h3>
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>كيفية الاستخدام:</h5>
                    <ol>
                        <li>انقر على الزر الأزرق الدائري على الجانب الأيمن</li>
                        <li>ستظهر القائمة الجانبية مع جميع صفحات الموقع</li>
                        <li>انقر على أي رابط للانتقال إلى الصفحة المطلوبة</li>
                        <li>يمكنك إغلاق القائمة بالنقر خارجها أو الضغط على Escape</li>
                    </ol>
                </div>
                
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>للأجهزة المحمولة:</h5>
                    <p>القائمة تتكيف تلقائياً مع الشاشات الصغيرة وتظهر بعرض كامل للشاشة.</p>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <button class="btn btn-test" onclick="testSidebar()">
                <i class="fas fa-play me-2"></i>اختبار القائمة الجانبية
            </button>
        </div>
        
        <div class="mt-5">
            <h3><i class="fas fa-code me-2"></i>الملفات المضافة:</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-file-code me-2"></i>sidebar.php</h5>
                            <p class="card-text">ملف القائمة الجانبية الرئيسي</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-file-css me-2"></i>sidebar-styles.css</h5>
                            <p class="card-text">ملف التصميم المتقدم للقائمة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title"><i class="fas fa-edit me-2"></i>header.php</h5>
                            <p class="card-text">تم تحديثه لدعم القائمة الجانبية</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    
    if (sidebar && sidebarToggle) {
        sidebarToggle.click();
        
        setTimeout(() => {
            alert('تم فتح القائمة الجانبية بنجاح! يمكنك الآن تجربة الروابط المختلفة.');
        }, 500);
    } else {
        alert('حدث خطأ في تحميل القائمة الجانبية. تأكد من تضمين ملف sidebar.php');
    }
}

// إضافة بعض التأثيرات التفاعلية
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
        });
    });
});
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
