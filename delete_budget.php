<?php
session_start();
require 'db.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: /login");
    exit;
}

if (isset($_GET['id'])) {
    $id = intval($_GET['id']);

    try {
        // جلب المبلغ الذي سيتم حذفه لتقليل الإجمالي
        $stmt = $conn->prepare("SELECT Amount FROM BudgetSupport WHERE ID = ?");
        $stmt->execute([$id]);
        $record = $stmt->fetch();

        if ($record) {
            $amount = $record['Amount'];

            // حذف السجل من جدول BudgetSupport
            $stmt = $conn->prepare("DELETE FROM BudgetSupport WHERE ID = ?");
            $stmt->execute([$id]);

            // تحديث إجمالي الموازنة في جدول BudgetTotal
            $stmt = $conn->prepare("UPDATE BudgetTotal SET Total = Total - ? WHERE ID = 1");
            $stmt->execute([$amount]);

            echo "<script>alert('تم الحذف بنجاح!'); window.location.href='index.php';</script>";
        } else {
            echo "<script>alert('السجل غير موجود.'); window.location.href='index.php';</script>";
        }
    } catch (PDOException $e) {
        echo "حدث خطأ أثناء الحذف: " . $e->getMessage();
    }
} else {
    header("Location: index.php");
}
?>
