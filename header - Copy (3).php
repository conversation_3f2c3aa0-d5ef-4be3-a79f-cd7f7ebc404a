<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'db.php';
$userID = $_SESSION['user_id'] ?? null;
$user = [];

if ($userID) {
    $stmt = $conn->prepare("SELECT Username, Role, ProfileImage FROM Users WHERE ID = :id");
    $stmt->bindParam(':id', $userID, PDO::PARAM_INT);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
}

// تحديد الصفحة الحالية لتمييزها
$current_page = basename($_SERVER['PHP_SELF']);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Finance</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }

        /* ألوان الهيدر الجديدة */
        .navbar {
            background-color: white !important;
            border-bottom: 1px solid #ddd;
        }

        .navbar a, .navbar-brand, .navbar-nav .nav-link {
            color: black !important;
        }

        .navbar-nav .nav-link {
            padding: 10px 20px;
            margin: 5px 5px;
            border-radius: 8px;
            transition: all 0.3s;
            font-weight: 500;
            text-align: center;
        }

        .navbar-nav .nav-link:hover {
            background-color: #e6e6e6;
            color: #007bff !important;
        }

        /* تمييز العنصر النشط */
        .navbar-nav .nav-link.active {
            background-color: #f0f0f0;
            color: #007bff !important;
            font-weight: bold;
            border: 1px solid #007bff;
        }

        .navbar-profile-img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            margin-left: 10px;
        }

        .user-info-container {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin-right: auto;
        }

        .user-info span {
            color: black;
            font-size: 16px;
            font-weight: 500;
        }

        /* توسيط القائمة عند الهواتف */
        @media (max-width: 768px) {
            .navbar-collapse {
                text-align: center;
            }

            .navbar-nav {
                flex-direction: row;
                justify-content: center;
                gap: 10px;
                flex-wrap: wrap;
            }

            .navbar-nav .nav-item {
                width: 100%;
            }

            .user-info-container {
                display: none; /* إخفاء معلومات المستخدم في الجوال */
            }
        }
    </style>
</head>
<body>

<nav class="navbar navbar-expand-lg">
    <div class="container-fluid">
        <div class="user-info-container">
            <?php if (!empty($user)): ?>
                <div class="user-info">
                    <a href="/e-finance/profile.php">
                        <img src="<?= !empty($user['ProfileImage']) ? 'uploads/' . $user['ProfileImage'] : 'default-user.png' ?>" class="navbar-profile-img" alt="الصورة الشخصية">
                    </a>
                    <span><?= htmlspecialchars($user['Username']) ?></span>
                </div>
            <?php endif; ?>
        </div>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar" aria-controls="mainNavbar" aria-expanded="false" aria-label="تبديل القائمة">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse justify-content-end" id="mainNavbar">
            <ul class="navbar-nav mx-auto mb-2 mb-lg-0 text-center">
                <li class="nav-item"><a class="nav-link <?= ($current_page == 'index.php') ? 'active' : '' ?>" href="/e-finance/index">الرئيسية</a></li>
                <li class="nav-item"><a class="nav-link <?= ($current_page == 'Add_Notification1.php') ? 'active' : '' ?>" href="/e-finance/Add_Notification1">ادارة التنبيهات</a></li>
                <li class="nav-item"><a class="nav-link <?= ($current_page == 'register.php') ? 'active' : '' ?>" href="/e-finance/register">تسجيل العملاء</a></li>
                <li class="nav-item"><a class="nav-link <?= ($current_page == 'search.php') ? 'active' : '' ?>" href="/e-finance/search">بحث العملاء</a></li>
                <li class="nav-item"><a class="nav-link <?= ($current_page == 'Expense_Management.php') ? 'active' : '' ?>" href="/e-finance/Expense_Management">ادارة الصرفيات</a></li>
                <li class="nav-item"><a class="nav-link <?= ($current_page == 'Budget_Management.php') ? 'active' : '' ?>" href="/e-finance/Budget_Management">ادارة الموازنات</a></li>
                <li class="nav-item"><a class="nav-link <?= ($current_page == 'expenses.php') ? 'active' : '' ?>" href="/e-finance/expenses">إنشاء صرفية</a></li>
                <li class="nav-item"><a class="nav-link <?= ($current_page == 'search_exp.php') ? 'active' : '' ?>" href="/e-finance/search_exp">بحث الصرفيات</a></li>
                <li class="nav-item"><a class="nav-link <?= ($current_page == 'add_budget.php') ? 'active' : '' ?>" href="/e-finance/add_budget">إضافة رصيد</a></li>
                <li class="nav-item"><a class="nav-link <?= ($current_page == 'dashboard.php') ? 'active' : '' ?>" href="/e-finance/dashboard">التقارير</a></li>
                <li class="nav-item"><a class="nav-link <?= ($current_page == 'manage_users.php') ? 'active' : '' ?>" href="/e-finance/manage_users">الصلاحيات</a></li>
                <li class="nav-item"><a class="nav-link text-danger" href="/e-finance/logout">تسجيل الخروج</a></li>
            </ul>
        </div>
    </div>
</nav>

<div class="container mt-4">
