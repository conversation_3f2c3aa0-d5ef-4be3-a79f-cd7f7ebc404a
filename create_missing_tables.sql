-- إن<PERSON><PERSON><PERSON> الجداول والأعمدة المطلوبة لتحسينات إدارة المهام
-- يمكن تشغيل هذا الملف بأمان حتى لو كانت بعض الأعمدة موجودة

-- التحقق من وجود قاعدة البيانات
USE efinance;

-- إضافة الأعمدة الجديدة للجدول الأساسي (مع تجاهل الأخطاء إذا كانت موجودة)
ALTER TABLE `notifications` 
ADD COLUMN `Priority` ENUM('منخفضة', 'متوسطة', 'عالية', 'عاجلة') DEFAULT 'متوسطة' AFTER `Status`;

ALTER TABLE `notifications` 
ADD COLUMN `Category` VARCHAR(100) DEFAULT NULL AFTER `Priority`;

ALTER TABLE `notifications` 
ADD COLUMN `AssignedTo` INT(11) DEFAULT NULL AFTER `Category`;

ALTER TABLE `notifications` 
ADD COLUMN `Progress` INT(3) DEFAULT 0 AFTER `AssignedTo`;

ALTER TABLE `notifications` 
ADD COLUMN `Notes` TEXT DEFAULT NULL AFTER `Progress`;

ALTER TABLE `notifications` 
ADD COLUMN `LastUpdated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `Notes`;

ALTER TABLE `notifications` 
ADD COLUMN `CreatedBy` INT(11) DEFAULT NULL AFTER `LastUpdated`;

-- إنشاء جدول التصنيفات
CREATE TABLE IF NOT EXISTS `task_categories` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) NOT NULL,
  `Description` text DEFAULT NULL,
  `Color` varchar(7) DEFAULT '#007bff',
  `Icon` varchar(50) DEFAULT 'fas fa-folder',
  `CreatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `IsActive` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_name` (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- إدراج التصنيفات الافتراضية
INSERT IGNORE INTO `task_categories` (`Name`, `Description`, `Color`, `Icon`) VALUES
('عام', 'مهام عامة', '#6c757d', 'fas fa-tasks'),
('مالية', 'مهام متعلقة بالشؤون المالية', '#28a745', 'fas fa-dollar-sign'),
('إدارية', 'مهام إدارية وتنظيمية', '#007bff', 'fas fa-briefcase'),
('تقنية', 'مهام تقنية وتطوير', '#17a2b8', 'fas fa-code'),
('عاجل', 'مهام عاجلة تحتاج متابعة فورية', '#dc3545', 'fas fa-exclamation-triangle');

-- إنشاء جدول التعليقات
CREATE TABLE IF NOT EXISTS `task_comments` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `TaskID` int(11) NOT NULL,
  `UserID` int(11) NOT NULL,
  `Comment` text NOT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `idx_task_id` (`TaskID`),
  KEY `idx_user_id` (`UserID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- إنشاء جدول سجل التغييرات
CREATE TABLE IF NOT EXISTS `task_history` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `TaskID` int(11) NOT NULL,
  `UserID` int(11) NOT NULL,
  `Action` varchar(100) NOT NULL,
  `OldValue` text DEFAULT NULL,
  `NewValue` text DEFAULT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `idx_task_id` (`TaskID`),
  KEY `idx_user_id` (`UserID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- إضافة الفهارس للأداء (مع تجاهل الأخطاء إذا كانت موجودة)
ALTER TABLE `notifications` ADD INDEX `idx_priority` (`Priority`);
ALTER TABLE `notifications` ADD INDEX `idx_category` (`Category`);
ALTER TABLE `notifications` ADD INDEX `idx_assigned_to` (`AssignedTo`);
ALTER TABLE `notifications` ADD INDEX `idx_created_by` (`CreatedBy`);
ALTER TABLE `notifications` ADD INDEX `idx_status_priority` (`Status`, `Priority`);

-- التحقق من وجود جدول المستخدمين وإضافة عمود FullName إذا لم يكن موجوداً
ALTER TABLE `users` ADD COLUMN `FullName` VARCHAR(255) DEFAULT NULL AFTER `Username`;
ALTER TABLE `users` ADD COLUMN `IsActive` TINYINT(1) DEFAULT 1 AFTER `FullName`;

COMMIT;
