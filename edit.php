<?php
session_start();
require 'db.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: /login");
    exit;
}

if ($_SESSION['role'] !== 'admin') {
    header("Location: /search");
    exit;
}

if (isset($_GET['id'])) {
    $customerID = intval($_GET['id']);
    $stmt = $conn->prepare("SELECT * FROM Customers WHERE ID = :id");
    $stmt->bindParam(':id', $customerID, PDO::PARAM_INT);
    $stmt->execute();
    $customer = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$customer) {
        echo "<script>alert('العميل غير موجود'); window.location.href = 'search.php';</script>";
        exit;
    }
} else {
    header("Location: /search");
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'];
    $nationalID = $_POST['national_id'];
    $accountNumber = $_POST['account_number'];
    $amount = $_POST['amount'];
    $tax = $_POST['tax'];
    $duration = $_POST['duration'];
    $startDate = $_POST['start_date'];
    $endDate = $_POST['end_date'];
    $authNumber = $_POST['auth_number'];
    $transferNumber = $_POST['transfer_number'];
    $documentCount = $_POST['document_count'];
    $notes = $_POST['notes'];
    $category = $_POST['category'];
    $cardExpiry = $_POST['card_expiry'];
    $filePath = $customer['FilePath'];

    if (!empty($_FILES['file']['name'])) {
        $target_dir = "uploads/";
        $filePath = $target_dir . basename($_FILES["file"]["name"]);
        move_uploaded_file($_FILES["file"]["tmp_name"], $filePath);
    }

    $stmt = $conn->prepare("UPDATE Customers SET 
        Name = :name,
        NationalID = :nationalID,
        AccountNumber = :accountNumber,
        Amount = :amount,
        Tax = :tax,
        PaymentDuration = :duration,
        StartDate = :startDate,
        EndDate = :endDate,
        AuthorizationNumber = :authNumber,
        TransferNumber = :transferNumber,
        DocumentCount = :documentCount,
        Notes = :notes,
        Category = :category,
        CardExpiry = :cardExpiry,
        FilePath = :filePath
        WHERE ID = :id");

    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':nationalID', $nationalID);
    $stmt->bindParam(':accountNumber', $accountNumber);
    $stmt->bindParam(':amount', $amount);
    $stmt->bindParam(':tax', $tax);
    $stmt->bindParam(':duration', $duration);
    $stmt->bindParam(':startDate', $startDate);
    $stmt->bindParam(':endDate', $endDate);
    $stmt->bindParam(':authNumber', $authNumber);
    $stmt->bindParam(':transferNumber', $transferNumber);
    $stmt->bindParam(':documentCount', $documentCount);
    $stmt->bindParam(':notes', $notes);
    $stmt->bindParam(':category', $category);
    $stmt->bindParam(':cardExpiry', $cardExpiry);
    $stmt->bindParam(':filePath', $filePath);
    $stmt->bindParam(':id', $customerID);

    if ($stmt->execute()) {
        echo "<script>alert('تم تعديل بيانات العميل بنجاح'); window.location.href = 'search.php';</script>";
    } else {
        echo "<script>alert('حدث خطأ أثناء التعديل');</script>";
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل بيانات العميل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-5">
    <h1 class="text-center">تعديل بيانات العميل</h1>
    <form method="POST" enctype="multipart/form-data" class="mt-4">
        <div class="mb-3">
            <label class="form-label">الاسم</label>
            <input type="text" name="name" class="form-control" value="<?= htmlspecialchars($customer['Name']) ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">الرقم القومي</label>
            <input type="text" name="national_id" class="form-control" value="<?= htmlspecialchars($customer['NationalID']) ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">رقم الحساب</label>
            <input type="text" name="account_number" class="form-control" value="<?= htmlspecialchars($customer['AccountNumber']) ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">المبلغ</label>
            <input type="number" name="amount" id="amount" class="form-control" value="<?= htmlspecialchars($customer['Amount']) ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">نسبة الضريبة</label>
            <select id="tax_percentage" class="form-select">
                <option value="">اختر النسبة</option>
                <option value="1">1%</option>
                <option value="3">3%</option>
                <option value="5">5%</option>
                <option value="10">10%</option>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">الضريبة</label>
            <input type="number" name="tax" id="tax" class="form-control" value="<?= htmlspecialchars($customer['Tax']) ?>" required>
        </div>
        <div class="mb-3">
            <label class="form-label">مدة الصرف</label>
            <input type="number" name="duration" class="form-control" value="<?= htmlspecialchars($customer['PaymentDuration']) ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">تاريخ البداية</label>
            <input type="date" name="start_date" class="form-control" value="<?= htmlspecialchars($customer['StartDate']) ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">تاريخ النهاية</label>
            <input type="date" name="end_date" class="form-control" value="<?= htmlspecialchars($customer['EndDate']) ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">تاريخ انتهاء كارت الميزة</label>
            <input type="date" name="card_expiry" class="form-control" value="<?= htmlspecialchars($customer['CardExpiry']) ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">رقم الإذن</label>
            <input type="text" name="auth_number" class="form-control" value="<?= htmlspecialchars($customer['AuthorizationNumber']) ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">رقم التحويل</label>
            <input type="text" name="transfer_number" class="form-control" value="<?= htmlspecialchars($customer['TransferNumber']) ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">عدد المستندات</label>
            <input type="number" name="document_count" class="form-control" value="<?= htmlspecialchars($customer['DocumentCount']) ?>">
        </div>
        <div class="mb-3">
            <label class="form-label">البند</label>
            <select name="category" class="form-select">
                <option value="مساعدات" <?= $customer['Category'] == 'مساعدات' ? 'selected' : '' ?>>مساعدات</option>
                <option value="تشغيل" <?= $customer['Category'] == 'تشغيل' ? 'selected' : '' ?>>تشغيل</option>
                <option value="تشغيل شهري" <?= $customer['Category'] == 'تشغيل شهري' ? 'selected' : '' ?>>تشغيل شهري</option>
            </select>
        </div>
        <div class="mb-3">
            <label class="form-label">ملاحظات</label>
            <textarea name="notes" class="form-control"><?= htmlspecialchars($customer['Notes']) ?></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">ملف (اختياري)</label>
            <input type="file" name="file" class="form-control">
        </div>
        <button type="submit" class="btn btn-primary">حفظ التعديلات</button>
        <a href="/search" class="btn btn-secondary">إلغاء</a>
    </form>
</div>
<script>
document.getElementById('tax_percentage').addEventListener('change', function () {
    const percentage = parseFloat(this.value);
    const amount = parseFloat(document.getElementById('amount').value);
    if (!isNaN(percentage) && !isNaN(amount)) {
        const taxValue = (percentage / 100) * amount;
        document.getElementById('tax').value = taxValue.toFixed(2);
    }
});
</script>
</body>
</html>
