<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'db.php';
$userID = $_SESSION['user_id'] ?? null;
$user = [];

if ($userID) {
    $stmt = $conn->prepare("SELECT Username, Role, ProfileImage FROM Users WHERE ID = :id");
    $stmt->bindParam(':id', $userID, PDO::PARAM_INT);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Finance</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="header-styles.css">
    <style>
        body {
            background-color: #f8f9fa;
        }

        .navbar {
            background-color: #343a40;
        }

        .navbar-nav .nav-link {
            color: #ffffff;
            padding: 10px 20px;
            margin: 5px 5px;
            border-radius: 8px;
            transition: background-color 0.3s;
            font-weight: 500;
            text-align: center;
        }

        .navbar-nav .nav-link:hover {
            background-color: #495057;
            color: #fff;
        }

        .navbar-nav .nav-item:first-child .nav-link {
            font-weight: bold;
            background-color: #0d6efd;
        }

        .navbar-profile-img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            margin-left: 10px;
        }

        .user-info-container {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin-right: auto;
        }

        .user-info span {
            color: white;
            font-size: 16px;
            font-weight: 500;
        }

        /* توسيط القائمة عند الهواتف */
        @media (max-width: 768px) {
            .navbar-collapse {
                text-align: center;
            }

           .navbar-nav {
             flex-direction: row;
             justify-content: center;
             gap: 10px;
             flex-wrap: wrap;
            }


            .navbar-nav .nav-item {
                width: 100%;
            }

            .user-info-container {
                display: none; /* إخفاء معلومات المستخدم في الجوال */
            }
        }
    </style>
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
        <div class="user-info-container">
            <?php if (!empty($user)): ?>
                <div class="user-info">
                    <a href="/e-finance/profile.php">
                        <img src="<?= !empty($user['ProfileImage']) ? 'uploads/' . $user['ProfileImage'] : 'default-user.png' ?>" class="navbar-profile-img" alt="الصورة الشخصية">
                    </a>
                    <span><?= htmlspecialchars($user['Username']) ?></span>
                </div>
            <?php endif; ?>
        </div>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar" aria-controls="mainNavbar" aria-expanded="false" aria-label="تبديل القائمة">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse justify-content-end" id="mainNavbar">
            <ul class="navbar-nav mx-auto mb-2 mb-lg-0 text-center">
                <li class="nav-item"><a class="nav-link" href="/e-finance/index">الرئيسية</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/Add_Notification1">ادارة التنبيهات</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/register">تسجيل العملاء</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/search">بحث العملاء</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/Expense_Management">ادارة الصرفيات</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/Budget_Management">ادارة الموازنات</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/expenses">إنشاء صرفية</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/search_exp">بحث الصرفيات</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/add_budget">إضافة رصيد</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/dashboard">التقارير</a></li>
                <li class="nav-item"><a class="nav-link" href="/e-finance/manage_users">الصلاحيات</a></li>
                <li class="nav-item"><a class="nav-link text-danger" href="/e-finance/logout">تسجيل الخروج</a></li>
            </ul>
        </div>
    </div>
</nav>

<div class="container mt-4">
