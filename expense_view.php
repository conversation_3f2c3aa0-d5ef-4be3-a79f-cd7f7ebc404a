<?php
require 'db.php';
if (!isset($_GET['id'])) {
    die("معرف الصرفية غير موجود.");
}
$id = $_GET['id'];
$stmt = $conn->prepare("SELECT * FROM Expenses WHERE ID = ?");
$stmt->execute([$id]);
$expense = $stmt->fetch();
if (!$expense) {
    die("الصرفية غير موجودة.");
}
?>
<!DOCTYPE html>
<html>
<head><title>عرض الصرفية</title></head>
<body>
    <h2>تفاصيل الصرفية</h2>
    <p>رقم الصرفية: <?= $expense['ExpenseNumber'] ?></p>
    <p>المبلغ: <?= $expense['Amount'] ?></p>
    <p>الوصف: <?= $expense['Description'] ?></p>
    <a href="expense_search.php">العودة</a>
</body>
</html>