<?php
session_start();
require 'db.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

$isAdmin = (isset($_SESSION['role']) && $_SESSION['role'] === 'admin');

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['customer_id'], $_POST['amount'], $_POST['budget_id'])) {
    $customerId = $_POST['customer_id'];
    $amount = floatval($_POST['amount']);
    $categoryId = isset($_POST['category_id']) && $_POST['category_id'] !== '' ? $_POST['category_id'] : null;
    $budgetId = $_POST['budget_id'];

    // احسب القيمة الصافية (بدون ضريبة)
    $netAmount = $amount;

    // أدخل المصروف
    $stmt = $conn->prepare("INSERT INTO Expenses (CustomerID, Amount, NetAmount, BudgetID, CategoryID, CreatedAt) VALUES (?, ?, ?, ?, ?, NOW())");
    $stmt->execute([$customerId, $amount, $netAmount, $budgetId, $categoryId]);

    // خصم من البند (إن وُجد)
    if ($categoryId) {
        // خصم من مبلغ البند
        $stmt = $conn->prepare("UPDATE Categories SET SpentAmount = SpentAmount + ? WHERE ID = ?");
        $stmt->execute([$netAmount, $categoryId]);

        // جلب BudgetID الخاص بالبند
        $stmt = $conn->prepare("SELECT BudgetID FROM Categories WHERE ID = ?");
        $stmt->execute([$categoryId]);
        $cat = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($cat && isset($cat['BudgetID'])) {
            // خصم من الموازنة المرتبطة بالبند
            $stmt = $conn->prepare("UPDATE Budgets SET UsedAmount = UsedAmount + ? WHERE ID = ?");
            $stmt->execute([$netAmount, $cat['BudgetID']]);
        }
    } else {
        // خصم من الموازنة المحددة مباشرة (في حالة عدم وجود بند)
        $stmt = $conn->prepare("UPDATE Budgets SET UsedAmount = UsedAmount + ? WHERE ID = ?");
        $stmt->execute([$netAmount, $budgetId]);
    }

    $_SESSION['success'] = "تم تسجيل المصروف بنجاح.";
    header("Location: ex.php");
    exit;
}

// جلب البيانات
$customers = $conn->query("SELECT ID, Name FROM Customers")->fetchAll(PDO::FETCH_ASSOC);
$budgets = $conn->query("SELECT * FROM Budgets")->fetchAll(PDO::FETCH_ASSOC);
$categories = $conn->query("SELECT * FROM Categories")->fetchAll(PDO::FETCH_ASSOC);
$expenses = $conn->query("
    SELECT e.*, c.Name as CustomerName, b.Title as BudgetTitle, cat.Name as CategoryName 
    FROM Expenses e 
    LEFT JOIN Customers c ON e.CustomerID = c.ID 
    LEFT JOIN Budgets b ON e.BudgetID = b.ID 
    LEFT JOIN Categories cat ON e.CategoryID = cat.ID 
    ORDER BY e.CreatedAt DESC
")->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة المصروفات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
<?php include 'header.php'; ?>

<div class="container mt-4">
    <h3 class="mb-4">إدارة المصروفات</h3>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success">
            <?= $_SESSION['success']; unset($_SESSION['success']); ?>
        </div>
    <?php endif; ?>

    <form method="POST" class="row g-3 mb-4">
        <div class="col-md-3">
            <select name="customer_id" class="form-select" required>
                <option value="">اختر المستفيد</option>
                <?php foreach ($customers as $c): ?>
                    <option value="<?= $c['ID'] ?>"><?= htmlspecialchars($c['Name']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="col-md-2">
            <input type="number" name="amount" class="form-control" placeholder="المبلغ" required>
        </div>
        <div class="col-md-3">
            <select name="budget_id" class="form-select" required>
                <option value="">اختر الموازنة</option>
                <?php foreach ($budgets as $b): ?>
                    <option value="<?= $b['ID'] ?>"><?= htmlspecialchars($b['Title']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="col-md-3">
            <select name="category_id" class="form-select">
                <option value="">اختر البند (اختياري)</option>
                <?php foreach ($categories as $cat): ?>
                    <option value="<?= $cat['ID'] ?>"><?= htmlspecialchars($cat['Name']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="col-md-1">
            <button type="submit" class="btn btn-success w-100">إضافة</button>
        </div>
    </form>

    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead class="table-light">
                <tr>
                    <th>المستفيد</th>
                    <th>المبلغ</th>
                    <th>الموازنة</th>
                    <th>البند</th>
                    <th>التاريخ</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($expenses as $e): ?>
                    <tr>
                        <td><?= htmlspecialchars($e['CustomerName']) ?></td>
                        <td><?= number_format($e['Amount'], 2) ?> جنيه</td>
                        <td><?= htmlspecialchars($e['BudgetTitle']) ?></td>
                        <td><?= $e['CategoryName'] ? htmlspecialchars($e['CategoryName']) : '—' ?></td>
                        <td><?= $e['CreatedAt'] ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div></body>
</html>
