# القائمة الجانبية للموقع - دليل الاستخدام

## نظرة عامة
تم إضافة قائمة جانبية شاملة لجميع صفحات الموقع لتحسين تجربة التنقل وسهولة الوصول إلى جميع الوظائف.

## الملفات المضافة

### 1. `sidebar.php`
- الملف الرئيسي للقائمة الجانبية
- يحتوي على قائمة جميع الصفحات والروابط
- يتضمن JavaScript للتحكم في القائمة
- يدعم تمييز الصفحة النشطة تلقائياً

### 2. `sidebar-styles.css`
- ملف التصميم المتقدم للقائمة الجانبية
- تأثيرات بصرية متطورة
- تصميم متجاوب للأجهزة المحمولة
- ألوان متناسقة مع تصميم الموقع (أبيض، أسود، أزرق)

### 3. `test_sidebar.php`
- صفحة اختبار للقائمة الجانبية
- تعرض جميع المميزات والوظائف
- تحتوي على تعليمات الاستخدام

## التحديثات على الملفات الموجودة

### `header.php`
- تم إضافة استدعاء `sidebar.php`
- تم إضافة class `main-content` للحاوي الرئيسي
- تحسينات في CSS لدعم القائمة الجانبية

### `index.php` و `dashboard.php`
- تم إصلاح بنية HTML لتجنب التداخل
- تحسين التوافق مع القائمة الجانبية

## المميزات

### 🎨 التصميم
- تصميم عصري ومتطور
- ألوان متناسقة (أبيض، أسود، أزرق)
- تأثيرات انتقال سلسة
- أيقونات واضحة لكل صفحة

### 📱 التجاوب
- يتكيف مع جميع أحجام الشاشات
- تصميم خاص للأجهزة المحمولة
- قائمة بعرض كامل على الشاشات الصغيرة

### ⚡ الوظائف
- فتح/إغلاق القائمة بزر دائري
- إغلاق تلقائي عند النقر خارج القائمة
- إغلاق بمفتاح Escape
- تمييز الصفحة النشطة
- انزلاق سلس من الجانب الأيمن

### 🔧 سهولة الاستخدام
- زر واضح ومرئي على الجانب الأيمن
- تأثيرات بصرية عند التمرير
- روابط منظمة ومرتبة
- أيقونات معبرة لكل قسم

## الصفحات المتضمنة في القائمة

1. **الرئيسية** - الصفحة الرئيسية للنظام
2. **إدارة المهام** - إدارة المهام والتنبيهات
3. **تسجيل عميل جديد** - إضافة عميل جديد
4. **رفع ملف العملاء** - رفع ملف Excel للعملاء
5. **بحث العملاء** - البحث في قاعدة بيانات العملاء
6. **بحث متقدم للعملاء** - بحث متقدم مع فلاتر
7. **إدارة الصرفيات** - إدارة المصروفات والصرفيات
8. **بحث الصرفيات** - البحث في الصرفيات
9. **إدارة الموازنات** - إدارة الموازنات المالية
10. **إضافة موازنة** - إضافة موازنة جديدة
11. **التقارير** - لوحة التقارير والإحصائيات
12. **إدارة المستخدمين** - إدارة صلاحيات المستخدمين
13. **الملف الشخصي** - صفحة الملف الشخصي
14. **تسجيل الخروج** - خروج من النظام

## كيفية الاستخدام

### للمطورين
1. تأكد من تضمين `sidebar.php` في `header.php`
2. تأكد من وجود class `main-content` في الحاوي الرئيسي
3. تأكد من تحميل ملف `sidebar-styles.css`

### للمستخدمين
1. انقر على الزر الأزرق الدائري على الجانب الأيمن
2. ستظهر القائمة الجانبية مع جميع الصفحات
3. انقر على أي رابط للانتقال إلى الصفحة
4. أغلق القائمة بالنقر خارجها أو بمفتاح Escape

## التخصيص

### إضافة صفحة جديدة
لإضافة صفحة جديدة إلى القائمة، عدّل مصفوفة `$menu_items` في `sidebar.php`:

```php
[
    'title' => 'اسم الصفحة',
    'icon' => 'fas fa-icon-name',
    'url' => '/e-finance/page-url',
    'page' => 'page-file.php'
]
```

### تغيير الألوان
عدّل متغيرات CSS في `sidebar-styles.css`:
- اللون الأساسي: `#007bff`
- اللون الثانوي: `#0056b3`
- لون النص: `#2c3e50`

### تغيير موضع الزر
عدّل خصائص `.sidebar-toggle` في CSS:
```css
.sidebar-toggle {
    right: 15px; /* المسافة من اليمين */
    top: 50%;    /* الموضع العمودي */
}
```

## الاختبار

### اختبار سريع
1. افتح `test_sidebar.php` في المتصفح
2. انقر على زر "اختبار القائمة الجانبية"
3. تأكد من ظهور القائمة وعمل جميع الوظائف

### اختبار شامل
1. اختبر على أحجام شاشات مختلفة
2. اختبر جميع الروابط
3. اختبر إغلاق القائمة بالطرق المختلفة
4. اختبر تمييز الصفحة النشطة

## المتطلبات التقنية

- Bootstrap 5.3.0
- Font Awesome 6.4.0
- jQuery (اختياري)
- متصفح حديث يدعم CSS3 و ES6

## الدعم والصيانة

### مشاكل شائعة
1. **القائمة لا تظهر**: تأكد من تضمين `sidebar.php` و `sidebar-styles.css`
2. **الروابط لا تعمل**: تحقق من مسارات الملفات في `$menu_items`
3. **التصميم مكسور**: تأكد من تحميل Bootstrap و Font Awesome

### تحديثات مستقبلية
- إضافة المزيد من التأثيرات البصرية
- دعم الثيمات المتعددة
- إضافة إعدادات شخصية للمستخدم
- تحسين الأداء والسرعة

## الترخيص
هذا المكون جزء من نظام E-Finance وخاضع لنفس شروط الترخيص.
