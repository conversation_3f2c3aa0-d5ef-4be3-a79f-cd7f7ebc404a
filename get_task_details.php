<?php
require_once 'db.php';
require_once 'functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$taskId = $_GET['id'] ?? 0;

if (!$taskId) {
    echo json_encode(['success' => false, 'message' => 'معرف المهمة مطلوب']);
    exit;
}

try {
    // جلب تفاصيل المهمة
    $stmt = $conn->prepare("
        SELECT n.*, 
               u1.Username as CreatedByName, u1.FullName as CreatedByFullName,
               u2.Username as AssignedToName, u2.FullName as AssignedToFullName,
               tc.Name as CategoryName, tc.Color as CategoryColor, tc.Icon as CategoryIcon
        FROM Notifications n 
        LEFT JOIN users u1 ON n.CreatedBy = u1.ID 
        LEFT JOIN users u2 ON n.AssignedTo = u2.ID 
        LEFT JOIN task_categories tc ON n.Category = tc.Name
        WHERE n.ID = ?
    ");
    $stmt->execute([$taskId]);
    $task = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$task) {
        echo json_encode(['success' => false, 'message' => 'المهمة غير موجودة']);
        exit;
    }
    
    // جلب التعليقات
    $commentsStmt = $conn->prepare("
        SELECT tc.*, u.Username, u.FullName 
        FROM task_comments tc 
        JOIN users u ON tc.UserID = u.ID 
        WHERE tc.TaskID = ? 
        ORDER BY tc.CreatedAt DESC
    ");
    $commentsStmt->execute([$taskId]);
    $comments = $commentsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب سجل التغييرات
    $historyStmt = $conn->prepare("
        SELECT th.*, u.Username, u.FullName 
        FROM task_history th 
        JOIN users u ON th.UserID = u.ID 
        WHERE th.TaskID = ? 
        ORDER BY th.CreatedAt DESC 
        LIMIT 10
    ");
    $historyStmt->execute([$taskId]);
    $history = $historyStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // حساب المدة المتبقية
    $today = date('Y-m-d');
    $daysRemaining = '';
    if ($task['ToDate'] && $task['Status'] !== 'تم') {
        $endDate = new DateTime($task['ToDate']);
        $currentDate = new DateTime($today);
        $interval = $currentDate->diff($endDate);
        
        if ($endDate < $currentDate) {
            $daysRemaining = '<span class="text-danger"><i class="fas fa-exclamation-triangle"></i> متأخر ' . $interval->days . ' يوم</span>';
        } elseif ($interval->days == 0) {
            $daysRemaining = '<span class="text-warning"><i class="fas fa-clock"></i> ينتهي اليوم</span>';
        } else {
            $daysRemaining = '<span class="text-info"><i class="fas fa-calendar-day"></i> ' . $interval->days . ' يوم متبقي</span>';
        }
    } elseif ($task['Status'] === 'تم') {
        $daysRemaining = '<span class="text-success"><i class="fas fa-check"></i> مكتمل</span>';
    }
    
    // بناء HTML
    $html = '
    <div class="task-details">
        <div class="row">
            <div class="col-md-8">
                <h4 class="task-title-modal">' . htmlspecialchars($task['Title']) . '</h4>
                <div class="task-meta mb-3">
                    <span class="badge bg-primary me-2">
                        <i class="fas fa-calendar-alt"></i> 
                        ' . date('d/m/Y', strtotime($task['NotifyDate'])) . ' - ' . date('d/m/Y', strtotime($task['ToDate'])) . '
                    </span>';
    
    if ($task['CategoryName']) {
        $html .= '<span class="badge me-2" style="background-color: ' . ($task['CategoryColor'] ?? '#007bff') . '">
                    <i class="' . ($task['CategoryIcon'] ?? 'fas fa-folder') . '"></i> 
                    ' . htmlspecialchars($task['CategoryName']) . '
                  </span>';
    }
    
    // الأولوية
    $priorityIcon = '';
    switch($task['Priority'] ?? 'متوسطة') {
        case 'عاجلة': $priorityIcon = '🔴'; break;
        case 'عالية': $priorityIcon = '🟠'; break;
        case 'متوسطة': $priorityIcon = '🟡'; break;
        case 'منخفضة': $priorityIcon = '🟢'; break;
    }
    
    $html .= '<span class="badge bg-warning text-dark me-2">
                ' . $priorityIcon . ' ' . ($task['Priority'] ?? 'متوسطة') . '
              </span>';
    
    if ($task['AssignedToFullName']) {
        $html .= '<span class="badge bg-info">
                    <i class="fas fa-user"></i> ' . htmlspecialchars($task['AssignedToFullName']) . '
                  </span>';
    }
    
    $html .= '</div>
                <div class="task-description-modal">
                    <h6><i class="fas fa-align-left"></i> الوصف:</h6>
                    <p>' . nl2br(htmlspecialchars($task['Message'])) . '</p>
                </div>';
    
    if ($task['Notes']) {
        $html .= '<div class="task-notes-modal">
                    <h6><i class="fas fa-sticky-note"></i> ملاحظات:</h6>
                    <p>' . nl2br(htmlspecialchars($task['Notes'])) . '</p>
                  </div>';
    }
    
    $html .= '</div>
            <div class="col-md-4">
                <div class="task-status-panel">
                    <h6><i class="fas fa-info-circle"></i> معلومات المهمة</h6>
                    <div class="status-item">
                        <strong>الحالة:</strong> 
                        <span class="badge bg-' . ($task['Status'] === 'تم' ? 'success' : ($task['Status'] === 'تحت التنفيذ' ? 'info' : 'warning')) . '">
                            ' . ($task['Status'] ?? 'غير محددة') . '
                        </span>
                    </div>
                    <div class="status-item">
                        <strong>التقدم:</strong>
                        <div class="progress mt-1" style="height: 20px;">
                            <div class="progress-bar bg-' . (($task['Progress'] ?? 0) == 100 ? 'success' : (($task['Progress'] ?? 0) >= 50 ? 'info' : 'warning')) . '" 
                                 style="width: ' . ($task['Progress'] ?? 0) . '%">
                                ' . ($task['Progress'] ?? 0) . '%
                            </div>
                        </div>
                    </div>
                    <div class="status-item">
                        <strong>المدة المتبقية:</strong><br>
                        ' . $daysRemaining . '
                    </div>
                    <div class="status-item">
                        <strong>أنشأها:</strong> ' . htmlspecialchars($task['CreatedByFullName'] ?? $task['CreatedByName'] ?? 'غير معروف') . '
                    </div>
                    <div class="status-item">
                        <strong>تاريخ الإنشاء:</strong><br>
                        <small>' . date('d/m/Y H:i', strtotime($task['CreatedAt'])) . '</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- التعليقات -->
        <div class="task-comments mt-4">
            <h6><i class="fas fa-comments"></i> التعليقات (' . count($comments) . ')</h6>';
    
    if (count($comments) > 0) {
        $html .= '<div class="comments-list">';
        foreach ($comments as $comment) {
            $html .= '<div class="comment-item">
                        <div class="comment-header">
                            <strong>' . htmlspecialchars($comment['FullName'] ?? $comment['Username']) . '</strong>
                            <small class="text-muted">' . date('d/m/Y H:i', strtotime($comment['CreatedAt'])) . '</small>
                        </div>
                        <div class="comment-body">
                            ' . nl2br(htmlspecialchars($comment['Comment'])) . '
                        </div>
                      </div>';
        }
        $html .= '</div>';
    } else {
        $html .= '<p class="text-muted">لا توجد تعليقات بعد</p>';
    }
    
    // نموذج إضافة تعليق
    $html .= '<div class="add-comment-form mt-3">
                <textarea class="form-control" id="newComment" placeholder="أضف تعليق..." rows="3"></textarea>
                <button class="btn btn-primary btn-sm mt-2" onclick="addComment(' . $taskId . ')">
                    <i class="fas fa-plus"></i> إضافة تعليق
                </button>
              </div>';
    
    $html .= '</div>
        
        <!-- سجل التغييرات -->
        <div class="task-history mt-4">
            <h6><i class="fas fa-history"></i> سجل التغييرات</h6>';
    
    if (count($history) > 0) {
        $html .= '<div class="history-list">';
        foreach ($history as $item) {
            $html .= '<div class="history-item">
                        <div class="history-header">
                            <strong>' . htmlspecialchars($item['FullName'] ?? $item['Username']) . '</strong>
                            <small class="text-muted">' . date('d/m/Y H:i', strtotime($item['CreatedAt'])) . '</small>
                        </div>
                        <div class="history-body">
                            <span class="badge bg-secondary">' . htmlspecialchars($item['Action']) . '</span>
                            ' . htmlspecialchars($item['NewValue']) . '
                        </div>
                      </div>';
        }
        $html .= '</div>';
    } else {
        $html .= '<p class="text-muted">لا يوجد سجل تغييرات</p>';
    }
    
    $html .= '</div>
    </div>';
    
    echo json_encode(['success' => true, 'html' => $html]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
}
?>
