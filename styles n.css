/* Reset & Basics */
html, body {
    height: 100%;
    margin: 0;
    background-color: #f4f6f9;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

body {
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

.container {
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Navbar */
.navbar {
    background-color: #056b07;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.navbar a {
    margin-right: 10px;
    padding: 8px 15px;
    color: white !important;
    border-radius: 5px;
    transition: 0.3s;
}

.navbar a:hover {
    background-color: #034f05;
}

/* Footer */
footer {
    background-color: #343a40;
    color: #fff;
    text-align: center;
    padding: 20px 0;
    width: 100%;
    margin-top: auto;
}

/* Buttons */
.btn {
    padding: 10px 18px;
    font-size: 15px;
    border-radius: 6px;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.btn-primary {
    background-color: #056b07;
    border-color: #056b07;
}

.btn-primary:hover {
    background-color: #034f05;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-warning {
    background-color: #ffc107;
    color: #000;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-danger {
    background-color: #dc3545;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* Tables */
.table {
    width: 100%;
    margin-top: 20px;
}

.table th, .table td {
    padding: 12px;
    text-align: center;
    vertical-align: middle;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
}

.table-dark th {
    background-color: #343a40;
    color: white;
}

.table-responsive {
    overflow-x: auto;
}

.table-custom {
    width: 100%;
    min-width: 1200px;
    font-size: 15px;
}

.table-custom tr:nth-child(even) {
    background-color: #f2f2f2;
}

.table-custom tr:hover {
    background-color: #e9ecef;
}

/* Forms */
.form-control,
.form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
}

textarea.form-control {
    resize: vertical;
}

button:focus,
input:focus,
select:focus {
    outline: none;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

/* Alerts */
.alert {
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Utility */
.text-center {
    text-align: center;
}

.center {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.cardCenter {
    display: grid;
    justify-items: center;
    margin-bottom: 100px;
}

.avatar-sm {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 50%;
    margin-left: 10px;
}
