<?php
// تحديد الصفحة الحالية
$current_page = basename($_SERVER['PHP_SELF']);

// الحصول على بيانات المستخدم إذا لم تكن متاحة
if (!isset($user) || empty($user)) {
    if (isset($userID) && $userID && isset($conn)) {
        $stmt = $conn->prepare("SELECT Username, Role, ProfileImage FROM Users WHERE ID = :id");
        $stmt->bindParam(':id', $userID, PDO::PARAM_INT);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
    }
}

// قائمة الصفحات والروابط
$menu_items = [
    [
        'title' => 'الرئيسية',
        'icon' => 'fas fa-home',
        'url' => '/e-finance/index',
        'page' => 'index.php'
    ],
    [
        'title' => 'إدارة المهام',
        'icon' => 'fas fa-tasks',
        'url' => '/e-finance/Add_Notification2',
        'page' => 'Add_Notification2.php'
    ],
    [
        'title' => 'تسجيل عميل جديد',
        'icon' => 'fas fa-user-plus',
        'url' => '/e-finance/register',
        'page' => 'register.php'
    ],
    [
        'title' => 'رفع ملف العملاء',
        'icon' => 'fas fa-upload',
        'url' => '/e-finance/customer_upload',
        'page' => 'customer_upload.php'
    ],
    [
        'title' => 'بحث العملاء',
        'icon' => 'fas fa-search',
        'url' => '/e-finance/search',
        'page' => 'search.php'
    ],
    // [
    //     'title' => 'بحث متقدم للعملاء',
    //     'icon' => 'fas fa-search-plus',
    //     'url' => '/e-finance/customer_search',
    //     'page' => 'customer_search.php'
    // ],
    [
        'title' => 'إدارة الصرفيات',
        'icon' => 'fas fa-money-bill-wave',
        'url' => '/e-finance/Expense_Management',
        'page' => 'Expense_Management.php'
    ],
    [
        'title' => 'بحث الصرفيات',
        'icon' => 'fas fa-receipt',
        'url' => '/e-finance/expense_search',
        'page' => 'expense_search.php'
    ],
    [
        'title' => 'إدارة الموازنات',
        'icon' => 'fas fa-wallet',
        'url' => '/e-finance/Budget_Management',
        'page' => 'Budget_Management.php'
    ],
    [
        'title' => 'إضافة موازنة',
        'icon' => 'fas fa-plus-circle',
        'url' => '/e-finance/add_budget',
        'page' => 'add_budget.php'
    ],
    [
        'title' => 'التقارير',
        'icon' => 'fas fa-chart-line',
        'url' => '/e-finance/dashboard',
        'page' => 'dashboard.php'
    ],
    [
        'title' => 'إدارة المستخدمين',
        'icon' => 'fas fa-users-cog',
        'url' => '/e-finance/manage_users',
        'page' => 'manage_users.php'
    ],
    [
        'title' => 'الملف الشخصي',
        'icon' => 'fas fa-user-circle',
        'url' => '/e-finance/profile',
        'page' => 'profile.php'
    ]
];
?>

<link rel="stylesheet" href="sidebar-styles.css">

<!-- زر تبديل القائمة الجانبية -->
<button class="sidebar-toggle" id="sidebarToggle">
    <i class="fas fa-bars"></i>
</button>

<!-- طبقة التراكب -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<!-- القائمة الجانبية -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <div class="user-profile-section">
            <?php if (!empty($user)): ?>
                <div class="user-avatar">
                    <img src="<?= !empty($user['ProfileImage']) ? 'uploads/' . $user['ProfileImage'] : 'default-avatar.png' ?>"
                         alt="الصورة الشخصية" class="profile-img">
                </div>
                <div class="user-details">
                    <h5 class="user-name"><?= htmlspecialchars($user['Username']) ?></h5>
                    <span class="user-role"><?= htmlspecialchars($user['Role']) ?></span>
                </div>
            <?php endif; ?>
        </div>
        <h4><i class="fas fa-list me-2"></i>قائمة الصفحات</h4>
    </div>
    
    <ul class="sidebar-menu">
        <?php foreach ($menu_items as $item): ?>
            <li>
                <a href="<?= $item['url'] ?>" class="<?= ($current_page == $item['page']) ? 'active' : '' ?>">
                    <i class="<?= $item['icon'] ?>"></i>
                    <span><?= $item['title'] ?></span>
                </a>
            </li>
        <?php endforeach; ?>
        
        <!-- رابط تسجيل الخروج -->
        <li>
            <a href="/e-finance/logout" style="border-top: 2px solid rgba(0, 0, 0, 0.1); margin-top: 10px;">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </li>
    </ul>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');
    const mainContent = document.querySelector('.main-content') || document.getElementById('mainContent') || document.querySelector('.container');
    
    // تبديل القائمة الجانبية
    function toggleSidebar() {
        sidebar.classList.toggle('active');
        sidebarOverlay.classList.toggle('active');
        sidebarToggle.classList.toggle('active');
        
        if (mainContent && window.innerWidth > 768) {
            mainContent.classList.toggle('sidebar-open');
        }
    }
    
    // إغلاق القائمة الجانبية
    function closeSidebar() {
        sidebar.classList.remove('active');
        sidebarOverlay.classList.remove('active');
        sidebarToggle.classList.remove('active');
        
        if (mainContent) {
            mainContent.classList.remove('sidebar-open');
        }
    }
    
    // أحداث النقر
    sidebarToggle.addEventListener('click', toggleSidebar);
    sidebarOverlay.addEventListener('click', closeSidebar);
    
    // إغلاق القائمة عند النقر على رابط (للأجهزة المحمولة)
    const sidebarLinks = sidebar.querySelectorAll('a');
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                closeSidebar();
            }
        });
    });
    
    // إغلاق القائمة عند تغيير حجم الشاشة
    window.addEventListener('resize', function() {
        if (window.innerWidth <= 768) {
            if (mainContent) {
                mainContent.classList.remove('sidebar-open');
            }
        }
    });
    
    // إغلاق القائمة بالضغط على Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sidebar.classList.contains('active')) {
            closeSidebar();
        }
    });
});
</script>
