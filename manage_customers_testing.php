<?php

session_start();

require 'db.php'; // الاتصال بقاعدة البيانات



// التحقق من تسجيل الدخول

if (!isset($_SESSION['user_id'])) {

    header("Location: /login");

    exit;

}



// التحقق من صلاحية المستخدم

$role = $_SESSION['role'];

if (!in_array($role, ['admin', 'viewer'])) {

    header("Location: /index");

    exit;

}



// البحث عن العملاء

$search = '';

$customers = [];



if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['query']) && !empty(trim($_GET['query']))) {

    $search = trim($_GET['query']);



    // إعداد الاستعلام الديناميكي

    $query = "SELECT * FROM Customers WHERE (Name LIKE :search 

               OR NationalID LIKE :search 

               OR AccountNumber LIKE :search 

               OR AuthorizationNumber LIKE :search 

               OR TransferNumber LIKE :search 

               OR Notes LIKE :search 

               OR Category LIKE :search)";



    // إضافة شروط للقيم الرقمية

    if (is_numeric($search)) {

        $query .= " OR Amount = :amount OR Tax = :tax OR DocumentCount = :doc_count";

    }



    // تحضير وتنفيذ الاستعلام

    $stmt = $conn->prepare($query);

    $stmt->bindValue(':search', "%$search%", PDO::PARAM_STR);



    // إذا كانت القيمة رقمية، يتم تمرير القيم المناسبة

    if (is_numeric($search)) {

        $stmt->bindValue(':amount', $search, PDO::PARAM_INT);

        $stmt->bindValue(':tax', $search, PDO::PARAM_INT);

        $stmt->bindValue(':doc_count', $search, PDO::PARAM_INT);

    }



    $stmt->execute();

    $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);

} else {

    // عرض جميع العملاء في حال عدم وجود بحث

    $stmt = $conn->query("SELECT * FROM Customers");

    $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);

}

?>





<!DOCTYPE html>

<html lang="ar" dir="rtl">

<head>

    <meta charset="UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>إدارة العملاء</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <link rel="stylesheet" href="styles.css">

</head>

<body>

<div class="container mt-5">

    <h1 class="text-center">إدارة العملاء</h1>



    <!-- نموذج البحث -->

    <form class="mt-4" method="get">

        <div class="input-group mb-3">

            <input type="text" name="query" class="form-control" placeholder="ابحث باستخدام أي حقل" value="echo htmlspecialchars($customer['Name'] ?? 'غير متوفر');">

            <button type="submit" class="btn btn-primary">بحث</button>

        </div>

    </form>



    <!-- جدول عرض العملاء -->

    <div class="table-responsive">

        <table class="table table-bordered mt-4 text-center">

            <thead class="table-dark">

            <tr>

                <th>#</th>

                <th>الاسم</th>

                <th>الرقم القومي</th>

                <th>رقم الحساب</th>

                <th>المبلغ</th>

                <th>الضريبة</th>

                <th>الصافي</th>

                <th>مدة الصرف</th>

                <th>تاريخ البداية</th>

                <th>تاريخ النهاية</th>

                <th>الملف</th>

                <th>رقم الإذن</th>

                <th>رقم التحويل</th>

                <th>عدد المستندات</th>

                <th>ملاحظات</th>

                <th>البند</th>

                <th>الإجراءات</th>

            </tr>

            </thead>

            <tbody>

          <?php if (!empty($customers)): ?>

    <?php foreach ($customers as $index => $customer): ?>

        <tr>

            <td><?php echo $index + 1; ?></td>

            <td><?php echo htmlspecialchars($customer['Name'] ?? 'غير متوفر'); ?></td>

            <td><?php echo htmlspecialchars($customer['NationalID'] ?? 'غير متوفر'); ?></td>

            <td><?php echo htmlspecialchars($customer['AccountNumber'] ?? 'غير متوفر'); ?></td>

            <td><?php echo htmlspecialchars($customer['Amount'] ?? 0); ?></td>

            <td><?php echo htmlspecialchars($customer['Tax'] ?? 0); ?></td>

            <td><?php echo htmlspecialchars(($customer['Amount'] ?? 0) - ($customer['Tax'] ?? 0)); ?></td>

            <td><?php echo htmlspecialchars($customer['PaymentDuration'] ?? 'غير متوفر'); ?></td>

            <td><?php echo htmlspecialchars($customer['StartDate'] ?? 'غير متوفر'); ?></td>

            <td><?php echo htmlspecialchars($customer['EndDate'] ?? 'غير متوفر'); ?></td>

            <td>

                <a href="uploads/<?php echo htmlspecialchars($customer['FilePath'] ?? ''); ?>" target="_blank">عرض</a>

            </td>

            <td><?php echo htmlspecialchars($customer['AuthorizationNumber'] ?? 'غير متوفر'); ?></td>

            <td><?php echo htmlspecialchars($customer['TransferNumber'] ?? 'غير متوفر'); ?></td>

            <td><?php echo htmlspecialchars($customer['DocumentCount'] ?? 0); ?></td>

            <td><?php echo htmlspecialchars($customer['Notes'] ?? 'غير متوفر'); ?></td>

            <td><?php echo htmlspecialchars($customer['Category'] ?? 'غير متوفر'); ?></td>

            <td>

                <?php if ($role === 'admin'): ?>

                    <a href="edit.php?id=<?php echo $customer['ID']; ?>" class="btn btn-warning btn-sm">تعديل</a>

                    <form method="POST" action="delete_customer.php" style="display:inline-block;">

                        <input type="hidden" name="delete_id" value="<?php echo $customer['ID']; ?>">

                        <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟')">حذف</button>

                    </form>

                <?php endif; ?>

            </td>

        </tr>

    <?php endforeach; ?>

<?php else: ?>

    <tr><td colspan="17" class="text-center">لا توجد نتائج.</td></tr>

<?php endif; ?>



            </tbody>

        </table>

    </div>

</div>

</body>

</html>

