<?php
require_once 'db.php';
require_once 'functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';

// التحقق من نوع التصدير
$exportType = $_GET['type'] ?? 'excel';
$format = $_GET['format'] ?? 'xlsx';

// الحصول على المعاملات من الجلسة أو GET
$currentMonth = $_GET['month'] ?? date('m');
$currentYear = $_GET['year'] ?? date('Y');
$statusFilter = $_GET['status'] ?? '';
$searchQuery = $_GET['search'] ?? '';
$dateFromFilter = $_GET['date_from'] ?? '';
$dateToFilter = $_GET['date_to'] ?? '';

$startDate = "$currentYear-" . str_pad($currentMonth, 2, '0', STR_PAD_LEFT) . "-01";
$endDate = date("Y-m-t", strtotime($startDate));

// بناء الاستعلام مع البيانات الإضافية
$query = "SELECT n.*,
                 u1.Username as CreatedByName, u1.FullName as CreatedByFullName,
                 u2.Username as AssignedToName, u2.FullName as AssignedToFullName,
                 tc.Name as CategoryName, tc.Color as CategoryColor, tc.Icon as CategoryIcon
          FROM Notifications n
          LEFT JOIN users u1 ON n.CreatedBy = u1.ID
          LEFT JOIN users u2 ON n.AssignedTo = u2.ID
          LEFT JOIN task_categories tc ON n.Category = tc.Name
          WHERE (n.NotifyDate BETWEEN ? AND ? OR n.ToDate BETWEEN ? AND ?)";
$params = [$startDate, $endDate, $startDate, $endDate];

if ($searchQuery !== '') {
    $query .= " AND (n.Title LIKE ? OR n.Message LIKE ?)";
    $params[] = "%$searchQuery%";
    $params[] = "%$searchQuery%";
}

if ($statusFilter !== '') {
    $query .= " AND n.Status = ?";
    $params[] = $statusFilter;
}

if ($dateFromFilter !== '') {
    $query .= " AND n.NotifyDate >= ?";
    $params[] = $dateFromFilter;
}

if ($dateToFilter !== '') {
    $query .= " AND n.ToDate <= ?";
    $params[] = $dateToFilter;
}

$query .= " ORDER BY NotifyDate ASC";
$stmt = $conn->prepare($query);
$stmt->execute($params);
$tasks = $stmt->fetchAll(PDO::FETCH_ASSOC);

// تصدير Excel محسن
if ($exportType === 'excel') {
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="tasks_report_' . date('Y-m-d_H-i-s') . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');

    echo "\xEF\xBB\xBF"; // UTF-8 BOM

    // إضافة معلومات التقرير
    echo '<table border="1" style="width:100%; border-collapse: collapse;">';
    echo '<tr style="background-color: #007bff; color: white;">';
    echo '<td colspan="10" style="text-align: center; font-size: 16px; font-weight: bold; padding: 10px;">';
    echo 'تقرير المهام - ' . date('d/m/Y H:i');
    echo '</td>';
    echo '</tr>';

    // معلومات الفلاتر المطبقة
    echo '<tr>';
    echo '<td colspan="10" style="background-color: #f8f9fa; padding: 5px;">';
    echo '<strong>الفلاتر المطبقة:</strong> ';
    if ($statusFilter) echo "الحالة: $statusFilter | ";
    if ($searchQuery) echo "البحث: $searchQuery | ";
    if ($dateFromFilter) echo "من تاريخ: $dateFromFilter | ";
    if ($dateToFilter) echo "إلى تاريخ: $dateToFilter";
    echo '</td>';
    echo '</tr>';

    // عدد النتائج
    echo '<tr>';
    echo '<td colspan="10" style="background-color: #e9ecef; padding: 5px;">';
    echo '<strong>عدد المهام:</strong> ' . count($tasks);
    echo '</td>';
    echo '</tr>';

    // فراغ
    echo '<tr><td colspan="10" style="height: 10px;"></td></tr>';

    // رؤوس الأعمدة
    echo '<tr style="background-color: #343a40; color: white; font-weight: bold;">';
    echo '<th style="padding: 8px; text-align: center;">الرقم</th>';
    echo '<th style="padding: 8px; text-align: center;">العنوان</th>';
    echo '<th style="padding: 8px; text-align: center;">الوصف</th>';
    echo '<th style="padding: 8px; text-align: center;">الأولوية</th>';
    echo '<th style="padding: 8px; text-align: center;">التصنيف</th>';
    echo '<th style="padding: 8px; text-align: center;">من تاريخ</th>';
    echo '<th style="padding: 8px; text-align: center;">إلى تاريخ</th>';
    echo '<th style="padding: 8px; text-align: center;">الحالة</th>';
    echo '<th style="padding: 8px; text-align: center;">التقدم</th>';
    echo '<th style="padding: 8px; text-align: center;">تاريخ الإنشاء</th>';
    echo '</tr>';

    // البيانات
    foreach ($tasks as $index => $task) {
        $rowColor = ($index % 2 == 0) ? '#ffffff' : '#f8f9fa';
        echo '<tr style="background-color: ' . $rowColor . ';">';
        echo '<td style="padding: 5px; text-align: center;">' . ($index + 1) . '</td>';
        echo '<td style="padding: 5px;">' . htmlspecialchars($task['Title']) . '</td>';
        echo '<td style="padding: 5px;">' . htmlspecialchars(mb_substr($task['Message'], 0, 100)) . '...</td>';
        echo '<td style="padding: 5px; text-align: center;">' . htmlspecialchars($task['Priority'] ?? 'متوسطة') . '</td>';
        echo '<td style="padding: 5px; text-align: center;">' . htmlspecialchars($task['Category'] ?? 'غير محدد') . '</td>';
        echo '<td style="padding: 5px; text-align: center;">' . date('d/m/Y', strtotime($task['NotifyDate'])) . '</td>';
        echo '<td style="padding: 5px; text-align: center;">' . date('d/m/Y', strtotime($task['ToDate'])) . '</td>';
        echo '<td style="padding: 5px; text-align: center;">' . htmlspecialchars($task['Status']) . '</td>';
        echo '<td style="padding: 5px; text-align: center;">' . ($task['Progress'] ?? 0) . '%</td>';
        echo '<td style="padding: 5px; text-align: center;">' . date('d/m/Y H:i', strtotime($task['CreatedAt'])) . '</td>';
        echo '</tr>';
    }

    // إضافة إحصائيات في النهاية
    $completed = count(array_filter($tasks, fn($t) => $t['Status'] === 'تم'));
    $inProgress = count(array_filter($tasks, fn($t) => $t['Status'] === 'تحت التنفيذ'));
    $pending = count(array_filter($tasks, fn($t) => $t['Status'] === 'لم تنفذ'));

    echo '<tr><td colspan="10" style="height: 10px;"></td></tr>';
    echo '<tr style="background-color: #007bff; color: white; font-weight: bold;">';
    echo '<td colspan="10" style="text-align: center; padding: 8px;">إحصائيات التقرير</td>';
    echo '</tr>';
    echo '<tr>';
    echo '<td colspan="2" style="padding: 5px; background-color: #d4edda;"><strong>مكتملة:</strong> ' . $completed . '</td>';
    echo '<td colspan="2" style="padding: 5px; background-color: #cce5ff;"><strong>قيد التنفيذ:</strong> ' . $inProgress . '</td>';
    echo '<td colspan="2" style="padding: 5px; background-color: #fff3cd;"><strong>في الانتظار:</strong> ' . $pending . '</td>';
    echo '<td colspan="4" style="padding: 5px; background-color: #f8f9fa;"><strong>معدل الإنجاز:</strong> ' .
         (count($tasks) > 0 ? round(($completed / count($tasks)) * 100, 1) : 0) . '%</td>';
    echo '</tr>';

    echo '</table>';
    exit;
}

// تصدير CSV محسن
if ($exportType === 'csv') {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="tasks_report_' . date('Y-m-d_H-i-s') . '.csv"');
    header('Pragma: no-cache');
    header('Expires: 0');

    echo "\xEF\xBB\xBF"; // UTF-8 BOM

    $output = fopen('php://output', 'w');

    // معلومات التقرير
    fputcsv($output, ['تقرير المهام - ' . date('d/m/Y H:i')]);
    fputcsv($output, ['']);

    // معلومات الفلاتر
    $filterInfo = 'الفلاتر المطبقة: ';
    if ($statusFilter) $filterInfo .= "الحالة: $statusFilter | ";
    if ($searchQuery) $filterInfo .= "البحث: $searchQuery | ";
    if ($dateFromFilter) $filterInfo .= "من تاريخ: $dateFromFilter | ";
    if ($dateToFilter) $filterInfo .= "إلى تاريخ: $dateToFilter";
    fputcsv($output, [$filterInfo]);

    fputcsv($output, ['عدد المهام: ' . count($tasks)]);
    fputcsv($output, ['']);

    // العناوين المحسنة
    fputcsv($output, [
        'الرقم',
        'العنوان',
        'الوصف',
        'الأولوية',
        'التصنيف',
        'المسؤول',
        'من تاريخ',
        'إلى تاريخ',
        'الحالة',
        'التقدم (%)',
        'تاريخ الإنشاء',
        'آخر تحديث',
        'الملاحظات'
    ]);

    // البيانات المحسنة
    foreach ($tasks as $index => $task) {
        fputcsv($output, [
            $index + 1,
            $task['Title'],
            $task['Message'],
            $task['Priority'] ?? 'متوسطة',
            $task['Category'] ?? 'غير محدد',
            $task['AssignedToName'] ?? 'غير معين',
            date('d/m/Y', strtotime($task['NotifyDate'])),
            date('d/m/Y', strtotime($task['ToDate'])),
            $task['Status'],
            $task['Progress'] ?? 0,
            date('d/m/Y H:i', strtotime($task['CreatedAt'])),
            $task['LastUpdated'] ? date('d/m/Y H:i', strtotime($task['LastUpdated'])) : '',
            $task['Notes'] ?? ''
        ]);
    }

    // إضافة إحصائيات
    fputcsv($output, ['']);
    fputcsv($output, ['إحصائيات التقرير']);

    $completed = count(array_filter($tasks, fn($t) => $t['Status'] === 'تم'));
    $inProgress = count(array_filter($tasks, fn($t) => $t['Status'] === 'تحت التنفيذ'));
    $pending = count(array_filter($tasks, fn($t) => $t['Status'] === 'لم تنفذ'));

    fputcsv($output, ['مكتملة', $completed]);
    fputcsv($output, ['قيد التنفيذ', $inProgress]);
    fputcsv($output, ['في الانتظار', $pending]);
    fputcsv($output, ['معدل الإنجاز (%)', count($tasks) > 0 ? round(($completed / count($tasks)) * 100, 1) : 0]);

    fclose($output);
    exit;
}

// تصدير PDF باستخدام HTML
if ($exportType === 'pdf') {
    header('Content-Type: text/html; charset=utf-8');

    // إنشاء HTML للطباعة
    echo '<!DOCTYPE html>';
    echo '<html lang="ar" dir="rtl">';
    echo '<head>';
    echo '<meta charset="UTF-8">';
    echo '<title>تقرير المهام - ' . date('d/m/Y') . '</title>';
    echo '<style>';
    echo 'body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }';
    echo '.header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007bff; padding-bottom: 10px; }';
    echo '.filters { background-color: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 5px; }';
    echo 'table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }';
    echo 'th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }';
    echo 'th { background-color: #007bff; color: white; font-weight: bold; }';
    echo 'tr:nth-child(even) { background-color: #f2f2f2; }';
    echo '.stats { background-color: #e9ecef; padding: 15px; border-radius: 5px; }';
    echo '.priority-high { background-color: #ffebee; }';
    echo '.priority-medium { background-color: #fff3e0; }';
    echo '.priority-low { background-color: #e8f5e8; }';
    echo '.status-completed { background-color: #d4edda; }';
    echo '.status-progress { background-color: #cce5ff; }';
    echo '.status-pending { background-color: #fff3cd; }';
    echo '@media print { body { margin: 0; } .no-print { display: none; } }';
    echo '</style>';
    echo '</head>';
    echo '<body>';

    // رأس التقرير
    echo '<div class="header">';
    echo '<h1>تقرير المهام</h1>';
    echo '<p>تاريخ التقرير: ' . date('d/m/Y H:i') . '</p>';
    echo '</div>';

    // معلومات الفلاتر
    echo '<div class="filters">';
    echo '<h3>الفلاتر المطبقة:</h3>';
    echo '<ul>';
    if ($statusFilter) echo '<li><strong>الحالة:</strong> ' . $statusFilter . '</li>';
    if ($searchQuery) echo '<li><strong>البحث:</strong> ' . $searchQuery . '</li>';
    if ($dateFromFilter) echo '<li><strong>من تاريخ:</strong> ' . $dateFromFilter . '</li>';
    if ($dateToFilter) echo '<li><strong>إلى تاريخ:</strong> ' . $dateToFilter . '</li>';
    echo '<li><strong>عدد المهام:</strong> ' . count($tasks) . '</li>';
    echo '</ul>';
    echo '</div>';

    // الجدول
    echo '<table>';
    echo '<thead>';
    echo '<tr>';
    echo '<th>الرقم</th>';
    echo '<th>العنوان</th>';
    echo '<th>الأولوية</th>';
    echo '<th>التصنيف</th>';
    echo '<th>من تاريخ</th>';
    echo '<th>إلى تاريخ</th>';
    echo '<th>الحالة</th>';
    echo '<th>التقدم</th>';
    echo '</tr>';
    echo '</thead>';
    echo '<tbody>';

    foreach ($tasks as $index => $task) {
        $priorityClass = '';
        switch($task['Priority'] ?? 'متوسطة') {
            case 'عاجلة':
            case 'عالية':
                $priorityClass = 'priority-high';
                break;
            case 'متوسطة':
                $priorityClass = 'priority-medium';
                break;
            case 'منخفضة':
                $priorityClass = 'priority-low';
                break;
        }

        $statusClass = '';
        switch($task['Status']) {
            case 'تم':
                $statusClass = 'status-completed';
                break;
            case 'تحت التنفيذ':
                $statusClass = 'status-progress';
                break;
            case 'لم تنفذ':
                $statusClass = 'status-pending';
                break;
        }

        echo '<tr class="' . $priorityClass . ' ' . $statusClass . '">';
        echo '<td>' . ($index + 1) . '</td>';
        echo '<td style="text-align: right;">' . htmlspecialchars($task['Title']) . '</td>';
        echo '<td>' . htmlspecialchars($task['Priority'] ?? 'متوسطة') . '</td>';
        echo '<td>' . htmlspecialchars($task['Category'] ?? 'غير محدد') . '</td>';
        echo '<td>' . date('d/m/Y', strtotime($task['NotifyDate'])) . '</td>';
        echo '<td>' . date('d/m/Y', strtotime($task['ToDate'])) . '</td>';
        echo '<td>' . htmlspecialchars($task['Status']) . '</td>';
        echo '<td>' . ($task['Progress'] ?? 0) . '%</td>';
        echo '</tr>';
    }

    echo '</tbody>';
    echo '</table>';

    // الإحصائيات
    $completed = count(array_filter($tasks, fn($t) => $t['Status'] === 'تم'));
    $inProgress = count(array_filter($tasks, fn($t) => $t['Status'] === 'تحت التنفيذ'));
    $pending = count(array_filter($tasks, fn($t) => $t['Status'] === 'لم تنفذ'));

    echo '<div class="stats">';
    echo '<h3>إحصائيات التقرير:</h3>';
    echo '<div style="display: flex; justify-content: space-around; text-align: center;">';
    echo '<div><strong>مكتملة:</strong><br>' . $completed . '</div>';
    echo '<div><strong>قيد التنفيذ:</strong><br>' . $inProgress . '</div>';
    echo '<div><strong>في الانتظار:</strong><br>' . $pending . '</div>';
    echo '<div><strong>معدل الإنجاز:</strong><br>' .
         (count($tasks) > 0 ? round(($completed / count($tasks)) * 100, 1) : 0) . '%</div>';
    echo '</div>';
    echo '</div>';

    // زر الطباعة
    echo '<div class="no-print" style="text-align: center; margin-top: 20px;">';
    echo '<button onclick="window.print()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">طباعة التقرير</button>';
    echo '<button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px;">إغلاق</button>';
    echo '</div>';

    echo '</body>';
    echo '</html>';
    exit;
}

// إذا لم يتم تحديد نوع صحيح
header("Location: Add_Notification2.php");
exit;
?>
