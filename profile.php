<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

$userID = $_SESSION['user_id'];
$stmt = $conn->prepare("SELECT * FROM Users WHERE ID = :id");
$stmt->bindParam(':id', $userID, PDO::PARAM_INT);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

$isAdmin = ($user['Role'] === 'admin');

// تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $newName = $_POST['name'];
    $newRole = $user['Role']; // الدور لا يتغير للمستخدم العادي

    // السماح فقط للإدمن بتعديل الدور
    if ($isAdmin && !empty($_POST['role'])) {
        $newRole = $_POST['role'];
    }

    $oldPasswordInput = $_POST['old_password'] ?? '';
    $changePassword = !empty($_POST['password']);

    if ($changePassword) {
        if (!password_verify($oldPasswordInput, $user['Password'])) {
            echo "<script>alert('كلمة المرور القديمة غير صحيحة'); window.history.back();</script>";
            exit;
        }
        $newPassword = password_hash($_POST['password'], PASSWORD_DEFAULT);
    } else {
        $newPassword = $user['Password'];
    }

    $profileImage = $user['ProfileImage'];
    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === UPLOAD_ERR_OK) {
        $imageTmpPath = $_FILES['profile_image']['tmp_name'];
        $imageName = time() . '_' . $_FILES['profile_image']['name'];
        $destination = 'uploads/' . $imageName;
        if (move_uploaded_file($imageTmpPath, $destination)) {
            $profileImage = $imageName;
        }
    }

    $updateStmt = $conn->prepare("UPDATE Users SET Username = :name, Role = :role, Password = :password, ProfileImage = :image WHERE ID = :id");
    $updateStmt->execute([
        ':name' => $newName,
        ':role' => $newRole,
        ':password' => $newPassword,
        ':image' => $profileImage,
        ':id' => $userID
    ]);

    echo "<script>alert('تم تحديث البيانات بنجاح'); window.location.href='profile.php';</script>";
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>الملف الشخصي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
</head>
<body class="container mt-5">
    <h2 class="text-center mb-4">الملف الشخصي</h2>

    <form method="POST" enctype="multipart/form-data">
        <!-- صورة المستخدم -->
        <div class="mb-3 text-center">
            <?php if (!empty($user['ProfileImage'])): ?>
                <img src="uploads/<?= htmlspecialchars($user['ProfileImage']) ?>" alt="الصورة الشخصية" class="rounded-circle" width="100" height="100" style="object-fit:cover;">
            <?php else: ?>
                <img src="default-avatar.png" alt="صورة افتراضية" class="rounded-circle" width="100" height="100">
            <?php endif; ?>
        </div>

        <!-- الاسم -->
        <div class="mb-3">
            <label for="name" class="form-label">الاسم</label>
            <input type="text" name="name" id="name" class="form-control" value="<?= htmlspecialchars($user['Username']) ?>" required>
            <div class="form-text">يمكنك تعديل اسمك هنا.</div>
        </div>

        <!-- الدور -->
        <?php if ($isAdmin): ?>
            <div class="mb-3">
                <label for="role" class="form-label">الوظيفة / الدور</label>
                <input type="text" name="role" id="role" class="form-control" value="<?= htmlspecialchars($user['Role']) ?>" required>
                <div class="form-text">كإدمن، يمكنك تعديل الدور.</div>
            </div>
        <?php else: ?>
            <input type="hidden" name="role" value="<?= htmlspecialchars($user['Role']) ?>">
        <?php endif; ?>

        <!-- كلمة المرور القديمة -->
        <div class="mb-3">
            <label for="old_password" class="form-label">كلمة المرور الحالية</label>
            <input type="password" name="old_password" id="old_password" class="form-control">
            <div class="form-text">أدخل كلمة المرور الحالية إذا كنت ترغب في تغييرها.</div>
        </div>

        <!-- كلمة المرور الجديدة -->
        <div class="mb-3">
            <label for="password" class="form-label">كلمة المرور الجديدة (اختياري)</label>
            <input type="password" name="password" id="password" class="form-control">
            <div class="form-text">اتركها فارغة إذا لا ترغب في تغييرها.</div>
        </div>

        <!-- الصورة الشخصية -->
        <div class="mb-3">
            <label for="profile_image" class="form-label">تحديث الصورة الشخصية</label>
            <input type="file" name="profile_image" id="profile_image" class="form-control">
            <div class="form-text">يمكنك رفع صورة جديدة.</div>
        </div>

        <!-- الأزرار -->
        <button type="submit" class="btn btn-success">حفظ التعديلات</button>
        <a href="index.php" class="btn btn-secondary">عودة للرئيسية</a>
    </form>
</body>
</html>
