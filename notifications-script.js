// ===== سكريبت تحسين صفحة إدارة التنبيهات =====

document.addEventListener('DOMContentLoaded', function() {
    
    // تحسين زر العودة لأعلى
    const scrollBtn = document.getElementById('scrollBtn');
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 200) {
            scrollBtn.style.display = 'block';
            scrollBtn.style.opacity = '1';
        } else {
            scrollBtn.style.opacity = '0';
            setTimeout(() => {
                if (window.pageYOffset <= 200) {
                    scrollBtn.style.display = 'none';
                }
            }, 300);
        }
    });
    
    // تحسين العودة لأعلى
    function scrollToTop() {
        window.scrollTo({ 
            top: 0, 
            behavior: 'smooth' 
        });
    }
    
    // ربط الدالة بالزر
    if (scrollBtn) {
        scrollBtn.addEventListener('click', scrollToTop);
    }
    
    // تحسين تأكيد الحذف
    document.querySelectorAll('.btn-delete').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // إنشاء نافذة تأكيد مخصصة
            const result = confirm(
                '⚠️ تحذير!\n\n' +
                'هل أنت متأكد من حذف هذه المهمة؟\n' +
                'لا يمكن التراجع عن هذا الإجراء.\n\n' +
                'اضغط "موافق" للمتابعة أو "إلغاء" للتراجع.'
            );
            
            if (result) {
                // إضافة تأثير التحميل
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحذف...';
                btn.style.pointerEvents = 'none';
                
                // الانتقال للرابط بعد تأخير قصير
                setTimeout(() => {
                    window.location.href = btn.href;
                }, 500);
            }
        });
    });
    
    // تحسين أزرار تحديث الحالة
    document.querySelectorAll('.btn-update-status').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            this.disabled = true;
            
            // إعادة تفعيل الزر في حالة عدم إرسال النموذج
            setTimeout(() => {
                if (this.disabled) {
                    this.innerHTML = originalText;
                    this.disabled = false;
                }
            }, 5000);
        });
    });
    
    // تحسين أزرار الترحيل
    document.querySelectorAll('.btn-reschedule').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            const result = confirm(
                '📅 ترحيل المهمة\n\n' +
                'هل تريد ترحيل هذه المهمة إلى الغد؟\n' +
                'سيتم تحديث تاريخ البداية والنهاية.'
            );
            
            if (result) {
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الترحيل...';
                this.disabled = true;
            } else {
                e.preventDefault();
            }
        });
    });
    
    // تحسين تفاعل الجدول
    document.querySelectorAll('.table tbody tr').forEach(function(row) {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.01)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // تحسين النماذج
    document.querySelectorAll('form').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn && !submitBtn.disabled) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
                submitBtn.disabled = true;
                
                // إعادة تفعيل الزر في حالة عدم إرسال النموذج
                setTimeout(() => {
                    if (submitBtn.disabled) {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                }, 10000);
            }
        });
    });
    
    // تحسين حقول الإدخال
    document.querySelectorAll('.form-control, .form-select').forEach(function(input) {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
        });
    });
    
    // تحسين الشارات
    document.querySelectorAll('.status-badge').forEach(function(badge) {
        badge.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1)';
        });
        
        badge.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
    
    // تحسين التنقل بين الشهور
    document.querySelectorAll('.nav-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
            this.style.pointerEvents = 'none';
        });
    });
    
    // إضافة تأثيرات صوتية (اختيارية)
    function playClickSound() {
        // يمكن إضافة صوت نقرة هنا إذا رغبت
        // const audio = new Audio('click-sound.mp3');
        // audio.play().catch(() => {});
    }
    
    // تحسين إمكانية الوصول
    document.querySelectorAll('.btn').forEach(function(btn) {
        btn.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
    
    // تحسين التمرير السلس للروابط الداخلية
    document.querySelectorAll('a[href^="#"]').forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // تحسين عرض الرسائل
    document.querySelectorAll('.alert').forEach(function(alert) {
        // إخفاء الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                alert.style.display = 'none';
            }, 300);
        }, 5000);
    });
    
    // تحسين الاستجابة للوحة المفاتيح
    document.addEventListener('keydown', function(e) {
        // Ctrl + F للبحث السريع
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.querySelector('input[type="search"], input[name="search"]');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // ESC لإغلاق النوافذ المنبثقة
        if (e.key === 'Escape') {
            // يمكن إضافة منطق إغلاق النوافذ المنبثقة هنا
        }
    });
    
    // تحسين الأداء - تأخير تحميل الصور
    document.querySelectorAll('img[data-src]').forEach(function(img) {
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    observer.unobserve(img);
                }
            });
        });
        observer.observe(img);
    });
    
    // تحسين التحقق من صحة النماذج
    document.querySelectorAll('form').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            let isValid = true;
            
            // التحقق من الحقول المطلوبة
            form.querySelectorAll('[required]').forEach(function(field) {
                if (!field.value.trim()) {
                    isValid = false;
                    field.style.borderColor = '#dc3545';
                    field.focus();
                } else {
                    field.style.borderColor = '#28a745';
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('⚠️ يرجى ملء جميع الحقول المطلوبة');
            }
        });
    });
    
    console.log('✅ تم تحميل سكريبت إدارة التنبيهات بنجاح');
});

// دالة مساعدة لتنسيق التواريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// دالة مساعدة لحساب الأيام المتبقية
function getDaysRemaining(endDate) {
    const today = new Date();
    const end = new Date(endDate);
    const diffTime = end - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
}

// تصدير الدوال للاستخدام العام
window.scrollToTop = function() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
};

window.formatDate = formatDate;
window.getDaysRemaining = getDaysRemaining;
