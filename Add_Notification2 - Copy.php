<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';

// الشهر والسنة المحددين (افتراضيًا الشهر الحالي)
$currentMonth = isset($_GET['month']) ? (int)$_GET['month'] : (int)date('m');
$currentYear = isset($_GET['year']) ? (int)$_GET['year'] : (int)date('Y');

$startDate = "$currentYear-" . str_pad($currentMonth, 2, '0', STR_PAD_LEFT) . "-01";
$endDate = date("Y-m-t", strtotime($startDate));

// إضافة تنبيه جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_notification']) && $isAdmin) {
    $title = $_POST['title'];
    $message = $_POST['message'];
    $notifyDate = $_POST['notify_date'];

    $stmt = $conn->prepare("INSERT INTO Notifications (Title, Message, NotifyDate) VALUES (?, ?, ?)");
    $stmt->execute([$title, $message, $notifyDate]);

    $_SESSION['success'] = "تمت إضافة التنبيه بنجاح.";
    header("Location: Add_Notification1.php");
    exit;
}

// حذف تنبيه
if (isset($_GET['delete']) && $isAdmin) {
    $id = $_GET['delete'];
    $stmt = $conn->prepare("DELETE FROM Notifications WHERE ID = ?");
    $stmt->execute([$id]);

    $_SESSION['success'] = "تم حذف التنبيه.";
    header("Location: Add_Notification1.php");
    exit;
}

// تحديث الحالة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status']) && $isAdmin) {
    $id = $_POST['id'];
    $status = $_POST['status'];
    $stmt = $conn->prepare("UPDATE Notifications SET Status = ? WHERE ID = ?");
    $stmt->execute([$status, $id]);
    $_SESSION['success'] = "تم تحديث الحالة.";
    header("Location: Add_Notification1.php?month=$currentMonth&year=$currentYear");
    exit;
}

// ترحيل المهمة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reschedule']) && $isAdmin) {
    $id = $_POST['reschedule'];
    $stmt = $conn->prepare("UPDATE Notifications SET NotifyDate = DATE_ADD(NotifyDate, INTERVAL 1 DAY) WHERE ID = ?");
    $stmt->execute([$id]);
    $_SESSION['success'] = "تم ترحيل المهمة ليوم الغد.";
    header("Location: Add_Notification1.php?month=$currentMonth&year=$currentYear");
    exit;
}

// جلب التنبيهات لهذا الشهر
$stmt = $conn->prepare("SELECT * FROM Notifications WHERE NotifyDate BETWEEN ? AND ? ORDER BY NotifyDate ASC");
$stmt->execute([$startDate, $endDate]);
$notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

// حساب الشهور السابقة والتالية
$prevMonth = $currentMonth - 1;
$prevYear = $currentYear;
$nextMonth = $currentMonth + 1;
$nextYear = $currentYear;
if ($prevMonth < 1) { $prevMonth = 12; $prevYear--; }
if ($nextMonth > 12) { $nextMonth = 1; $nextYear++; }
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة التنبيهات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
    <style>
        .center-box { max-width: 1000px; margin: auto; margin-top: 40px; }
        .highlight-today { background-color: #fff3cd !important; font-weight: bold; }
    </style>
</head>
<body>

<?php include 'header.php'; ?>

<div class="center-box">
    <div class="card shadow">
        <div class="card-header bg-primary text-white text-center">
            <h4 class="mb-0">إدارة التنبيهات</h4>
        </div>
        <div class="card-body">

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success text-center">
                    <?= $_SESSION['success']; unset($_SESSION['success']); ?>
                </div>
            <?php endif; ?>

            <?php if ($isAdmin): ?>
            <form method="POST" class="row g-3 mb-4">
                <input type="hidden" name="add_notification" value="1">
                <div class="col-md-6">
                    <label>العنوان</label>
                    <input type="text" name="title" class="form-control" required>
                </div>
                <div class="col-md-6">
                    <label>تاريخ التنبيه</label>
                    <input type="date" name="notify_date" class="form-control" required>
                </div>
                <div class="col-md-12">
                    <label>الوصف</label>
                    <textarea name="message" class="form-control" rows="3" required></textarea>
                </div>
                <div class="col-md-12 text-center">
                    <button type="submit" class="btn btn-success">إضافة تنبيه جديد</button>
                </div>
            </form>
            <hr>
            <?php endif; ?>

            <!-- التنقل بين الشهور -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <a href="?month=<?= $prevMonth ?>&year=<?= $prevYear ?>" class="btn btn-outline-secondary">&larr; الشهر السابق</a>
                <h5 class="mb-0">تنبيهات شهر <?= date("F Y", strtotime($startDate)) ?></h5>
                <a href="?month=<?= $nextMonth ?>&year=<?= $nextYear ?>" class="btn btn-outline-secondary">الشهر التالي &rarr;</a>
            </div>

            <!-- جدول التنبيهات -->
            <div class="table-responsive">
                <table class="table table-bordered text-center align-middle">
                    <thead class="table-dark">
                        <tr>
                            <th>العنوان</th>
                            <th>الوصف</th>
                            <th>تاريخ التنبيه</th>
                            <?php if ($isAdmin): ?><th>الاجراء</th><?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (count($notifications) > 0): ?>
                            <?php foreach ($notifications as $note): ?>
                                <?php $isToday = ($note['NotifyDate'] === date('Y-m-d')); ?>
                                <tr class="<?= $isToday ? 'highlight-today' : '' ?>">
                                    <td><?= htmlspecialchars($note['Title']) ?></td>
                                    <td><?= nl2br(htmlspecialchars($note['Message'])) ?></td>
                                    <td><?= $note['NotifyDate'] ?></td>
                                    <?php if ($isAdmin): ?>
                                    <td>
                                        <form method="POST" class="d-flex flex-column gap-1">
                                            <input type="hidden" name="id" value="<?= $note['ID'] ?>">
                                            <select name="status" class="form-select form-select-sm" required>
                                                <option value="تم" <?= $note['Status'] === 'تم' ? 'selected' : '' ?>>✔️ تم</option>
                                                <option value="تحت التنفيذ" <?= $note['Status'] === 'تحت التنفيذ' ? 'selected' : '' ?>>🔄 تحت التنفيذ</option>
                                                <option value="لم تنفذ" <?= $note['Status'] === 'لم تنفذ' ? 'selected' : '' ?>>❌ لم تنفذ</option>
                                            </select>
                                            <button type="submit" name="update_status" value="1" class="btn btn-sm btn-outline-primary">تحديث الحالة</button>
                                            <?php if ($note['Status'] === 'لم تنفذ'): ?>
                                                <button name="reschedule" value="<?= $note['ID'] ?>" class="btn btn-sm btn-outline-secondary">↪️ ترحيل للغد</button>
                                            <?php endif; ?>
                                            <a href="edit_notification.php?id=<?= $note['ID'] ?>" class="btn btn-sm btn-warning">✏️ تعديل</a>
                                            <a href="Add_Notification1.php?delete=<?= $note['ID'] ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من الحذف؟');">🗑 حذف</a>
                                        </form>
                                    </td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr><td colspan="<?= $isAdmin ? '4' : '3' ?>">لا توجد تنبيهات.</td></tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

        </div>
    </div>
</div>

<?php include 'footer.php'; ?>
</body>
</html>
