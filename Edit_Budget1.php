<?php
session_start();
require 'db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: /e-finance/login");
    exit;
}

if (!isset($_GET['id'])) {
    header("Location: Budget_Management.php");
    exit;
}

$id = $_GET['id'];
$stmt = $conn->prepare("SELECT * FROM Budgets WHERE ID = ?");
$stmt->execute([$id]);
$budget = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$budget) {
    echo "الموازنة غير موجودة";
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = $_POST['title'];
    $totalAmount = $_POST['total_amount'];

    $stmt = $conn->prepare("UPDATE Budgets SET Title = ?, TotalAmount = ? WHERE ID = ?");
    $stmt->execute([$title, $totalAmount, $id]);

    header("Location: Budget_Management.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تعديل الموازنة</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
</head>
<body>
<?php include 'header.php'; ?>
<div class="container mt-5">
    <h2 class="mb-4 text-center">تعديل الموازنة</h2>
    <form method="POST" class="row g-3">
        <div class="col-md-6">
            <label class="form-label">عنوان الموازنة</label>
            <input type="text" name="title" class="form-control" value="<?= htmlspecialchars($budget['Title']) ?>" required>
        </div>
        <div class="col-md-6">
            <label class="form-label">القيمة الكلية</label>
            <input type="number" name="total_amount" class="form-control" value="<?= $budget['TotalAmount'] ?>" required>
        </div>
        <div class="col-12 text-center">
            <button type="submit" class="btn btn-primary mt-3">تحديث</button>
            <a href="Budget_Management.php" class="btn btn-secondary mt-3">إلغاء</a>
        </div>
    </form>
</div></body>
</html>
