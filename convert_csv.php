<!DOCTYPE html>

<html lang="ar">

<head>

    <meta charset="UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>تحويل الفواصل</title>

</head>

<body>

    <h1 style="text-align: center;">تحويل CSV من فاصلة منقوطة إلى فاصلة عادية</h1>

    

    <form action="" method="post" enctype="multipart/form-data" style="text-align: center;">

        <label for="csvFile">اختر ملف CSV:</label>

        <input type="file" name="csvFile" id="csvFile" accept=".csv">

        <br><br>

        <button type="submit" name="convert">تحويل الملف</button>

    </form>



    <?php

    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csvFile'])) {

        // التحقق من رفع الملف

        $file = $_FILES['csvFile'];

        if ($file['error'] !== UPLOAD_ERR_OK) {

            echo "<p style='color: red; text-align: center;'>حدث خطأ أثناء رفع الملف.</p>";

            exit;

        }



        // قراءة الملف

        $inputFile = $file['tmp_name'];

        $outputFile = 'converted_' . $file['name'];



        // فتح الملف للقراءة

        $inputHandle = fopen($inputFile, 'r');

        if (!$inputHandle) {

            echo "<p style='color: red; text-align: center;'>تعذر فتح الملف.</p>";

            exit;

        }



        // فتح ملف جديد للكتابة

        $outputHandle = fopen($outputFile, 'w');

        if (!$outputHandle) {

            echo "<p style='color: red; text-align: center;'>تعذر إنشاء الملف الجديد.</p>";

            fclose($inputHandle);

            exit;

        }



        // معالجة كل صف واستبدال الفواصل

        while (($row = fgetcsv($inputHandle, 0, ';')) !== false) {

            fputcsv($outputHandle, $row, ',');

        }



        // إغلاق الملفات

        fclose($inputHandle);

        fclose($outputHandle);



        // توفير رابط لتنزيل الملف الجديد

        echo "<p style='text-align: center; color: green;'>تم تحويل الملف بنجاح!</p>";

        echo "<p style='text-align: center;'><a href='$outputFile' download>تحميل الملف الجديد</a></p>";

    }

    ?>

</body>

</html>

