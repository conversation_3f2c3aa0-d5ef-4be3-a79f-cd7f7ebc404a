<?php
require 'SimpleXLSX.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excel_file'])) {
    $filePath = $_FILES['excel_file']['tmp_name'];

    if ($xlsx = SimpleXLSX::parse($filePath)) {
        $data = $xlsx->rows();
        ?>
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>مراجعة بيانات العملاء</title>
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
        </head>
        <body>
        <div class="container mt-5">
            <h1 class="text-center">مراجعة بيانات العملاء</h1>
            <form method="post" action="insert_customers.php">
                <table class="table table-bordered mt-4">
                    <thead class="table-dark">
                        <tr>
                            <th>الاسم</th>
                            <th>الرقم القومي</th>
                            <th>رقم الحساب</th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($data as $index => $row): ?>
                        <?php if ($index === 0) continue; // تخطي العناوين ?>
                        <tr>
                            <td><input type="hidden" name="name[]" value="<?= htmlspecialchars($row[0]) ?>"><?= htmlspecialchars($row[0]) ?></td>
                            <td><input type="hidden" name="national_id[]" value="<?= htmlspecialchars($row[1]) ?>"><?= htmlspecialchars($row[1]) ?></td>
                            <td><input type="hidden" name="account_number[]" value="<?= htmlspecialchars($row[2]) ?>"><?= htmlspecialchars($row[2]) ?></td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
                <button type="submit" class="btn btn-success">تأكيد الإدخال</button>
            </form>
        </div>
        </body>
        </html>
        <?php
    } else {
        echo "خطأ: " . SimpleXLSX::parseError();
    }
}
?>
