<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /login");
    exit;
}

// التحقق من الصلاحيات
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo "<script>alert('ليس لديك صلاحية الوصول إلى هذه الصفحة.'); window.location.href = '/e-finance/index';</script>";
    exit;
}

// عرض الأخطاء أثناء التطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

// جلب العملاء من قاعدة البيانات لاستخدامها في البحث
$customersStmt = $conn->query("SELECT ID, Name FROM Customers ORDER BY Name");
$customers = $customersStmt->fetchAll(PDO::FETCH_ASSOC);

// التحقق من مدخلات البحث
$customerID = $_GET['customer_id'] ?? null;
$expenseNumber = $_GET['expense_number'] ?? null;
$authorizationNumber = $_GET['authorization_number'] ?? null;
$fromDate = $_GET['from_date'] ?? null;
$toDate = $_GET['to_date'] ?? null;
$transferType = $_GET['transfer_type'] ?? null;

$query = "SELECT Expenses.*, Customers.Name AS CustomerName
          FROM Expenses
          JOIN Customers ON Expenses.CustomerID = Customers.ID
          WHERE 1=1";

$params = [];

if (!empty($customerID)) {
    $query .= " AND Expenses.CustomerID = :customerID";
    $params[':customerID'] = $customerID;
}
if (!empty($expenseNumber)) {
    $query .= " AND Expenses.ExpenseNumber LIKE :expenseNumber";
    $params[':expenseNumber'] = "%$expenseNumber%";
}
if (!empty($authorizationNumber)) {
    $query .= " AND Expenses.AuthorizationNumber LIKE :authorizationNumber";
    $params[':authorizationNumber'] = "%$authorizationNumber%";
}
if (!empty($fromDate)) {
    $query .= " AND Expenses.Date >= :fromDate";
    $params[':fromDate'] = $fromDate;
}
if (!empty($toDate)) {
    $query .= " AND Expenses.Date <= :toDate";
    $params[':toDate'] = $toDate;
}
if (!empty($transferType)) {
    $query .= " AND Expenses.TransferType = :transferType";
    $params[':transferType'] = $transferType;
}

$query .= " ORDER BY Expenses.Date DESC, Expenses.CreatedAt DESC";

$stmt = $conn->prepare($query);
$stmt->execute($params);
$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);

// حساب الإجماليات
$totalAmount = 0;
$totalTax = 0;
$totalNet = 0;
foreach ($expenses as $expense) {
    $totalAmount += $expense['Amount'];
    $totalTax += $expense['Tax'];
    $totalNet += $expense['NetAmount'];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بحث الصرفيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        .search-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .search-card .card-header {
            background: transparent;
            border-bottom: 1px solid rgba(255,255,255,0.2);
            color: white;
        }
        .search-card .card-body {
            background: rgba(255,255,255,0.95);
            border-radius: 0 0 15px 15px;
        }
        .results-summary {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .btn-search {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        .btn-reset {
            background: linear-gradient(45deg, #6c757d, #495057);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: bold;
            color: white;
        }
    </style>
</head>
<body>
<?php include 'header.php'; ?>

<div class="container-fluid px-4">
    <!-- بطاقة البحث -->
    <div class="card search-card">
        <div class="card-header">
            <h4 class="mb-0"><i class="fas fa-search me-2"></i>بحث الصرفيات المتقدم</h4>
        </div>
        <div class="card-body">
            <form method="GET" id="searchForm">
                <div class="row g-3">
                    <div class="col-lg-3 col-md-6">
                        <label for="customer_id" class="form-label"><i class="fas fa-user me-1"></i>اسم العميل</label>
                        <select name="customer_id" id="customer_id" class="form-select">
                            <option value="">جميع العملاء</option>
                            <?php foreach ($customers as $customer) { ?>
                                <option value="<?= $customer['ID'] ?>" <?= $customer['ID'] == $customerID ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($customer['Name'], ENT_QUOTES, 'UTF-8') ?>
                                </option>
                            <?php } ?>
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="expense_number" class="form-label"><i class="fas fa-hashtag me-1"></i>رقم الصرفية</label>
                        <input type="text" name="expense_number" id="expense_number" class="form-control"
                            placeholder="ادخل رقم الصرفية"
                            value="<?= htmlspecialchars($expenseNumber ?? '', ENT_QUOTES, 'UTF-8') ?>">
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="authorization_number" class="form-label"><i class="fas fa-file-alt me-1"></i>رقم الإذن</label>
                        <input type="text" name="authorization_number" id="authorization_number" class="form-control"
                            placeholder="ادخل رقم الإذن"
                            value="<?= htmlspecialchars($authorizationNumber ?? '', ENT_QUOTES, 'UTF-8') ?>">
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="transfer_type" class="form-label"><i class="fas fa-exchange-alt me-1"></i>نوع التحويل</label>
                        <select name="transfer_type" id="transfer_type" class="form-select">
                            <option value="">جميع الأنواع</option>
                            <option value="Swift" <?= $transferType == 'Swift' ? 'selected' : '' ?>>Swift</option>
                            <option value="Internal" <?= $transferType == 'Internal' ? 'selected' : '' ?>>Internal</option>
                            <option value="Letter" <?= $transferType == 'Letter' ? 'selected' : '' ?>>Letter</option>
                        </select>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="from_date" class="form-label"><i class="fas fa-calendar-alt me-1"></i>من تاريخ</label>
                        <input type="date" name="from_date" id="from_date" class="form-control"
                            value="<?= htmlspecialchars($fromDate ?? '', ENT_QUOTES, 'UTF-8') ?>">
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <label for="to_date" class="form-label"><i class="fas fa-calendar-alt me-1"></i>إلى تاريخ</label>
                        <input type="date" name="to_date" id="to_date" class="form-control"
                            value="<?= htmlspecialchars($toDate ?? '', ENT_QUOTES, 'UTF-8') ?>">
                    </div>
                    <div class="col-lg-6 col-md-12 d-flex align-items-end">
                        <div class="btn-group w-100" role="group">
                            <button type="submit" class="btn btn-primary btn-search">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                            <button type="button" class="btn btn-secondary btn-reset" onclick="resetForm()">
                                <i class="fas fa-undo me-2"></i>إعادة تعيين
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- ملخص النتائج -->
    <?php if (!empty($expenses)) { ?>
    <div class="results-summary">
        <div class="row text-center">
            <div class="col-md-3">
                <h5><i class="fas fa-list-ol me-2"></i>عدد الصرفيات</h5>
                <h3><?= count($expenses) ?></h3>
            </div>
            <div class="col-md-3">
                <h5><i class="fas fa-money-bill-wave me-2"></i>إجمالي المبلغ</h5>
                <h3><?= number_format($totalAmount, 2) ?> ج.م</h3>
            </div>
            <div class="col-md-3">
                <h5><i class="fas fa-percentage me-2"></i>إجمالي الضريبة</h5>
                <h3><?= number_format($totalTax, 2) ?> ج.م</h3>
            </div>
            <div class="col-md-3">
                <h5><i class="fas fa-calculator me-2"></i>صافي المبلغ</h5>
                <h3><?= number_format($totalNet, 2) ?> ج.م</h3>
            </div>
        </div>
    </div>
    <?php } ?>

    <!-- جدول النتائج -->
    <div class="table-container">
        <div class="d-flex justify-content-between align-items-center p-3 bg-light">
            <h4 class="mb-0"><i class="fas fa-table me-2"></i>نتائج البحث</h4>
            <?php if (!empty($expenses)) { ?>
            <div class="btn-group">
                <button class="btn btn-success btn-sm" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-1"></i>تصدير Excel
                </button>
                <button class="btn btn-info btn-sm" onclick="printTable()">
                    <i class="fas fa-print me-1"></i>طباعة
                </button>
            </div>
            <?php } ?>
        </div>

        <?php if (empty($expenses)) { ?>
        <div class="text-center p-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد نتائج</h4>
            <p class="text-muted">لم يتم العثور على صرفيات تطابق معايير البحث المحددة</p>
        </div>
        <?php } else { ?>
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="expensesTable">
                <thead class="table-dark">
                    <tr>
                        <th class="text-center">#</th>
                        <th>رقم الصرفية</th>
                        <th>اسم العميل</th>
                        <th>المبلغ</th>
                        <th>الضريبة</th>
                        <th>المبلغ الصافي</th>
                        <th>تاريخ الصرف</th>
                        <th>الوصف</th>
                        <th>رقم الإذن</th>
                        <th>ملف</th>
                        <th>نوع التحويل</th>
                        <th>ملاحظات</th>
                        <th class="text-center">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($expenses as $index => $expense) { ?>
                        <tr>
                            <td class="text-center"><?= $index + 1 ?></td>
                            <td>
                                <span class="badge bg-primary"><?= htmlspecialchars($expense['ExpenseNumber'], ENT_QUOTES, 'UTF-8') ?></span>
                            </td>
                            <td>
                                <strong><?= htmlspecialchars($expense['CustomerName'], ENT_QUOTES, 'UTF-8') ?></strong>
                            </td>
                            <td class="text-end">
                                <span class="text-success fw-bold"><?= number_format($expense['Amount'], 2) ?> ج.م</span>
                            </td>
                            <td class="text-end">
                                <span class="text-warning fw-bold"><?= number_format($expense['Tax'], 2) ?> ج.م</span>
                            </td>
                            <td class="text-end">
                                <span class="text-primary fw-bold"><?= number_format($expense['NetAmount'], 2) ?> ج.م</span>
                            </td>
                            <td>
                                <i class="fas fa-calendar-alt me-1"></i>
                                <?= date('d/m/Y', strtotime($expense['Date'])) ?>
                            </td>
                            <td>
                                <span class="text-truncate" style="max-width: 150px;" title="<?= htmlspecialchars($expense['Description'] ?? '', ENT_QUOTES, 'UTF-8') ?>">
                                    <?= htmlspecialchars(substr($expense['Description'] ?? 'لا يوجد', 0, 30), ENT_QUOTES, 'UTF-8') ?>
                                    <?= strlen($expense['Description'] ?? '') > 30 ? '...' : '' ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-info"><?= htmlspecialchars($expense['AuthorizationNumber'], ENT_QUOTES, 'UTF-8') ?></span>
                            </td>
                            <td class="text-center">
                                <?php if (!empty($expense['File'])) { ?>
                                    <a href="uploads/<?= htmlspecialchars($expense['File'], ENT_QUOTES, 'UTF-8') ?>"
                                       class="btn btn-sm btn-outline-info" target="_blank" title="عرض الملف">
                                        <i class="fas fa-file-alt"></i>
                                    </a>
                                <?php } else { ?>
                                    <span class="text-muted">-</span>
                                <?php } ?>
                            </td>
                            <td>
                                <?php
                                $transferTypeClass = '';
                                switch($expense['TransferType']) {
                                    case 'Swift': $transferTypeClass = 'bg-success'; break;
                                    case 'Internal': $transferTypeClass = 'bg-warning'; break;
                                    case 'Letter': $transferTypeClass = 'bg-secondary'; break;
                                    default: $transferTypeClass = 'bg-light text-dark';
                                }
                                ?>
                                <span class="badge <?= $transferTypeClass ?>">
                                    <?= htmlspecialchars($expense['TransferType'] ?? 'غير محدد', ENT_QUOTES, 'UTF-8') ?>
                                </span>
                            </td>
                            <td>
                                <span class="text-truncate" style="max-width: 100px;" title="<?= htmlspecialchars($expense['Notes'] ?? '', ENT_QUOTES, 'UTF-8') ?>">
                                    <?= htmlspecialchars(substr($expense['Notes'] ?? 'لا توجد', 0, 20), ENT_QUOTES, 'UTF-8') ?>
                                    <?= strlen($expense['Notes'] ?? '') > 20 ? '...' : '' ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="edit_expense.php?id=<?= $expense['ID'] ?>"
                                       class="btn btn-warning btn-sm" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-danger btn-sm"
                                            onclick="confirmDelete(<?= $expense['ID'] ?>)" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php } ?>
                </tbody>
            </table>
        </div>
        <?php } ?>
    </div>
</div>

<!-- زر العودة للأعلى -->
<button id="scrollBtn" onclick="scrollToTop()" title="العودة للأعلى">
    <i class="fas fa-arrow-up"></i>
</button>

<?php include 'footer.php'; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// إعادة تعيين النموذج
function resetForm() {
    document.getElementById('searchForm').reset();
    window.location.href = 'search_exp.php';
}

// تأكيد الحذف
function confirmDelete(expenseId) {
    if (confirm('هل أنت متأكد من حذف هذه الصرفية؟\nلا يمكن التراجع عن هذا الإجراء.')) {
        window.location.href = 'delete_expense.php?id=' + expenseId;
    }
}

// تصدير إلى Excel
function exportToExcel() {
    const table = document.getElementById('expensesTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "الصرفيات"});
    const filename = 'صرفيات_' + new Date().toISOString().slice(0,10) + '.xlsx';
    XLSX.writeFile(wb, filename);
}

// طباعة الجدول
function printTable() {
    const printContent = document.querySelector('.table-container').innerHTML;
    const originalContent = document.body.innerHTML;

    document.body.innerHTML = `
        <html dir="rtl">
        <head>
            <title>طباعة الصرفيات</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                th { background-color: #f2f2f2; }
                .btn-group { display: none; }
                @media print {
                    .btn { display: none; }
                }
            </style>
        </head>
        <body>
            <h2 style="text-align: center;">تقرير الصرفيات</h2>
            <p style="text-align: center;">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
            ${printContent}
        </body>
        </html>
    `;

    window.print();
    document.body.innerHTML = originalContent;
    location.reload();
}

// زر العودة للأعلى
window.onscroll = function() {
    const scrollBtn = document.getElementById("scrollBtn");
    if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
        scrollBtn.style.display = "block";
    } else {
        scrollBtn.style.display = "none";
    }
};

function scrollToTop() {
    document.body.scrollTop = 0;
    document.documentElement.scrollTop = 0;
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للصفوف
    const tableRows = document.querySelectorAll('#expensesTable tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // تحسين عرض التواريخ
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.name === 'from_date') {
                const toDate = document.getElementById('to_date');
                if (toDate.value && this.value > toDate.value) {
                    alert('تاريخ البداية لا يمكن أن يكون أكبر من تاريخ النهاية');
                    this.value = '';
                }
            } else if (this.name === 'to_date') {
                const fromDate = document.getElementById('from_date');
                if (fromDate.value && this.value < fromDate.value) {
                    alert('تاريخ النهاية لا يمكن أن يكون أصغر من تاريخ البداية');
                    this.value = '';
                }
            }
        });
    });

    // إضافة تلميحات للأزرار
    const tooltips = document.querySelectorAll('[title]');
    tooltips.forEach(element => {
        element.setAttribute('data-bs-toggle', 'tooltip');
    });

    // تفعيل Bootstrap tooltips
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
});

// إضافة مكتبة XLSX للتصدير (يمكن إضافتها من CDN)
if (typeof XLSX === 'undefined') {
    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
    document.head.appendChild(script);
}
</script>
</body>
</html>
