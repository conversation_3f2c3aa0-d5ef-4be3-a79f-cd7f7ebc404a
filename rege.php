<?php
session_start();
require 'db.php'; // ملف الاتصال بقاعدة البيانات

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التعامل مع رفع ملف CSV
    if (!empty($_FILES['csv_file']['name'])) {
        $target_dir = "uploads/";
        $file_path = $target_dir . basename($_FILES["csv_file"]["name"]);
        move_uploaded_file($_FILES["csv_file"]["tmp_name"], $file_path);
        
        // قراءة محتويات الملف
        if (($handle = fopen($file_path, "r")) !== FALSE) {
            // تخطي السطر الأول إذا كان يحتوي على رؤوس الأعمدة
            fgetcsv($handle); 

            // قراءة البيانات من الملف وإدخالها في قاعدة البيانات
            while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $stmt = $conn->prepare("INSERT INTO Customers 
                    (Name, NationalID, AccountNumber, Amount, Tax, Net, PaymentDuration, StartDate, EndDate, FilePath, AuthorizationNumber, TransferNumber, DocumentCount, Notes, Category, CardExpiry) 
                    VALUES 
                    (:name, :national_id, :account_number, :amount, :tax, :net, :duration, :start_date, :end_date, :file_path, :auth_number, :transfer_number, :document_count, :notes, :category, :card_expiry)");

                // ربط البيانات من الملف بالمتغيرات
                $stmt->bindParam(':name', $data[0]);
                $stmt->bindParam(':national_id', $data[1]);
                $stmt->bindParam(':account_number', $data[2]);
                $stmt->bindParam(':amount', $data[3]);
                $stmt->bindParam(':tax', $data[4]);
                $stmt->bindParam(':net', $data[5]);
                $stmt->bindParam(':duration', $data[6]);
                $stmt->bindParam(':start_date', $data[7]);
                $stmt->bindParam(':end_date', $data[8]);
                $stmt->bindParam(':file_path', $file_path);
                $stmt->bindParam(':auth_number', $data[9]);
                $stmt->bindParam(':transfer_number', $data[10]);
                $stmt->bindParam(':document_count', $data[11]);
                $stmt->bindParam(':notes', $data[12]);
                $stmt->bindParam(':category', $data[13]);
                $stmt->bindParam(':card_expiry', $data[14]);
                
                if (!$stmt->execute()) {
                    echo "<script>alert('خطأ أثناء تسجيل بيانات العميل: " . $data[0] . "');</script>";
                }
            }
            fclose($handle);
            echo "<script>alert('تم رفع البيانات بنجاح!');</script>";
        } else {
            echo "<script>alert('خطأ في قراءة الملف.');</script>";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع بيانات العملاء دفعة واحدة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<?php include 'header.php'; ?>
<div class="container mt-5">
    <h1 class="text-center">رفع بيانات العملاء دفعة واحدة</h1>
    <form method="POST" enctype="multipart/form-data">
        <div class="mb-3">
            <label for="csv_file" class="form-label">اختار ملف CSV لرفع البيانات</label>
            <input type="file" name="csv_file" id="csv_file" class="form-control" accept=".csv" required>
        </div>
        <button type="submit" class="btn btn-primary mt-3">رفع البيانات</button>
    </form>
</div>
<?php include 'footer.php'; ?>
</body>
</html>
