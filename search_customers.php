<?php
require 'db.php';

$term = $_GET['term'] ?? '';

$stmt = $conn->prepare("SELECT ID, Name FROM Customers WHERE Name LIKE ? LIMIT 10");
$stmt->execute(["%$term%"]);

$results = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $results[] = [
        'id' => $row['ID'],
        'label' => $row['Name'], // ما يُعرض للمستخدم
        'value' => $row['Name'], // ما يُملأ في الحقل تلقائيًا
    ];
}

echo json_encode($results);
