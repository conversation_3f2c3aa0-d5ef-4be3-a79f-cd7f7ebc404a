/* ===== إعدادات عامة ===== */
:root {
    --primary-color: #000000;
    --secondary-color: #007bff;
    --success-color: #007bff;
    --warning-color: #007bff;
    --danger-color: #000000;
    --info-color: #007bff;
    --light-color: #f8f9fa;
    --dark-color: #000000;
    --white-color: #ffffff;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e3f2fd 100%);
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
    scroll-behavior: smooth;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.container {
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 0 15px;
}

main {
    flex: 1;
}

.text-center {
    text-align: center;
}

/* ===== تحسينات الجداول ===== */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    margin-top: 20px;
    background: var(--white-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table th,
.table td {
    padding: 12px 15px;
    text-align: center;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
}

.table th {
    background: linear-gradient(135deg, var(--primary-color), #333333);
    color: var(--white-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.table-responsive {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

/* ===== تحسينات النماذج والمدخلات ===== */
.input-group {
    margin-bottom: 20px;
    position: relative;
}

.input-group .form-control {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    border-right: 0;
    transition: var(--transition);
    border: 2px solid #e9ecef;
}

.input-group .form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    z-index: 3;
}

.input-group .btn {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    border: 2px solid var(--secondary-color);
    border-left: 0;
}

.navbar {
    margin-bottom: 20px;
}

.navbar a {
    margin-right: 10px;
    padding: 8px 15px;
    color: white;
    background-color: #59bf36;
    text-decoration: none;
    border-radius: 5px;
}

.navbar a:hover {
    background-color: #5abf37;
}

.form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 0.95rem;
    display: block;
}

.form-control,
.form-select {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 12px 15px;
    font-size: 0.95rem;
    transition: var(--transition);
    background-color: var(--white-color);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
    transform: translateY(-2px);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 0.75rem center;
    background-size: 16px 12px;
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

/* ===== تحسينات الأزرار ===== */
button, .btn {
    border-radius: var(--border-radius);
    text-align: center;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
    padding: 12px 24px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.mt-4.mb-4{
    text-align: center;
}

.btn.btn-primary.mt-3{
    width: 100%;
    font-size: 1.1rem;
    font-weight: 600;
    padding: 15px 24px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--secondary-color), #0056b3);
    color: var(--white-color);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, var(--secondary-color));
    color: var(--white-color);
}

button:focus,
input:focus,
select:focus,
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(5, 107, 7, 0.25);
}

.btn-success {
    background: linear-gradient(135deg, var(--secondary-color), #0056b3);
    color: var(--white-color);
}

.btn-success:hover {
    background: linear-gradient(135deg, #0056b3, var(--secondary-color));
    color: var(--white-color);
}

.btn-warning {
    background: linear-gradient(135deg, var(--secondary-color), #0056b3);
    color: var(--white-color);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #0056b3, var(--secondary-color));
    color: var(--white-color);
}

.btn-danger {
    background: linear-gradient(135deg, var(--primary-color), #333333);
    color: var(--white-color);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #333333, var(--primary-color));
    color: var(--white-color);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: var(--white-color);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #7f8c8d, #95a5a6);
    color: var(--white-color);
}

/* ===== تحسينات التنبيهات ===== */
.alert {
    padding: 15px 20px;
    margin-bottom: 20px;
    border: none;
    border-radius: var(--border-radius);
    border-left: 4px solid;
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255,255,255,0.3), transparent);
}

.alert-success {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-left-color: var(--secondary-color);
    color: #0d47a1;
}

.alert-danger {
    background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
    border-left-color: var(--primary-color);
    color: #000000;
}

.alert-warning {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-left-color: var(--secondary-color);
    color: #0d47a1;
}

.alert-info {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-left-color: var(--secondary-color);
    color: #0d47a1;
}
/* ===== تحسينات البطاقات ===== */
.card {
    background: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), #333333);
    color: var(--white-color);
    font-weight: 600;
    padding: 15px 20px;
    border-bottom: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-body {
    padding: 20px;
}

.card-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 15px;
}

.center {
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
}

.cardCenter{
    display: grid;
    justify-items: center;
    margin-bottom: 100px;
}


   /* .table-responsive {
        overflow-x: auto;
    }
    .table-custom {
        width: 100%;
        min-width: 1200px;
        border-collapse: collapse;
        font-size: 15px;
    }
    .table-custom th, .table-custom td {
        white-space: nowrap;
        padding: 10px 12px;
        vertical-align: middle;
    }
    .table-custom th {
        background-color: #343a40;
        color: white;
        text-align: center;
    }
    .table-custom tr:nth-child(even) {
        background-color: #f9f9f9;
    }
    .table-custom tr:hover {
        background-color: #f1f1f1;
    }
    .action-buttons .btn {
        margin: 2px;
    }*/

#scrollBtn {
  position: fixed;
  bottom: 30px;
  left: 30px;
  z-index: 100;
  font-size: 24px;
  background-color: #0d6efd;
  color: white;
  border: none;
  border-radius: 50%;
  width: 45px;
  height: 45px;
  cursor: pointer;
  display: none;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

#scrollBtn:hover {
  background-color: #0b5ed7;
}

/* تحسينات إضافية للجداول */
.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
    min-width: 1200px; /* ضمان عرض كامل للجدول */
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border: 1px solid #dee2e6;
    text-align: center;
    white-space: nowrap;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #343a40;
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody + tbody {
    border-top: 2px solid #dee2e6;
}

.table-hover tbody tr:hover {
    color: #212529;
    background-color: rgba(0, 123, 255, 0.1);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* تحسينات خاصة لصفحة البحث */
.container-fluid {
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
}

.table-container {
    width: 100%;
    margin: 0;
}

.table-container .table-responsive {
    border-radius: 0 0 15px 15px;
}

/* تحسين عرض الأعمدة */
.table th:nth-child(1), .table td:nth-child(1) { width: 50px; } /* # */
.table th:nth-child(2), .table td:nth-child(2) { width: 120px; } /* رقم الصرفية */
.table th:nth-child(3), .table td:nth-child(3) { width: 150px; } /* اسم العميل */
.table th:nth-child(4), .table td:nth-child(4) { width: 100px; } /* المبلغ */
.table th:nth-child(5), .table td:nth-child(5) { width: 100px; } /* الضريبة */
.table th:nth-child(6), .table td:nth-child(6) { width: 120px; } /* المبلغ الصافي */
.table th:nth-child(7), .table td:nth-child(7) { width: 100px; } /* تاريخ الصرف */
.table th:nth-child(8), .table td:nth-child(8) { width: 200px; } /* الوصف */
.table th:nth-child(9), .table td:nth-child(9) { width: 100px; } /* رقم الإذن */
.table th:nth-child(10), .table td:nth-child(10) { width: 80px; } /* ملف */
.table th:nth-child(11), .table td:nth-child(11) { width: 100px; } /* نوع التحويل */
.table th:nth-child(12), .table td:nth-child(12) { width: 150px; } /* ملاحظات */
.table th:nth-child(13), .table td:nth-child(13) { width: 120px; } /* الإجراءات */

/* تحسينات للأزرار */
.btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.375rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn:hover {
    color: #212529;
    text-decoration: none;
}

.btn:focus,
.btn.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسينات للنماذج */
.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: #212529;
    background-color: #fff;
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تحسينات للبطاقات */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.375rem;
}

.card-header {
    padding: 0.5rem 1rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-body {
    flex: 1 1 auto;
    padding: 1rem 1rem;
}

/* تحسينات للتنبيهات */
.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
}

.alert-dismissible {
    padding-right: 4rem;
}

.alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 0.9375rem 1.25rem;
}

/* تحسينات للشارات */
.badge {
    display: inline-block;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

/* تحسينات للأيقونات */
.fas, .far, .fab {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands";
    font-weight: 900;
}

/* ===== تحسينات الاستجابة للأجهزة المحمولة ===== */
@media (max-width: 1200px) {
    .container, .container-fluid {
        max-width: 100%;
        padding-left: 15px;
        padding-right: 15px;
    }

    .table-responsive {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
}

@media (max-width: 992px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .stat-card {
        margin-bottom: 15px;
    }

    .table {
        font-size: 0.9rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .container, .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-section {
        padding: 25px;
        margin: 20px 0;
    }

    .tasks-container,
    .budget-container,
    .chart-container {
        padding: 20px;
        margin: 20px 0;
    }

    .table-responsive {
        font-size: 0.875rem;
        border-radius: 8px;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .table {
        min-width: 800px;
        font-size: 0.8rem;
    }

    .table th,
    .table td {
        padding: 8px 6px;
        white-space: nowrap;
    }

    .btn {
        padding: 8px 16px;
        font-size: 0.875rem;
        margin: 2px;
    }

    .btn-group .btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    .card-body {
        padding: 15px;
    }

    .search-card .card-body {
        padding: 20px;
    }

    .form-control,
    .form-select {
        padding: 10px 12px;
        font-size: 0.9rem;
    }

    .form-label {
        font-size: 0.85rem;
        margin-bottom: 5px;
    }

    .results-summary {
        padding: 15px;
    }

    .results-summary h5 {
        font-size: 0.9rem;
    }

    .results-summary h3 {
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-icon {
        font-size: 2rem;
    }

    /* إخفاء بعض الأعمدة في الشاشات الصغيرة */
    .table th:nth-child(8), .table td:nth-child(8), /* الوصف */
    .table th:nth-child(12), .table td:nth-child(12), /* ملاحظات */
    .table th:nth-child(15), .table td:nth-child(15) { /* ملاحظات إضافية */
        display: none;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 1.8rem;
    }

    .hero-section {
        padding: 20px;
        margin: 15px 0;
    }

    .tasks-container,
    .budget-container,
    .chart-container {
        padding: 15px;
        margin: 15px 0;
    }

    .table {
        min-width: 600px;
        font-size: 0.75rem;
    }

    .table th,
    .table td {
        padding: 6px 4px;
    }

    .btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        margin: 1px;
    }

    .form-control,
    .form-select {
        padding: 8px 10px;
        font-size: 0.85rem;
    }

    .card-body {
        padding: 12px;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    .stat-icon {
        font-size: 1.8rem;
    }

    /* إخفاء المزيد من الأعمدة في الشاشات الصغيرة جداً */
    .table th:nth-child(6), .table td:nth-child(6), /* تاريخ النهاية */
    .table th:nth-child(7), .table td:nth-child(7), /* مدة الصرف */
    .table th:nth-child(9), .table td:nth-child(9), /* رقم الإذن */
    .table th:nth-child(10), .table td:nth-child(10) { /* رقم التحويل */
        display: none;
    }
}

/* تحسينات إضافية للطباعة */
@media print {
    body {
        background: white !important;
        color: black !important;
    }

    .navbar,
    .btn,
    .search-form {
        display: none !important;
    }

    .table {
        font-size: 0.8rem;
    }

    .table th,
    .table td {
        padding: 4px;
        border: 1px solid #000 !important;
    }

    .table th {
        background: #f0f0f0 !important;
        color: black !important;
    }
}

@media (max-width: 576px) {
    .table {
        min-width: 600px;
    }

    /* إخفاء المزيد من الأعمدة في الشاشات الأصغر */
    .table th:nth-child(5), .table td:nth-child(5), /* الضريبة */
    .table th:nth-child(9), .table td:nth-child(9), /* رقم الإذن */
    .table th:nth-child(11), .table td:nth-child(11) { /* نوع التحويل */
        display: none;
    }

    .btn-group .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }

    .results-summary .col-md-3 {
        margin-bottom: 15px;
    }
}
