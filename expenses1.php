<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

// عرض الأخطاء للتصحيح أثناء التطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

// التحقق من الصلاحيات
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo "<script>alert('ليس لديك صلاحية الوصول إلى هذه الصفحة.'); window.location.href = '/e-finance/index';</script>";
    exit;
}

// التعامل مع طلب الإضافة
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // التحقق من الحقول المطلوبة
        $requiredFields = [
            'customer_id',
            'authorization_number',
            'account_number',
            'amount',
            'date',
            'payment_date',
            'transfer_number',
            'transfer_type',
            'description',
        ];

        $missingFields = [];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            throw new Exception('الحقول التالية مفقودة أو غير صحيحة: ' . implode(', ', $missingFields));
        }

        $customerID = !empty($_POST['customer_id']) ? $_POST['customer_id'] : null;
        $authorizationNumber = $_POST['authorization_number'];
        $accountNumber = $_POST['account_number'];
        $amount = $_POST['amount'];
        $tax = !empty($_POST['tax']) ? $_POST['tax'] : 0; // افتراض أن الضريبة صفر إذا كانت فارغة
        $netAmount = $amount - $tax;
        $date = $_POST['date'];
        $paymentDate = $_POST['payment_date'];
        $transferNumber = $_POST['transfer_number'];
        $transferType = $_POST['transfer_type'];
        $expenseNumber = 'EXP' . time(); // رقم صرفية مميز
        $notes = $_POST['notes'] ?? null;
        $description = $_POST['description'];
        $createdAt = date('Y-m-d H:i:s'); // تاريخ الإضافة
        $filePath = null;

        // معالجة رفع الملف
        if (!empty($_FILES['file']['name'])) {
            $fileName = time() . '_' . $_FILES['file']['name'];
            $targetFile = 'uploads/' . $fileName;
            if (move_uploaded_file($_FILES['file']['tmp_name'], $targetFile)) {
                $filePath = $fileName;
            } else {
                throw new Exception('حدث خطأ أثناء تحميل الملف.');
            }
        }

        // استعلام الإضافة
        $stmt = $conn->prepare("INSERT INTO Expenses 
            (CustomerID, AuthorizationNumber, AccountNumber, Amount, Tax, NetAmount, Date, PaymentDate, 
             TransferNumber, TransferType, ExpenseNumber, File, Notes, CreatedAt, Description) 
            VALUES 
            (:customerID, :authorizationNumber, :accountNumber, :amount, :tax, :netAmount, :date, :paymentDate, 
             :transferNumber, :transferType, :expenseNumber, :filePath, :notes, :createdAt, :description)");

        $stmt->bindParam(':customerID', $customerID);
        $stmt->bindParam(':authorizationNumber', $authorizationNumber);
        $stmt->bindParam(':accountNumber', $accountNumber);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':tax', $tax);
        $stmt->bindParam(':netAmount', $netAmount);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':paymentDate', $paymentDate);
        $stmt->bindParam(':transferNumber', $transferNumber);
        $stmt->bindParam(':transferType', $transferType);
        $stmt->bindParam(':expenseNumber', $expenseNumber);
        $stmt->bindParam(':filePath', $filePath);
        $stmt->bindParam(':notes', $notes);
        $stmt->bindParam(':createdAt', $createdAt);
        $stmt->bindParam(':description', $description);

        if ($stmt->execute()) {
            echo "<script>alert('تمت إضافة الصرفية بنجاح!'); window.location.href='expenses.php';</script>";
        } else {
            throw new Exception('خطأ أثناء تنفيذ الاستعلام.');
        }
    } catch (Exception $e) {
        echo "<div style='color: red;'><strong>حدث خطأ:</strong> " . $e->getMessage() . "</div>";
    }
}
// جلب العملاء
$customersStmt = $conn->query("SELECT ID, Name FROM Customers");
$customers = $customersStmt->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> الصرفيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
<?php include 'header.php'; ?>

<div class="container">
    <h1 class="text-center mb-4">إضافة صرفية جديدة</h1>

    <form method="POST" enctype="multipart/form-data" class="mb-5">
        <div class="row g-3">
            <div class="col-md-4">
                
                <label for="customer_name" class="form-label">اسم العميل</label>
<input type="text" name="customer_name" id="customer_name" class="form-control" autocomplete="off">
<div id="customer_list" class="list-group position-absolute"></div>
<input type="hidden" name="customer_id" id="customer_id"> <!-- لتخزين ID العميل -->

            </div>
            <div class="col-md-4">
                <label for="authorization_number" class="form-label">رقم الإذن</label>
                <input type="text" name="authorization_number" id="authorization_number" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="account_number" class="form-label">رقم الحساب</label>
                <input type="text" name="account_number" id="account_number" class="form-control" required>
            </div>
        </div>

        <div class="row g-3 mt-3">
            <div class="col-md-4">
                <label for="amount" class="form-label">المبلغ</label>
                <input type="number" name="amount" id="amount" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="tax" class="form-label">الضريبة</label>
                <input type="number" name="tax" id="tax" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="date" class="form-label">التاريخ</label>
                <input type="date" name="date" id="date" class="form-control" required>
            </div>
        </div>

        <div class="row g-3 mt-3">
            <div class="col-md-4">
                <label for="payment_date" class="form-label">تاريخ الدفع</label>
                <input type="date" name="payment_date" id="payment_date" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="transfer_number" class="form-label">رقم التحويل</label>
                <input type="text" name="transfer_number" id="transfer_number" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="transfer_type" class="form-label">نوع التحويل</label>
                <input type="text" name="transfer_type" id="transfer_type" class="form-control" required>
            </div>
        </div>

        <div class="mt-3">
            <label for="description" class="form-label">وصف</label>
            <textarea name="description" id="description" class="form-control" rows="3" required></textarea>
        </div>
        <div class="mt-3">
            <label for="file" class="form-label">ملف (اختياري)</label>
            <input type="file" name="file" id="file" class="form-control">
        </div>
        <div class="mt-3">
            <label for="notes" class="form-label">ملاحظات</label>
            <textarea name="notes" id="notes" class="form-control" rows="2"></textarea>
        </div>
        <button type="submit" class="btn btn-primary mt-4">إضافة</button>
    </form>
</div><script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    $("#customer_name").on("input", function() {
        let query = $(this).val();
        if (query.length > 1) {
            $.ajax({
                url: "autocomplete.php",
                method: "GET",
                data: { query: query },
                success: function(data) {
                    let customers = JSON.parse(data);
                    let list = $("#customer_list");
                    list.empty();
                    customers.forEach(customer => {
                        list.append(`<a href="#" class="list-group-item list-group-item-action" data-id="${customer.ID}">${customer.Name}</a>`);
                    });
                    list.show();
                }
            });
        } else {
            $("#customer_list").hide();
        }
    });

    $(document).on("click", "#customer_list a", function(e) {
        e.preventDefault();
        $("#customer_name").val($(this).text());
        $("#customer_id").val($(this).data("id"));
        $("#customer_list").hide();
    });

    $(document).click(function(e) {
        if (!$(e.target).closest("#customer_name, #customer_list").length) {
            $("#customer_list").hide();
        }
    });
});
</script>

</body>
</html>
