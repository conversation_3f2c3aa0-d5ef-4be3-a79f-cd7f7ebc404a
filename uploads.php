<?php
require 'db.php'; // ملف الاتصال بقاعدة البيانات



require 'vendor/autoload.php'; // تحميل مكتبة PhpSpreadsheet

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;


if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['file'])) {
    $fileTmpName = $_FILES['file']['tmp_name'];
    $fileName = $_FILES['file']['name'];
    $fileType = $_FILES['file']['type'];
    $filePath = 'uploads/' . $fileName;

    // نقل الملف المرفوع إلى المجلد المناسب
    if (move_uploaded_file($fileTmpName, $filePath)) {
        try {
            // قراءة الملف Excel
            $spreadsheet = IOFactory::load($filePath);
            $sheet = $spreadsheet->getActiveSheet();
            $rows = $sheet->toArray();

            // تجاوز العنوان في الصف الأول
            foreach ($rows as $row) {
                // افترض أن الأعمدة مرتبة بهذا الشكل في ملف الإكسل
                $name = $row[0];
                $nationalID = $row[1];
                $accountNumber = $row[2];
                $amount = $row[3];
                $tax = $row[4];
                $net = $row[5];
                $paymentDuration = $row[6];
                $startDate = $row[7];
                $endDate = $row[8];
                $authorizationNumber = $row[9];
                $transferNumber = $row[10];
                $documentCount = $row[11];
                $notes = $row[12];
                $category = $row[13];

                // إدخال البيانات في قاعدة البيانات
                $stmt = $conn->prepare("INSERT INTO excel_data (Name, NationalID, AccountNumber, Amount, Tax, Net, PaymentDuration, StartDate, EndDate, FilePath, AuthorizationNumber, TransferNumber, DocumentCount, Notes, Category) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->bind_param("sssdssssssssss", $name, $nationalID, $accountNumber, $amount, $tax, $net, $paymentDuration, $startDate, $endDate, $filePath, $authorizationNumber, $transferNumber, $documentCount, $notes, $category);
                $stmt->execute();
            }

            echo "تم رفع البيانات بنجاح!";
        } catch (Exception $e) {
            echo "خطأ في قراءة الملف: " . $e->getMessage();
        }
    } else {
        echo "فشل في تحميل الملف.";
    }
} else {
?>
    <form method="POST" enctype="multipart/form-data">
        <label for="file">رفع ملف Excel:</label>
        <input type="file" name="file" id="file" required>
        <button type="submit">رفع البيانات</button>
    </form>
<?php
}
?>

