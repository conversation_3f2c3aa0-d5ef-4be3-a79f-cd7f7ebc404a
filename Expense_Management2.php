<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

// التحقق من الصلاحيات
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo "<script>alert('ليس لديك صلاحية الوصول إلى هذه الصفحة.'); window.location.href = '/e-finance/index';</script>";
    exit;
}

// جلب الموازنات
$budgets = $conn->query("SELECT ID, Title, TotalAmount, SpentAmount FROM Budgets")->fetchAll(PDO::FETCH_ASSOC);

// جلب سجل الصرفيات
$conditions = [];
$params = [];

if (!empty($_GET['from_date'])) {
    $conditions[] = "e.Date >= :from_date";
    $params[':from_date'] = $_GET['from_date'];
}

if (!empty($_GET['to_date'])) {
    $conditions[] = "e.Date <= :to_date";
    $params[':to_date'] = $_GET['to_date'];
}

if (!empty($_GET['customer_search'])) {
    $conditions[] = "c.Name LIKE :customer_search";
    $params[':customer_search'] = "%" . $_GET['customer_search'] . "%";
}

$where = $conditions ? "WHERE " . implode(" AND ", $conditions) : "";

$sql = "SELECT e.*, c.Name AS CustomerName, b.Title AS BudgetTitle 
        FROM Expenses e 
        JOIN Customers c ON e.CustomerID = c.ID 
        JOIN Budgets b ON e.BudgetID = b.ID 
        $where
        ORDER BY e.CreatedAt DESC";

$stmt = $conn->prepare($sql);
$stmt->execute($params);
$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);


// جلب العملاء
$customers = $conn->query("SELECT ID, Name FROM Customers")->fetchAll(PDO::FETCH_ASSOC);

// معالجة الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $customerID = $_POST['customer_id'];
        $budgetID = $_POST['budget_id'];
        $amount = $_POST['amount'];
        $tax = $_POST['tax'] ?? 0;
        $netAmount = $amount - $tax;

        // جلب الموازنة المختارة
        $budgetStmt = $conn->prepare("SELECT TotalAmount, SpentAmount FROM Budgets WHERE ID = ?");
        $budgetStmt->execute([$budgetID]);
        $budget = $budgetStmt->fetch(PDO::FETCH_ASSOC);

        $remaining = $budget['TotalAmount'] - $budget['SpentAmount'];

        if ($netAmount > $remaining) {
            throw new Exception("المبلغ يتجاوز الموازنة المتاحة.");
        }

        // إدخال بيانات الصرف
        $stmt = $conn->prepare("INSERT INTO Expenses 
            (CustomerID, AuthorizationNumber, AccountNumber, Amount, Tax, NetAmount, Date, PaymentDate, 
            TransferNumber, TransferType, ExpenseNumber, File, Notes, CreatedAt, Description, BudgetID) 
            VALUES 
            (:customerID, :authorizationNumber, :accountNumber, :amount, :tax, :netAmount, :date, :paymentDate, 
            :transferNumber, :transferType, :expenseNumber, :file, :notes, :createdAt, :description, :budgetID)");

        $stmt->execute([
            ':customerID' => $customerID,
            ':authorizationNumber' => $_POST['authorization_number'],
            ':accountNumber' => $_POST['account_number'],
            ':amount' => $amount,
            ':tax' => $tax,
            ':netAmount' => $netAmount,
            ':date' => $_POST['date'],
            ':paymentDate' => $_POST['payment_date'],
            ':transferNumber' => $_POST['transfer_number'],
            ':transferType' => $_POST['transfer_type'],
            ':expenseNumber' => 'EXP' . time(),
            ':file' => null,
            ':notes' => $_POST['notes'] ?? null,
            ':createdAt' => date('Y-m-d H:i:s'),
            ':description' => $_POST['description'],
            ':budgetID' => $budgetID
        ]);

        // تحديث الموازنة
        $update = $conn->prepare("UPDATE Budgets SET SpentAmount = SpentAmount + ? WHERE ID = ?");
        $update->execute([$netAmount, $budgetID]);

        $_SESSION['success'] = "تمت إضافة الصرفية بنجاح";
        header("Location: Expense_Management.php");
        exit;
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger mt-3'>خطأ: " . $e->getMessage() . "</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة الصرفيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        .form-section { background: #f9f9f9; padding: 20px; border-radius: 8px; }
    </style>
</head>
<body>
<?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success text-center">
        <?= $_SESSION['success']; unset($_SESSION['success']); ?>
    </div>
<?php endif; ?>

<?php include 'header.php'; ?>

<div class="container mt-4">
    <h2 class="text-center mb-4">إضافة صرفية جديدة</h2>

    <form method="POST" id="expenseForm" class="form-section">
        <div class="row g-3">
            <div class="col-md-4">
                <label>اسم العميل</label>
                <input type="text" id="customer_name" class="form-control" autocomplete="off">
                <input type="hidden" name="customer_id" id="customer_id">
            </div>
            <div class="col-md-4">
                <label>رقم الإذن</label>
                <input type="text" name="authorization_number" id="authorization_number" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label>رقم الحساب</label>
                <input type="text" name="account_number" id="account_number" class="form-control" required>
            </div>

            <div class="col-md-4">
                <label>المبلغ</label>
                <input type="number" name="amount" id="amount" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label>الضريبة</label>
                <input type="number" name="tax" id="tax" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label>التاريخ</label>
                <input type="date" name="date" id="date" class="form-control" required>
            </div>

            <div class="col-md-4">
                <label>تاريخ الدفع</label>
                <input type="date" name="payment_date" id="payment_date" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label>رقم التحويل</label>
                <input type="text" name="transfer_number" id="transfer_number" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label>نوع التحويل</label>
                <input type="text" name="transfer_type" id="transfer_type" class="form-control" required>
            </div>

            <div class="col-md-12">
                <label>الوصف</label>
                <textarea name="description" id="description" class="form-control" rows="2" required></textarea>
            </div>
            <div class="col-md-12">
                <label>ملاحظات</label>
                <textarea name="notes" id="notes" class="form-control" rows="2"></textarea>
            </div>

            <div class="col-md-4">
                <label>الموازنة</label>
                <select name="budget_id" class="form-control" required>
                    <option disabled selected>اختر الموازنة</option>
                    <?php foreach ($budgets as $budget): ?>
                        <option value="<?= $budget['ID'] ?>">
                            <?= $budget['Title'] ?> (المتاح: <?= $budget['TotalAmount'] - $budget['SpentAmount'] ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        <button type="submit" id="submitExpense" class="btn btn-primary mt-4">إضافة صرفية</button>
    </form>

    <hr class="my-5">
    <form method="GET" class="row g-3 mb-4">
    <div class="col-md-3">
        <label>من تاريخ</label>
        <input type="date" name="from_date" value="<?= $_GET['from_date'] ?? '' ?>" class="form-control">
    </div>
    <div class="col-md-3">
        <label>إلى تاريخ</label>
        <input type="date" name="to_date" value="<?= $_GET['to_date'] ?? '' ?>" class="form-control">
    </div>
    <div class="col-md-4">
        <label>اسم العميل</label>
        <input type="text" name="customer_search" value="<?= $_GET['customer_search'] ?? '' ?>" class="form-control" placeholder="بحث باسم العميل">
    </div>
    <div class="col-md-2 d-flex align-items-end">
        <button type="submit" class="btn btn-primary w-100">بحث</button>
    </div>
</form>

    <h3 class="text-center mb-4">سجل الصرفيات</h3>
    <div class="table-responsive">
        <table class="table table-bordered table-striped text-center">
            <thead class="table-dark">
                <tr>
                    <th>العميل</th>
                    <th>المبلغ</th>
                    <th>الضريبة</th>
                    <th>الصافي</th>
                    <th>التاريخ</th>
                    <th>الموازنة</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($expenses as $exp): ?>
                    <tr>
                        <td><?= $exp['CustomerName'] ?></td>
                        <td><?= $exp['Amount'] ?></td>
                        <td><?= $exp['Tax'] ?></td>
                        <td><?= $exp['NetAmount'] ?></td>
                        <td><?= $exp['Date'] ?></td>
                        <td><?= $exp['BudgetTitle'] ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div><script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    let typingTimer;
    let doneTypingInterval = 600;

    $('#customer_name').on('input', function () {
        clearTimeout(typingTimer);
        let name = $(this).val().trim();

        if (name.length >= 2) {
            typingTimer = setTimeout(function () {
                $.ajax({
                    url: 'get_customer_info.php',
                    method: 'GET',
                    data: { name: name },
                    success: function(response) {
                        let data = JSON.parse(response);
                        if (data && data.ID) {
                            $('#customer_id').val(data.ID);
                            $('#authorization_number').val(data.AuthorizationNumber || '');
                            $('#account_number').val(data.AccountNumber || '');
                            $('#transfer_type').val(data.TransferType || '');
                            $('#description').val(data.Description || '');
                            $('#notes').val(data.Notes || '');
                        } else {
                            $('#customer_id').val('');
                            $('#authorization_number, #account_number, #transfer_type, #description, #notes').val('');
                        }
                    }
                });
            }, doneTypingInterval);
        } else {
            $('#customer_id').val('');
        }
    });
});
</script>

</body>
</html>
