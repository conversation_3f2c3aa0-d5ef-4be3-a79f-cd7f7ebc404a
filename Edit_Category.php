<?php
session_start();
require 'db.php';

if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: /e-finance/login");
    exit;
}

if (!isset($_GET['id'])) {
    header("Location: Budget_Management.php");
    exit;
}

$id = $_GET['id'];
$stmt = $conn->prepare("SELECT * FROM Categories WHERE ID = ?");
$stmt->execute([$id]);
$category = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$category) {
    echo "البند غير موجود";
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'];

    $stmt = $conn->prepare("UPDATE Categories SET Name = ? WHERE ID = ?");
    $stmt->execute([$name, $id]);

    header("Location: Budget_Management.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تعديل البند</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
</head>
<body>
<?php include 'header.php'; ?>
<div class="container mt-5">
    <h2 class="mb-4 text-center">تعديل البند</h2>
    <form method="POST" class="row g-3">
        <div class="col-md-12">
            <label class="form-label">اسم البند</label>
            <input type="text" name="name" class="form-control" value="<?= htmlspecialchars($category['Name']) ?>" required>
        </div>
        <div class="col-12 text-center">
            <button type="submit" class="btn btn-primary mt-3">تحديث</button>
            <a href="Budget_Management.php" class="btn btn-secondary mt-3">إلغاء</a>
        </div>
    </form>
</div></body>
</html>
