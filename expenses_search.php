<?php
session_start();
require 'db.php'; // الاتصال بقاعدة البيانات

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// التحقق من صلاحيات الوصول
$role = $_SESSION['role'];
if ($role !== 'admin' && $role !== 'viewer') {
    echo "<script>alert('ليس لديك صلاحية الوصول إلى هذه الصفحة.'); window.location.href = 'index.php';</script>";
    exit;
}

// معالجة إضافة صرفية جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $role === 'admin') {
    $customerID = $_POST['customer_id'];
    $authorizationNumber = $_POST['authorization_number'];
    $accountNumber = $_POST['account_number'];
    $paymentDate = $_POST['payment_date'];
    $transferNumber = $_POST['transfer_number'];
    $transferType = $_POST['transfer_type'];
    $expenseNumber = $_POST['expense_number'];
    $notes = $_POST['notes'];

    // التعامل مع رفع الملف
    $filePath = null;
    if (!empty($_FILES['file']['name'])) {
        $fileName = basename($_FILES['file']['name']);
        $targetDir = "uploads/";
        $filePath = $targetDir . $fileName;
        move_uploaded_file($_FILES['file']['tmp_name'], $filePath);
    }

    $stmt = $conn->prepare("
        INSERT INTO Expenses (CustomerID, AuthorizationNumber, AccountNumber, PaymentDate, TransferNumber, TransferType, ExpenseNumber, File, Notes)
        VALUES (:customerID, :authorizationNumber, :accountNumber, :paymentDate, :transferNumber, :transferType, :expenseNumber, :filePath, :notes)
    ");
    $stmt->execute([
        ':customerID' => $customerID,
        ':authorizationNumber' => $authorizationNumber,
        ':accountNumber' => $accountNumber,
        ':paymentDate' => $paymentDate,
        ':transferNumber' => $transferNumber,
        ':transferType' => $transferType,
        ':expenseNumber' => $expenseNumber,
        ':filePath' => $filePath,
        ':notes' => $notes
    ]);
    echo "<script>alert('تمت إضافة الصرفية بنجاح!');</script>";
}

// معالجة البحث عن الصرفيات
$searchQuery = '';
if (isset($_GET['query'])) {
    $searchQuery = $_GET['query'];
    $stmt = $conn->prepare("
        SELECT e.*, c.Name AS CustomerName 
        FROM Expenses e 
        JOIN Customers c ON e.CustomerID = c.ID 
        WHERE c.Name LIKE :search OR e.AuthorizationNumber LIKE :search 
        ORDER BY e.PaymentDate DESC
    ");
    $stmt->execute([':search' => "%$searchQuery%"]);
} else {
    $stmt = $conn->query("
        SELECT e.*, c.Name AS CustomerName 
        FROM Expenses e 
        JOIN Customers c ON e.CustomerID = c.ID 
        ORDER BY e.PaymentDate DESC
    ");
}
$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب قائمة العملاء
$customers = $conn->query("SELECT ID, Name FROM Customers")->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصرفيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
 <div class="container mt-5">
    <a href="index.php" class="btn btn-secondary mb-3">العودة إلى الرئيسية</a>
</div>
<div class="container mt-4">
    <h1 class="text-center">إدارة الصرفيات</h1>
    <form method="GET" class="mb-3">
        <div class="input-group">
            <input type="text" name="query" class="form-control" placeholder="ابحث بالاسم أو رقم الإذن" value="<?php echo htmlspecialchars($searchQuery); ?>">
            <button type="submit" class="btn btn-primary">بحث</button>
        </div>
    </form>

    <?php if ($role === 'admin'): ?>
        <form method="POST" enctype="multipart/form-data" class="mb-4">
            <h3>إضافة صرفية جديدة</h3>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="customer_id" class="form-label">العميل</label>
                    <select name="customer_id" id="customer_id" class="form-select" required>
                        <option value="">اختر العميل</option>
                        <?php foreach ($customers as $customer): ?>
                            <option value="<?php echo $customer['ID']; ?>"><?php echo htmlspecialchars($customer['Name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="authorization_number" class="form-label">رقم الإذن</label>
                    <input type="text" name="authorization_number" id="authorization_number" class="form-control" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="account_number" class="form-label">رقم الحساب</label>
                    <input type="text" name="account_number" id="account_number" class="form-control" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="payment_date" class="form-label">تاريخ الصرف</label>
                    <input type="date" name="payment_date" id="payment_date" class="form-control" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="transfer_number" class="form-label">رقم التحويل</label>
                    <input type="text" name="transfer_number" id="transfer_number" class="form-control">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="transfer_type" class="form-label">نوع التحويل</label>
                    <select name="transfer_type" id="transfer_type" class="form-select">
                        <option value="">اختر النوع</option>
                        <option value="سويفت">سويفت</option>
                        <option value="داخلي">داخلي</option>
                        <option value="خطاب">خطاب</option>
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="expense_number" class="form-label">رقم الصرف</label>
                    <input type="text" name="expense_number" id="expense_number" class="form-control" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="file" class="form-label">الملف</label>
                    <input type="file" name="file" id="file" class="form-control">
                </div>
                <div class="col-md-12 mb-3">
                    <label for="notes" class="form-label">ملاحظات</label>
                    <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                </div>
            </div>
            <button type="submit" class="btn btn-success">إضافة الصرفية</button>
        </form>
    <?php endif; ?>

    <div class="table-responsive">
        <table class="table table-bordered text-center">
            <thead class="table-dark">
                <tr>
                    <th>#</th>
                    <th>اسم العميل</th>
                    <th>رقم الإذن</th>
                    <th>رقم الحساب</th>
                    <th>تاريخ الصرف</th>
                    <th>رقم التحويل</th>
                    <th>نوع التحويل</th>
                    <th>رقم الصرف</th>
                    <th>الملف</th>
                    <th>ملاحظات</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($expenses): ?>
                    <?php foreach ($expenses as $index => $expense): ?>
                        <tr>
                            <td><?php echo $index + 1; ?></td>
                            <td><?php echo htmlspecialchars($expense['CustomerName']); ?></td>
                            <td><?php echo htmlspecialchars($expense['AuthorizationNumber']); ?></td>
                            <td><?php echo htmlspecialchars($expense['AccountNumber']); ?></td>
                            <td><?php echo htmlspecialchars($expense['PaymentDate']); ?></td>
                            <td><?php echo htmlspecialchars($expense['TransferNumber']); ?></td>
                            <td><?php echo htmlspecialchars($expense['TransferType']); ?></td>
                            <td><?php echo htmlspecialchars($expense['ExpenseNumber']); ?></td>
                            <td><?php echo $expense['File'] ? "<a href='{$expense['File']}' target='_blank'>عرض</a>" : 'لا يوجد'; ?></td>
                            <td><?php echo htmlspecialchars($expense['Notes']); ?></td>
                            <td>
                                <?php if ($role === 'admin'): ?>
                                    <a href="edit_expense.php?id=<?php echo $expense['ID']; ?>" class="btn btn-warning btn-sm">تعديل</a>
                                    <a href="delete_expense.php?id=<?php echo $expense['ID']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من الحذف؟')">حذف</a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr><td colspan="11">لا توجد بيانات.</td></tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
</body>
</html>
