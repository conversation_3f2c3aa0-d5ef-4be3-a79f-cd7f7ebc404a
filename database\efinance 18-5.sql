-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: May 18, 2025 at 07:02 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `efinance`
--

-- --------------------------------------------------------

--
-- Table structure for table `budgetitems`
--

CREATE TABLE `budgetitems` (
  `ID` int(11) NOT NULL,
  `BudgetID` int(11) DEFAULT NULL,
  `Category` varchar(255) NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `CreatedAt` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `budgets`
--

CREATE TABLE `budgets` (
  `ID` int(11) NOT NULL,
  `Title` varchar(255) NOT NULL,
  `TotalAmount` decimal(12,2) NOT NULL,
  `SpentAmount` decimal(12,2) DEFAULT 0.00,
  `Notes` text DEFAULT NULL,
  `CreatedAt` datetime DEFAULT current_timestamp(),
  `UsedAmount` decimal(10,2) NOT NULL DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `budgets`
--

INSERT INTO `budgets` (`ID`, `Title`, `TotalAmount`, `SpentAmount`, `Notes`, `CreatedAt`, `UsedAmount`) VALUES
(10, 'موازنة الشركة', 1000000.00, 23790.00, NULL, '2025-05-17 10:20:46', 3000.00),
(13, 'موازنة 2', 10000.00, 6000.00, NULL, '2025-05-17 10:36:24', 1000.00),
(14, 'موازنة 3', 500000.00, 0.00, NULL, '2025-05-17 10:55:32', 0.00);

-- --------------------------------------------------------

--
-- Table structure for table `budgetsupport`
--

CREATE TABLE `budgetsupport` (
  `ID` int(11) NOT NULL,
  `Amount` decimal(15,2) NOT NULL,
  `Date` date NOT NULL,
  `Description` text DEFAULT NULL,
  `File` varchar(255) DEFAULT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `budgetsupport`
--

INSERT INTO `budgetsupport` (`ID`, `Amount`, `Date`, `Description`, `File`, `CreatedAt`) VALUES
(10, 2000000.00, '2025-05-15', 'مبلغ الميزانية الحالية رصيد اول المدة في 2025', NULL, '2025-01-04 03:24:26'),
(14, 1000.00, '2025-05-17', '111', NULL, '2025-05-17 15:08:45');

-- --------------------------------------------------------

--
-- Table structure for table `budgettotal`
--

CREATE TABLE `budgettotal` (
  `ID` int(11) NOT NULL,
  `Total` decimal(15,2) NOT NULL DEFAULT 0.00
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `budgettotal`
--

INSERT INTO `budgettotal` (`ID`, `Total`) VALUES
(2, 0.00),
(1, 2001000.00);

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `ID` int(11) NOT NULL,
  `Name` varchar(255) DEFAULT NULL,
  `BudgetID` int(11) NOT NULL,
  `Amount` decimal(12,2) DEFAULT NULL,
  `SpentAmount` decimal(10,2) DEFAULT 0.00,
  `RemainingAmount` decimal(10,2) DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`ID`, `Name`, `BudgetID`, `Amount`, `SpentAmount`, `RemainingAmount`) VALUES
(9, 'مساعدات ', 13, 5000.00, 1000.00, 0.00),
(11, 'تشغيل', 13, 5000.00, 6000.00, 0.00),
(12, 'راتب', 10, 10000.00, 6000.00, 0.00);

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `ID` int(11) NOT NULL,
  `Name` varchar(255) NOT NULL,
  `NationalID` varchar(20) NOT NULL,
  `AccountNumber` varchar(20) NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `Tax` decimal(10,2) NOT NULL,
  `Net` decimal(10,2) GENERATED ALWAYS AS (`Amount` - `Tax`) STORED,
  `PaymentDuration` int(11) NOT NULL,
  `StartDate` date NOT NULL,
  `EndDate` date NOT NULL,
  `FilePath` varchar(255) DEFAULT NULL,
  `AuthorizationNumber` varchar(50) NOT NULL,
  `TransferNumber` varchar(50) DEFAULT NULL,
  `DocumentCount` int(11) DEFAULT NULL,
  `Notes` text DEFAULT NULL,
  `Category` enum('مساعدات','تشغيل','تشغيل شهري') NOT NULL,
  `CardExpiryDate` date DEFAULT NULL,
  `CardExpiry` varchar(255) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`ID`, `Name`, `NationalID`, `AccountNumber`, `Amount`, `Tax`, `PaymentDuration`, `StartDate`, `EndDate`, `FilePath`, `AuthorizationNumber`, `TransferNumber`, `DocumentCount`, `Notes`, `Category`, `CardExpiryDate`, `CardExpiry`) VALUES
(5, 'سيد يحيي', '*********', '123321', 2000.00, 200.00, 12, '2025-01-04', '2025-01-04', 'uploads/customers_report.pdf', '2', '2', 12, '', 'مساعدات', NULL, ''),
(23, 'ahmed sayed', '**************', '**************', 50000.00, 5000.00, 12, '0000-00-00', '0000-00-00', NULL, 'AUTH124', 'TRF457', 1, 'no notes', 'مساعدات', NULL, '12/12/2025'),
(8, 'عمر سيد', '**********', '********', 10000.00, 1000.00, 12, '2025-01-07', '2025-01-07', '', '1', '1', 1, '', 'مساعدات', NULL, ''),
(9, 'نور عاطف', '**********', '********', 10000.00, 1000.00, 12, '2025-01-07', '2025-01-07', '', '1', '1', 1, '', 'تشغيل', NULL, NULL),
(10, 'احمد يحيي سيد', '***********', '********', 900.00, 0.00, 12, '2025-01-08', '2025-01-08', '', '12', '1222', 11, '', 'تشغيل', NULL, NULL),
(11, 'ياسر رمضان ', '2917124124', '12412414', 15000.00, 0.00, 6, '2025-01-01', '2025-06-30', '', '1245', '2500', 1, '', 'تشغيل شهري', NULL, NULL),
(19, 'ahmed tarek', '*********12345', '454545', 10000.00, 0.00, 12, '2025-03-18', '2025-03-18', '', '********', '********', 10, '', 'مساعدات', NULL, NULL),
(20, 'احمد عمر سيد', '********111111', '111111', 10000.00, 0.00, 12, '2025-04-01', '2025-04-30', '', '1111', '11111', 1, '', 'مساعدات', NULL, '2025-04-01'),
(21, 'محمد سيد ', '29710040101015', '555555555555', 10000.00, 1000.00, 12, '2025-04-01', '2025-04-30', 'uploads/customers_report.pdf', '0', '0', 12, '', 'مساعدات', NULL, '2025-05-14'),
(22, 'أحمد محمد', '29811223344556', '*********01234', 10000.00, 100.00, 12, '0000-00-00', '0000-00-00', NULL, 'AUTH123', 'TRF456', 3, 'مساعدة طبية', 'مساعدات', NULL, '12/1/2025'),
(24, 'نهى سيد عبدالمقصود', '20250689101036', '1000089', 20000.00, 2000.00, 10, '2025-05-01', '2026-04-30', '', '20389', '516', 10, 'راتب', 'تشغيل شهري', NULL, '2025-12-31'),
(44, 'ahmed sayed', '29710050101015', '78787878', 5000.00, 500.00, 2025, '2025-12-01', '2025-12-01', NULL, '1223', '1222', 10, '?? ????', '', NULL, '2025-12-01'),
(42, 'doaa mohmed', '29710050101015', '78787878', 5000.00, 500.00, 2025, '2025-12-01', '2025-12-01', NULL, '1223', '1222', 10, '?? ????', '', NULL, '2025-12-01'),
(43, 'ahmed ramadan', '29710050101015', '78787878', 5000.00, 500.00, 2025, '2025-12-01', '2025-12-01', NULL, '1223', '1222', 10, '?? ????', '', NULL, '2025-12-01'),
(40, 'دعاء محمد ', '20258900000000', '10000654', 200000.00, 2000.00, 5, '0000-00-00', '0000-00-00', NULL, '33335', '231', 5, 'لا يوجد', 'مساعدات', NULL, '31/07/2025'),
(41, 'ahmed sayed', '29710050101015', '78787878', 5000.00, 500.00, 2025, '2025-12-01', '2025-12-01', NULL, '1223', '1222', 10, '?? ????', '', NULL, '2025-12-01'),
(39, 'احمد يحيي سيد', '20258900000000', '10000654', 200000.00, 2000.00, 5, '0000-00-00', '0000-00-00', NULL, '33335', '231', 5, 'لا يوجد', 'مساعدات', NULL, '31/07/2025'),
(45, 'doaa mohmed', '29710050101015', '78787878', 5000.00, 500.00, 2025, '2025-12-01', '2025-12-01', NULL, '1223', '1222', 10, '?? ????', '', NULL, '2025-12-01'),
(46, 'ahmed ramadan', '29710050101015', '78787878', 5000.00, 500.00, 2025, '2025-12-01', '2025-12-01', NULL, '1223', '1222', 10, '?? ????', '', NULL, '2025-12-01'),
(47, 'ahmed sayed', '29710050101015', '78787878', 5000.00, 500.00, 2025, '2025-12-01', '2025-12-01', NULL, '1223', '1222', 10, '?? ????', '', NULL, '2025-12-01'),
(48, 'doaa mohmed', '29710050101015', '78787878', 5000.00, 500.00, 2025, '2025-12-01', '2025-12-01', NULL, '1223', '1222', 10, '?? ????', '', NULL, '2025-12-01'),
(49, 'ahmed ramadan', '29710050101015', '78787878', 5000.00, 500.00, 2025, '2025-12-01', '2025-12-01', NULL, '1223', '1222', 10, '?? ????', '', NULL, '2025-12-01');

--
-- Triggers `customers`
--
DELIMITER $$
CREATE TRIGGER `check_NationalID` BEFORE INSERT ON `customers` FOR EACH ROW BEGIN
    IF CHAR_LENGTH(NEW.NationalID) != 14 OR NEW.NationalID REGEXP '[^0-9]' THEN
        SIGNAL SQLSTATE '45000'
        SET MESSAGE_TEXT = 'الرقم القومي يجب أن يكون 14 رقمًا فقط ويحتوي على أرقام فقط.';
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `expenditures`
--

CREATE TABLE `expenditures` (
  `ID` int(11) NOT NULL,
  `CustomerID` int(11) NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `ExpenditureDate` date NOT NULL,
  `Notes` text DEFAULT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expenses`
--

CREATE TABLE `expenses` (
  `ID` int(11) NOT NULL,
  `CustomerID` int(11) NOT NULL,
  `BudgetID` int(11) DEFAULT NULL,
  `AuthorizationNumber` varchar(50) NOT NULL,
  `AccountNumber` varchar(50) NOT NULL,
  `Amount` decimal(15,2) NOT NULL,
  `Tax` decimal(15,2) NOT NULL,
  `NetAmount` decimal(15,2) GENERATED ALWAYS AS (`Amount` - `Tax`) STORED,
  `Date` date NOT NULL,
  `PaymentDate` date DEFAULT NULL,
  `TransferNumber` varchar(50) NOT NULL,
  `TransferType` enum('Swift','Internal','Letter') NOT NULL,
  `ExpenseNumber` varchar(50) NOT NULL,
  `File` varchar(255) DEFAULT NULL,
  `Notes` text DEFAULT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT current_timestamp(),
  `Description` text DEFAULT NULL,
  `Name` varchar(255) NOT NULL,
  `category_id` int(11) DEFAULT NULL,
  `CategoryID` int(11) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `expenses`
--

INSERT INTO `expenses` (`ID`, `CustomerID`, `BudgetID`, `AuthorizationNumber`, `AccountNumber`, `Amount`, `Tax`, `Date`, `PaymentDate`, `TransferNumber`, `TransferType`, `ExpenseNumber`, `File`, `Notes`, `CreatedAt`, `Description`, `Name`, `category_id`, `CategoryID`) VALUES
(619, 0, 8, '1222', '1000089', 25000.00, 0.00, '2025-05-13', '2025-05-13', '0', '', 'EXP1747166133', NULL, '', '2025-05-13 19:55:33', '0', '', NULL, NULL),
(618, 10, NULL, '12222', '12222', 8000.00, 800.00, '2025-05-13', '2025-05-13', '1212', '', 'EXP1747166047', NULL, '', '2025-05-13 19:54:07', '1212', '', NULL, NULL),
(617, 0, 7, '211', '********', 1000.00, 100.00, '2025-05-12', '2025-05-12', '*********', '', 'EXP1747014171', NULL, '', '2025-05-12 01:42:51', '3', '', NULL, NULL),
(616, 0, 7, '2', '2', 2000.00, 0.00, '2025-04-21', '2025-04-21', '2', 'Internal', 'EXP1745246375', NULL, '', '2025-04-21 14:39:35', '2', '', NULL, NULL),
(620, 10, 13, '12', '********', 1000.00, 0.00, '2025-05-17', '2025-05-17', '1', 'Swift', '1', 'uploads/6828b5951e1e9_customers_report.pdf', '11', '2025-05-17 16:13:09', '1', '', 9, NULL),
(621, 10, 13, '12', '********', 5000.00, 0.00, '2025-05-17', '2025-05-17', '124', '', '45', '', '45', '2025-05-17 16:40:14', '45', '', 11, NULL),
(622, 23, 10, '', '', 1000.00, 0.00, '0000-00-00', NULL, '', 'Swift', '', NULL, NULL, '2025-05-17 16:47:02', NULL, '', NULL, NULL),
(623, 40, 13, '', '', 1000.00, 0.00, '0000-00-00', NULL, '', 'Swift', '', NULL, NULL, '2025-05-17 16:47:49', NULL, '', NULL, 11),
(624, 40, 10, '33335', '10000654', 1000.00, 0.00, '2025-05-17', '2025-05-17', '11111', '', '1111', '', 'لا يوجد', '2025-05-17 16:56:58', '111', '', 12, NULL),
(625, 40, 10, '33335', '10000654', 1000.00, 0.00, '2025-05-17', '2025-05-17', '11111', '', '111111', '', 'لا يوجد', '2025-05-17 17:06:23', '1111', '', 12, NULL),
(626, 10, 10, '12', '********', 2000.00, 0.00, '2025-05-17', '2025-05-17', '1111', '', '1111111', '', '11111', '2025-05-17 17:17:31', '111111', '', 12, NULL),
(627, 10, 10, '', '', 2000.00, 0.00, '0000-00-00', NULL, '', 'Swift', '', NULL, NULL, '2025-05-17 17:18:39', NULL, '', NULL, 12),
(628, 24, 13, '20389', '1000089', 20000.00, 2000.00, '2025-03-05', '2025-07-05', '506', '', '325897', '', '', '2025-05-17 17:47:04', 'مساعدات', '', 9, NULL),
(629, 10, 10, '12', '********', 900.00, 10.00, '2025-05-17', '2025-05-17', '11', '', 'EXP1747505819', NULL, '122', '2025-05-17 18:16:59', '122', '', NULL, NULL),
(630, 10, 10, '12', '********', 900.00, 0.00, '2025-05-17', '2025-05-17', '120', '', 'EXP1747506264', NULL, 'لايوجد', '2025-05-17 18:24:24', '120', '', NULL, NULL),
(631, 40, 10, '33335', '10000654', 20000.00, 2000.00, '2025-10-10', '2025-10-15', '397', '', 'EXP1747507012', NULL, 'لا يوجد', '2025-05-17 18:36:52', 'تشغيل ', '', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `logs`
--

CREATE TABLE `logs` (
  `ID` int(11) NOT NULL,
  `UserID` int(11) NOT NULL,
  `Action` varchar(255) NOT NULL,
  `Timestamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `ID` int(11) NOT NULL,
  `Title` varchar(255) NOT NULL,
  `Message` text DEFAULT NULL,
  `NotifyDate` date NOT NULL,
  `ToDate` date DEFAULT NULL,
  `EndDate` date DEFAULT NULL,
  `CreatedAt` datetime DEFAULT current_timestamp(),
  `Status` varchar(50) DEFAULT 'لم تنفذ'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`ID`, `Title`, `Message`, `NotifyDate`, `ToDate`, `EndDate`, `CreatedAt`, `Status`) VALUES
(19, 'تنبيه 1', 'مثال لتنبيه 1', '2025-05-17', '2025-05-15', NULL, '2025-05-15 20:12:40', 'تحت التنفيذ'),
(20, 'تنبيه 2', 'مثال لتنبيه 2', '2025-05-16', '2025-05-31', NULL, '2025-05-16 17:41:10', 'تحت التنفيذ');

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `ID` int(11) NOT NULL,
  `Type` enum('add','deduct') NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `Description` text DEFAULT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `useractivity`
--

CREATE TABLE `useractivity` (
  `ID` int(11) NOT NULL,
  `UserID` int(11) NOT NULL,
  `Page` varchar(255) DEFAULT NULL,
  `Action` varchar(100) DEFAULT NULL,
  `URL` text DEFAULT NULL,
  `Method` varchar(10) DEFAULT NULL,
  `Data` text DEFAULT NULL,
  `Duration` int(11) DEFAULT NULL,
  `Timestamp` datetime DEFAULT current_timestamp(),
  `StartTime` datetime DEFAULT NULL,
  `EndTime` datetime DEFAULT NULL,
  `Details` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `useractivity`
--

INSERT INTO `useractivity` (`ID`, `UserID`, `Page`, `Action`, `URL`, `Method`, `Data`, `Duration`, `Timestamp`, `StartTime`, `EndTime`, `Details`) VALUES
(58, 8, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-16 20:15:51', NULL, NULL, NULL),
(59, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-16 20:15:52', NULL, NULL, NULL),
(60, 8, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-16 20:16:21', NULL, NULL, NULL),
(61, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-16 20:17:14', NULL, NULL, NULL),
(62, 5, 'Add_Notification2.php', 'زيارة الصفحة', '/e-finance/Add_Notification2', 'GET', '[]', NULL, '2025-05-16 20:21:04', NULL, NULL, NULL),
(63, 5, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'GET', '[]', NULL, '2025-05-16 20:26:02', NULL, NULL, NULL),
(64, 5, 'Add_Notification2.php', 'زيارة الصفحة', '/e-finance/Add_Notification2', 'GET', '[]', NULL, '2025-05-16 20:30:07', NULL, NULL, NULL),
(65, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-16 20:33:51', NULL, NULL, NULL),
(66, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-17 09:34:08', NULL, NULL, NULL),
(67, 5, 'Add_Notification2.php', 'زيارة الصفحة', '/e-finance/Add_Notification2', 'GET', '[]', NULL, '2025-05-17 09:34:25', NULL, NULL, NULL),
(68, 5, 'Add_Notification2.php', 'زيارة الصفحة', '/e-finance/Add_Notification2', 'GET', '[]', NULL, '2025-05-17 10:08:01', NULL, NULL, NULL),
(69, 5, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'GET', '[]', NULL, '2025-05-17 10:08:04', NULL, NULL, NULL),
(70, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-17 12:56:45', NULL, NULL, NULL),
(71, 5, 'Add_Notification2.php', 'زيارة الصفحة', '/e-finance/Add_Notification2', 'GET', '[]', NULL, '2025-05-17 12:59:29', NULL, NULL, NULL),
(72, 5, 'Add_Notification2.php', 'زيارة الصفحة', '/e-finance/Add_Notification2?month=4&year=2025', 'GET', '{\"month\":\"4\",\"year\":\"2025\"}', NULL, '2025-05-17 12:59:32', NULL, NULL, NULL),
(73, 5, 'Add_Notification2.php', 'زيارة الصفحة', '/e-finance/Add_Notification2?month=5&year=2025', 'GET', '{\"month\":\"5\",\"year\":\"2025\"}', NULL, '2025-05-17 12:59:34', NULL, NULL, NULL),
(74, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index', 'GET', '[]', NULL, '2025-05-17 13:00:48', NULL, NULL, NULL),
(75, 5, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'GET', '[]', NULL, '2025-05-17 13:01:03', NULL, NULL, NULL),
(76, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-17 13:37:03', NULL, NULL, NULL),
(77, 7, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-17 13:53:21', NULL, NULL, NULL),
(78, 7, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'GET', '[]', NULL, '2025-05-17 13:53:51', NULL, NULL, NULL),
(79, 7, 'Add_Notification2.php', 'زيارة الصفحة', '/e-finance/Add_Notification2', 'GET', '[]', NULL, '2025-05-17 13:54:01', NULL, NULL, NULL),
(80, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-17 18:08:46', NULL, NULL, NULL),
(81, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-17 18:08:53', NULL, NULL, NULL),
(82, 7, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-17 19:42:28', NULL, NULL, NULL),
(83, 7, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'GET', '[]', NULL, '2025-05-17 19:42:39', NULL, NULL, NULL),
(84, 7, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'POST', '{\"upload_excel\":\"\"}', NULL, '2025-05-17 19:42:51', NULL, NULL, NULL),
(85, 7, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'GET', '[]', NULL, '2025-05-17 19:42:55', NULL, NULL, NULL),
(86, 7, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'POST', '{\"upload_excel\":\"\"}', NULL, '2025-05-17 19:43:36', NULL, NULL, NULL),
(87, 7, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'GET', '[]', NULL, '2025-05-17 19:43:39', NULL, NULL, NULL),
(88, 7, 'index.php', 'زيارة الصفحة', '/e-finance/index', 'GET', '[]', NULL, '2025-05-17 19:44:06', NULL, NULL, NULL),
(89, 7, 'index.php', 'زيارة الصفحة', '/e-finance/index', 'GET', '[]', NULL, '2025-05-17 19:44:25', NULL, NULL, NULL),
(90, 7, 'index.php', 'زيارة الصفحة', '/e-finance/index', 'GET', '[]', NULL, '2025-05-17 19:44:29', NULL, NULL, NULL),
(91, 7, 'Add_Notification2.php', 'زيارة الصفحة', '/e-finance/Add_Notification2', 'GET', '[]', NULL, '2025-05-17 19:44:46', NULL, NULL, NULL),
(92, 7, 'Add_Notification2.php', 'زيارة الصفحة', '/e-finance/Add_Notification2', 'POST', '{\"id\":\"20\",\"status\":\"\\u062a\\u062d\\u062a \\u0627\\u0644\\u062a\\u0646\\u0641\\u064a\\u0630\",\"update_status\":\"\"}', NULL, '2025-05-17 19:45:12', NULL, NULL, NULL),
(93, 7, 'Add_Notification2.php', 'زيارة الصفحة', '/e-finance/Add_Notification2.php?month=5&year=2025', 'GET', '{\"month\":\"5\",\"year\":\"2025\"}', NULL, '2025-05-17 19:45:12', NULL, NULL, NULL),
(94, 7, 'Add_Notification2.php', 'زيارة الصفحة', '/e-finance/Add_Notification2.php', 'GET', '[]', NULL, '2025-05-17 19:45:49', NULL, NULL, NULL),
(95, 7, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'GET', '[]', NULL, '2025-05-17 19:46:05', NULL, NULL, NULL),
(96, 7, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-17 19:49:39', NULL, NULL, NULL),
(97, 5, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'GET', '[]', NULL, '2025-05-17 20:22:04', NULL, NULL, NULL),
(98, 7, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'GET', '[]', NULL, '2025-05-17 20:22:58', NULL, NULL, NULL),
(99, 7, 'customer_upload.php', 'زيارة الصفحة', '/e-finance/customer_upload', 'GET', '[]', NULL, '2025-05-17 20:29:15', NULL, NULL, NULL),
(100, 7, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-17 21:00:56', NULL, NULL, NULL),
(101, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-17 21:23:14', NULL, NULL, NULL),
(102, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index.php?date=2025-05-18', 'GET', '{\"date\":\"2025-05-18\"}', NULL, '2025-05-17 21:23:22', NULL, NULL, NULL),
(103, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index.php?date=2025-05-17', 'GET', '{\"date\":\"2025-05-17\"}', NULL, '2025-05-17 21:23:24', NULL, NULL, NULL),
(104, 7, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-17 21:30:23', NULL, NULL, NULL),
(105, 5, 'index.php', 'زيارة الصفحة', '/e-finance/index.php', 'GET', '[]', NULL, '2025-05-17 21:35:45', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `userroles`
--

CREATE TABLE `userroles` (
  `ID` int(11) NOT NULL,
  `UserID` int(11) NOT NULL,
  `Role` enum('admin','viewer','editor') NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `ID` int(11) NOT NULL,
  `Username` varchar(100) NOT NULL,
  `Password` varchar(255) NOT NULL,
  `Role` enum('admin','editor','viewer') NOT NULL DEFAULT 'viewer',
  `ProfileImage` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`ID`, `Username`, `Password`, `Role`, `ProfileImage`) VALUES
(4, 'admin', '$2y$10$fvLX/AK33h5aQbD7.jM5MuDw6MooxANQjZRXJacLmgt1XMqCkMHZK', 'admin', NULL),
(5, 'ahmed', '$2y$10$nyLL4xu9LbfniBT4n4TjzOpIbxhwZO4rKnIM6rtNxEZ1VaxpgKRPm', 'admin', '1747401508_profile2.png'),
(7, 'doaa', '$2y$10$LIjOtvJJvMrcKVpIvWKsm.8P3VRMNVAkjXIGXrBEx.l/Jc01dGQzi', 'admin', '1747401558_profile1.png'),
(8, 'محمد سمير', '$2y$10$h40LZr18PkPBmgu8tnjEpur1U82kdTiZqToayoBySD6tMljoF/D4y', 'editor', '1747411927_1747401508_profile2.png');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `budgets`
--
ALTER TABLE `budgets`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `budgetsupport`
--
ALTER TABLE `budgetsupport`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `budgettotal`
--
ALTER TABLE `budgettotal`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `expenditures`
--
ALTER TABLE `expenditures`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `CustomerID` (`CustomerID`);

--
-- Indexes for table `expenses`
--
ALTER TABLE `expenses`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `CustomerID` (`CustomerID`);

--
-- Indexes for table `logs`
--
ALTER TABLE `logs`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `UserID` (`UserID`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `useractivity`
--
ALTER TABLE `useractivity`
  ADD PRIMARY KEY (`ID`);

--
-- Indexes for table `userroles`
--
ALTER TABLE `userroles`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `UserID` (`UserID`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`ID`),
  ADD UNIQUE KEY `Username` (`Username`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `budgets`
--
ALTER TABLE `budgets`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `budgetsupport`
--
ALTER TABLE `budgetsupport`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `budgettotal`
--
ALTER TABLE `budgettotal`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=50;

--
-- AUTO_INCREMENT for table `expenditures`
--
ALTER TABLE `expenditures`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `expenses`
--
ALTER TABLE `expenses`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=632;

--
-- AUTO_INCREMENT for table `logs`
--
ALTER TABLE `logs`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `useractivity`
--
ALTER TABLE `useractivity`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=106;

--
-- AUTO_INCREMENT for table `userroles`
--
ALTER TABLE `userroles`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
