<?php
require 'db.php'; // ملف الاتصال بقاعدة البيانات
require_once 'functions.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


if (isset($_SESSION['user_id'])) {
    $_SESSION['page_start_time'] = time();

    $userId = $_SESSION['user_id'];
    $page = basename($_SERVER['PHP_SELF']);
    $action = 'زيارة الصفحة';
    $url = $_SERVER['REQUEST_URI'];
    $method = $_SERVER['REQUEST_METHOD'];
    $data = ($method === 'POST') ? json_encode($_POST) : json_encode($_GET);

    // سجل الزيارة بدون مدة الآن
    logActivity($conn, $userId, $page, $action, $url, $method, $data, null);
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

// معالجة رفع عميل واحد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['name'])) {
    $name = $_POST['name'];
    $national_id = $_POST['national_id'];
    $account_number = $_POST['account_number'];
    $amount = $_POST['amount'];
    $tax = $_POST['tax'];
    $net = $_POST['net'];
    $duration = $_POST['duration'];
    $start_date = $_POST['start_date'];
    $end_date = $_POST['end_date'];
    $auth_number = $_POST['auth_number'];
    $transfer_number = $_POST['transfer_number'];
    $document_count = $_POST['document_count'];
    $notes = $_POST['notes'];
    $category = $_POST['category'];
    $card_expiry = $_POST['card_expiry'];
    $file_path = '';

    // رفع الملف إذا تم اختياره
    if (!empty($_FILES['file']['name'])) {
        $target_dir = "uploads/";
        $file_path = $target_dir . basename($_FILES["file"]["name"]);
        move_uploaded_file($_FILES["file"]["tmp_name"], $file_path);
    }

    $stmt = $conn->prepare("INSERT INTO Customers 
    (Name, NationalID, AccountNumber, Amount, Tax, Net, PaymentDuration, StartDate, EndDate, FilePath, AuthorizationNumber, TransferNumber, DocumentCount, Notes, Category, CardExpiry) 
    VALUES 
    (:name, :national_id, :account_number, :amount, :tax, :net, :duration, :start_date, :end_date, :file_path, :auth_number, :transfer_number, :document_count, :notes, :category, :card_expiry)");

    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':national_id', $national_id);
    $stmt->bindParam(':account_number', $account_number);
    $stmt->bindParam(':amount', $amount);
    $stmt->bindParam(':tax', $tax);
    $stmt->bindParam(':net', $net);
    $stmt->bindParam(':duration', $duration);
    $stmt->bindParam(':start_date', $start_date);
    $stmt->bindParam(':end_date', $end_date);
    $stmt->bindParam(':file_path', $file_path);
    $stmt->bindParam(':auth_number', $auth_number);
    $stmt->bindParam(':transfer_number', $transfer_number);
    $stmt->bindParam(':document_count', $document_count);
    $stmt->bindParam(':notes', $notes);
    $stmt->bindParam(':category', $category);
    $stmt->bindParam(':card_expiry', $card_expiry);

    if ($stmt->execute()) {
        echo "<script>alert('تم تسجيل العميل بنجاح!');</script>";
    } else {
        echo "<script>alert('خطأ أثناء التسجيل.');</script>";
    }
}

// معالجة رفع ملف CSV
if (isset($_POST['upload_excel']) && isset($_FILES['excel_file'])) {
    $file_tmp = $_FILES['excel_file']['tmp_name'];
    $file_ext = pathinfo($_FILES['excel_file']['name'], PATHINFO_EXTENSION);

    if ($file_ext === 'csv') {
        $handle = fopen($file_tmp, "r");
        $row = 0;

        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
            $row++;
            if ($row == 1) continue; // تجاهل العنوان

            list($name, $national_id, $account_number, $amount, $tax, $net, $duration, $start_date, $end_date, $auth_number, $transfer_number, $document_count, $notes, $category, $card_expiry) = $data;

            $stmt = $conn->prepare("INSERT INTO Customers 
            (Name, NationalID, AccountNumber, Amount, Tax, Net, PaymentDuration, StartDate, EndDate, AuthorizationNumber, TransferNumber, DocumentCount, Notes, Category, CardExpiry) 
            VALUES 
            (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

            $stmt->execute([
                $name,
                $national_id,
                $account_number,
                $amount,
                $tax,
                $net,
                $duration,
                $start_date,
                $end_date,
                $auth_number,
                $transfer_number,
                $document_count,
                $notes,
                $category,
                $card_expiry
            ]);
        }

        fclose($handle);
        echo "<script>alert('✅ تم رفع بيانات العملاء من الملف بنجاح!');</script>";
    } else {
        echo "<script>alert('❌ صيغة الملف غير مدعومة. يجب أن يكون الملف بصيغة CSV فقط.');</script>";
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تسجيل العملاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .registration-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e3f2fd 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .registration-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            animation: slideInUp 0.6s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .registration-header {
            background: linear-gradient(135deg, #000000, #333333);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .registration-header h2 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
            position: relative;
            z-index: 1;
        }

        .form-section {
            padding: 30px;
        }

        .section-title {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 50px;
            height: 2px;
            background: linear-gradient(135deg, #007bff, #0056b3);
        }

        .submit-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            border: none;
            border-radius: 12px;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 123, 255, 0.4);
        }
    </style>
</head>
<body>
<?php include 'header.php'; ?>
<div class="registration-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="registration-card">
                    <div class="registration-header">
                        <h2><i class="fas fa-upload me-3"></i>رفع بيانات العملاء</h2>
                        <p class="subtitle">رفع ملف Excel يحتوي على بيانات العملاء</p>
                    </div>

                    <div class="form-section">
                        <form method="POST" enctype="multipart/form-data">
        <div class="row g-3">
            <div class="col-md-4">
                <label for="name" class="form-label">الاسم</label>
                <input type="text" name="name" id="name" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="national_id" class="form-label">الرقم القومي</label>
                <input type="text" name="national_id" id="national_id" pattern="\d{14}" title="الرقم القومي يجب أن يحتوي على 14 رقم فقط" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="account_number" class="form-label">رقم الحساب</label>
                <input type="text" name="account_number" id="account_number" class="form-control" required>
            </div>
        </div>
        <div class="row g-3 mt-3">
            <div class="col-md-4">
                <label for="amount" class="form-label">المبلغ</label>
                <input type="number" name="amount" id="amount" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="tax_rate" class="form-label">نسبة الضريبة</label>
                <select name="tax_rate" id="tax_rate" class="form-select" required>
                    <option value="">اختر النسبة</option>
                    <option value="0">0</option>
                    <option value="1">1%</option>
                    <option value="3">3%</option>
                    <option value="5">5%</option>
                    <option value="10">10%</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="tax" class="form-label">قيمة الضريبة</label>
                <input type="number" name="tax" id="tax" class="form-control" readonly>
            </div>
            <div class="col-md-4">
                <label for="net" class="form-label">الصافي</label>
                <input type="number" name="net" id="net" class="form-control" readonly>
            </div>
            <div class="col-md-4">
                <label for="duration" class="form-label">مدة الصرف</label>
                <input type="number" name="duration" id="duration" class="form-control" required>
            </div>
        </div>
        <div class="row g-3 mt-3">
            <div class="col-md-4">
                <label for="start_date" class="form-label">تاريخ البداية</label>
                <input type="date" name="start_date" id="start_date" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">تاريخ النهاية</label>
                <input type="date" name="end_date" id="end_date" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="card_expiry" class="form-label">تاريخ انتهاء كارت الميزة</label>
                <input type="date" name="card_expiry" id="card_expiry" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="file" class="form-label">الملف</label>
                <input type="file" name="file" id="file" class="form-control">
            </div>
        </div>
        <div class="row g-3 mt-3">
            <div class="col-md-4">
                <label for="auth_number" class="form-label">رقم الإذن</label>
                <input type="text" name="auth_number" id="auth_number" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="transfer_number" class="form-label">رقم التحويل</label>
                <input type="text" name="transfer_number" id="transfer_number" class="form-control" required>
            </div>
            <div class="col-md-4">
                <label for="document_count" class="form-label">عدد المستندات</label>
                <input type="number" name="document_count" id="document_count" class="form-control" required>
            </div>
        </div>
        <div class="mt-3">
            <label for="category" class="form-label">البند</label>
            <select name="category" id="category" class="form-select" required>
                <option value="">اختر البند</option>
                <option value="مساعدات">مساعدات</option>
                <option value="تشغيل">تشغيل</option>
                <option value="تشغيل شهري">تشغيل شهري</option>
            </select>
        </div>
        <div class="mt-3">
            <label for="notes" class="form-label">ملاحظات</label>
            <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
        </div>
        <button type="submit" class="btn btn-primary mt-3">تسجيل العميل</button>
    </form>

    <hr>
    <h3 class="mt-5">رفع بيانات العملاء من ملف CSV</h3>
    <form method="POST" enctype="multipart/form-data">
        <div class="mb-3">
            <label for="excel_file" class="form-label">اختر ملف CSV</label>
            <input type="file" name="excel_file" id="excel_file" class="form-control" accept=".csv" required>
        </div>
        <button type="submit" name="upload_excel" class="btn btn-success">رفع الملف ومعالجة البيانات</button>
    </form>
</div>

<?php include 'footer.php'; ?>

<script>
    const amountInput = document.getElementById('amount');
    const taxRateSelect = document.getElementById('tax_rate');
    const taxInput = document.getElementById('tax');
    const netInput = document.getElementById('net');

    function calculateTax() {
        const amount = parseFloat(amountInput.value) || 0;
        const rate = parseFloat(taxRateSelect.value) || 0;
        const tax = (amount * rate) / 100;
        const net = amount - tax;
        taxInput.value = tax.toFixed(2);
        netInput.value = net.toFixed(2);
    }

    amountInput.addEventListener('input', calculateTax);
    taxRateSelect.addEventListener('change', calculateTax);

    document.getElementById('card_expiry').addEventListener('change', function () {
        const expiryDate = new Date(this.value);
        const today = new Date();
        const oneMonthAhead = new Date();
        oneMonthAhead.setMonth(oneMonthAhead.getMonth() + 1);

        if (expiryDate < oneMonthAhead && expiryDate > today) {
            alert("⚠️ تنبيه: تاريخ انتهاء كارت الميزة أقل من شهر!");
        } else if (expiryDate <= today) {
            alert("❌ كارت الميزة منتهي بالفعل!");
        }
    });
</script>
</body>
</html>
