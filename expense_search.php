<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /login");
    exit;
}

// عرض الأخطاء أثناء التطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

// جلب العملاء من قاعدة البيانات لاستخدامها في البحث
$customersStmt = $conn->query("SELECT ID, Name FROM Customers");
$customers = $customersStmt->fetchAll(PDO::FETCH_ASSOC);

// التحقق من مدخلات البحث
$customerID = isset($_GET['customer_id']) ? $_GET['customer_id'] : null;
$expenseNumber = isset($_GET['expense_number']) ? $_GET['expense_number'] : null;

$query = "SELECT Expenses.*, Customers.Name AS CustomerName 
          FROM Expenses 
          JOIN Customers ON Expenses.CustomerID = Customers.ID 
          WHERE 1=1";

if (!empty($customerID)) {
    $query .= " AND Expenses.CustomerID = :customerID";
}
if (!empty($expenseNumber)) {
    $query .= " AND Expenses.ExpenseNumber LIKE :expenseNumber";
}

$stmt = $conn->prepare($query);

if (!empty($customerID)) {
    $stmt->bindParam(':customerID', $customerID, PDO::PARAM_INT);
}
if (!empty($expenseNumber)) {
    $searchExpenseNumber = "%$expenseNumber%";
    $stmt->bindParam(':expenseNumber', $searchExpenseNumber, PDO::PARAM_STR);
}

$stmt->execute();
$expenses = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بحث عن صرفية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
<?php include 'header.php'; ?>

<div class="main-content" id="mainContent">
    <h2 class="text-center">بحث عن صرفية</h2>

    <form method="GET" class="row g-3">
        <div class="col-md-6">
            <label for="customer_name" class="form-label">اسم العميل</label>
            <input type="text" id="customer_name" class="form-control" placeholder="أدخل اسم العميل">
            <input type="hidden" name="customer_id" id="customer_id">
            <ul id="customer_list" class="list-group position-absolute w-50"></ul>
        </div>

        <div class="col-md-6">
            <label for="expense_number" class="form-label">رقم الصرفية</label>
            <input type="text" name="expense_number" id="expense_number" class="form-control">
        </div>

        <div class="col-md-12 text-center">
            <button type="submit" class="btn btn-primary">بحث</button>
        </div>
    </form>

    <table class="table table-bordered table-hover mt-4">
        <thead>
            <tr>
                <th>رقم الصرفية</th>
                <th>اسم العميل</th>
                <th>المبلغ</th>
                <th>الضريبة</th>
                <th>الصافي</th>
                <th>التاريخ</th>
                <th>تاريخ الدفع</th>
                <th>رقم التحويل</th>
                <th>الوصف</th>
                <th>ملاحظات</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <?php if (!empty($expenses)): ?>
                <?php foreach ($expenses as $expense): ?>
                    <tr>
                        <td><?= htmlspecialchars($expense['ExpenseNumber']) ?></td>
                        <td><?= htmlspecialchars($expense['CustomerName']) ?></td>
                        <td><?= htmlspecialchars($expense['Amount']) ?></td>
                        <td><?= htmlspecialchars($expense['Tax']); ?></td>
                        <td><?= htmlspecialchars($expense['NetAmount']); ?></td>
                        <td><?= htmlspecialchars($expense['Date']) ?></td>
                        <td><?= htmlspecialchars($expense['PaymentDate']); ?></td>
                        <td><?= htmlspecialchars($expense['TransferNumber']) ?></td>
                        <td><?= htmlspecialchars($expense['Description']) ?></td>
                        <td><?= htmlspecialchars($expense['Notes']); ?></td>
                        <td>
                            <!--<a href="/e-finance/expense_view.php?id=<?= $expense['ID'] ?>" class="btn btn-info btn-sm">عرض</a>-->
                            <a href="/e-finance/edit_expense.php?id=<?= $expense['ID'] ?>" class="btn btn-warning btn-sm">تعديل</a>
                            <form method="POST" action="delete_expense.php" onsubmit="return confirm('هل أنت متأكد من حذف هذه الصرفية؟');">
                                <input type="hidden" name="delete_id" value="<?= $expense['ID']; ?>">
                                <button type="submit" class="btn btn-danger">حذف</button>
                            </form>

                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr><td colspan="7" class="text-center">لا توجد نتائج</td></tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>
<script>
$(document).ready(function() {
    $("#customer_name").on("input", function() {
        let searchText = $(this).val();
        if (searchText.length >= 2) {
            $.ajax({
                url: "customer_search.php",
                method: "GET",
                data: {query: searchText},
                success: function(data) {
                    $("#customer_list").html(data).fadeIn();
                }
            });
        } else {
            $("#customer_list").fadeOut();
        }
    });

    $(document).on("click", ".customer-item", function() {
        $("#customer_name").val($(this).text());
        $("#customer_id").val($(this).data("id"));
        $("#customer_list").fadeOut();
    });

    $(document).click(function(e) {
        if (!$(e.target).closest("#customer_name, #customer_list").length) {
            $("#customer_list").fadeOut();
        }
    });
});
</script></body>
</html>
