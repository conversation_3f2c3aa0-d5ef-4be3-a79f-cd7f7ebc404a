<?php
/**
 * حذف ملفات الفوتر من النظام
 */

$footer_files = [
    'footer.php',
    'footer-styles.css'
];

$deleted_files = [];
$failed_files = [];

foreach ($footer_files as $file) {
    $file_path = __DIR__ . '/' . $file;
    
    if (file_exists($file_path)) {
        if (unlink($file_path)) {
            $deleted_files[] = $file;
        } else {
            $failed_files[] = $file;
        }
    }
}

// إرجاع النتيجة
header('Content-Type: application/json');
echo json_encode([
    'success' => count($failed_files) === 0,
    'deleted' => $deleted_files,
    'failed' => $failed_files,
    'message' => count($failed_files) === 0 ? 'تم حذف جميع ملفات الفوتر بنجاح' : 'فشل في حذف بعض الملفات'
]);
?>
