<?php
require_once 'db.php';
require_once 'functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';

// إضافة تنبيه
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_notification']) && $isAdmin) {
    $title = $_POST['title'];
    $message = $_POST['message'];
    $notifyDate = $_POST['notify_date'];
    $toDate = $_POST['end_date'] ?? $_POST['notify_date'];
    
    $stmt = $conn->prepare("INSERT INTO Notifications (Title, Message, NotifyDate, ToDate, Status) VALUES (?, ?, ?, ?, 'لم تنفذ')");
    $stmt->execute([$title, $message, $notifyDate, $toDate]);
    
    $_SESSION['success'] = "تمت إضافة المهمة بنجاح.";
    header("Location: Add_Notification_Simple.php");
    exit;
}

// تحديث حالة المهمة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status']) && $isAdmin) {
    $id = $_POST['notification_id'];
    $status = $_POST['status'];
    $stmt = $conn->prepare("UPDATE Notifications SET Status = ? WHERE ID = ?");
    $stmt->execute([$status, $id]);
    $_SESSION['success'] = "تم تحديث حالة المهمة.";
    header("Location: Add_Notification_Simple.php");
    exit;
}

// حذف المهمة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_notification']) && $isAdmin) {
    $id = $_POST['notification_id'];
    $stmt = $conn->prepare("DELETE FROM Notifications WHERE ID = ?");
    $stmt->execute([$id]);
    $_SESSION['success'] = "تم حذف المهمة.";
    header("Location: Add_Notification_Simple.php");
    exit;
}

// متغيرات البحث والفلترة
$currentMonth = $_GET['month'] ?? date('m');
$currentYear = $_GET['year'] ?? date('Y');
$statusFilter = $_GET['status'] ?? '';
$searchQuery = $_GET['search'] ?? '';
$dateFromFilter = $_GET['date_from'] ?? '';
$dateToFilter = $_GET['date_to'] ?? '';

$startDate = "$currentYear-" . str_pad($currentMonth, 2, '0', STR_PAD_LEFT) . "-01";
$endDate = date("Y-m-t", strtotime($startDate));

// جلب التنبيهات
$query = "SELECT * FROM Notifications WHERE (NotifyDate BETWEEN ? AND ? OR ToDate BETWEEN ? AND ?)";
$params = [$startDate, $endDate, $startDate, $endDate];

if ($searchQuery !== '') {
    $query .= " AND (Title LIKE ? OR Message LIKE ?)";
    $params[] = "%$searchQuery%";
    $params[] = "%$searchQuery%";
}

if ($statusFilter !== '') {
    $query .= " AND Status = ?";
    $params[] = $statusFilter;
}

if ($dateFromFilter !== '') {
    $query .= " AND NotifyDate >= ?";
    $params[] = $dateFromFilter;
}

if ($dateToFilter !== '') {
    $query .= " AND ToDate <= ?";
    $params[] = $dateToFilter;
}

$query .= " ORDER BY NotifyDate ASC";
$stmt = $conn->prepare($query);
$stmt->execute($params);
$notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

// حساب إحصائيات بسيطة
$stats = ['total' => 0, 'completed' => 0, 'in_progress' => 0, 'pending' => 0];

try {
    $statsStmt = $conn->prepare("SELECT Status, COUNT(*) as count FROM Notifications GROUP BY Status");
    $statsStmt->execute();
    $statusCounts = $statsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($statusCounts as $row) {
        $stats['total'] += $row['count'];
        switch ($row['Status']) {
            case 'تم': $stats['completed'] = $row['count']; break;
            case 'تحت التنفيذ': $stats['in_progress'] = $row['count']; break;
            case 'لم تنفذ': $stats['pending'] = $row['count']; break;
        }
    }
} catch (PDOException $e) {
    // تجاهل الأخطاء
}

$prevMonth = $currentMonth - 1; $prevYear = $currentYear;
$nextMonth = $currentMonth + 1; $nextYear = $currentYear;
if ($prevMonth < 1) { $prevMonth = 12; $prevYear--; }
if ($nextMonth > 12) { $nextMonth = 1; $nextYear++; }
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المهام - نسخة مبسطة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .main-container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .page-header { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stats-row { margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .search-section { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .table-section { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-badge { padding: 5px 10px; border-radius: 15px; font-size: 0.85rem; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-in-progress { background: #cce5ff; color: #004085; }
        .status-pending { background: #fff3cd; color: #856404; }
        .btn-add { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; }
        .btn-add:hover { background: #0056b3; color: white; }
    </style>
</head>
<body>

<?php include 'header.php'; ?>

<div class="main-container">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <h2><i class="fas fa-tasks"></i> إدارة المهام - نسخة مبسطة</h2>
        <p class="text-muted">إدارة وتتبع المهام بطريقة بسيطة وسريعة</p>
    </div>

    <!-- الإحصائيات -->
    <div class="stats-row row">
        <div class="col-md-3">
            <div class="stat-card">
                <h4 class="text-success"><?= $stats['completed'] ?></h4>
                <p>مهام مكتملة</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <h4 class="text-info"><?= $stats['in_progress'] ?></h4>
                <p>قيد التنفيذ</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <h4 class="text-warning"><?= $stats['pending'] ?></h4>
                <p>في الانتظار</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <h4 class="text-primary"><?= $stats['total'] ?></h4>
                <p>إجمالي المهام</p>
            </div>
        </div>
    </div>

    <!-- رسائل النجاح -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success"><?= $_SESSION['success']; unset($_SESSION['success']); ?></div>
    <?php endif; ?>

    <!-- إضافة مهمة جديدة -->
    <?php if ($isAdmin): ?>
    <div class="search-section">
        <h5><i class="fas fa-plus"></i> إضافة مهمة جديدة</h5>
        <form method="POST" class="row g-3">
            <input type="hidden" name="add_notification" value="1">
            <div class="col-md-4">
                <input type="text" name="title" class="form-control" placeholder="عنوان المهمة" required>
            </div>
            <div class="col-md-3">
                <input type="date" name="notify_date" class="form-control" required>
            </div>
            <div class="col-md-3">
                <input type="date" name="end_date" class="form-control" required>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn-add w-100">إضافة</button>
            </div>
            <div class="col-md-12">
                <textarea name="message" class="form-control" rows="3" placeholder="وصف المهمة" required></textarea>
            </div>
        </form>
    </div>
    <?php endif; ?>

    <!-- البحث والفلترة -->
    <div class="search-section">
        <h5><i class="fas fa-search"></i> البحث والفلترة</h5>
        <form method="GET" class="row g-3">
            <input type="hidden" name="month" value="<?= $currentMonth ?>">
            <input type="hidden" name="year" value="<?= $currentYear ?>">
            <div class="col-md-4">
                <input type="text" name="search" class="form-control" placeholder="ابحث في العنوان أو الوصف" value="<?= htmlspecialchars($searchQuery) ?>">
            </div>
            <div class="col-md-2">
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="تم" <?= $statusFilter == 'تم' ? 'selected' : '' ?>>مكتملة</option>
                    <option value="تحت التنفيذ" <?= $statusFilter == 'تحت التنفيذ' ? 'selected' : '' ?>>قيد التنفيذ</option>
                    <option value="لم تنفذ" <?= $statusFilter == 'لم تنفذ' ? 'selected' : '' ?>>في الانتظار</option>
                </select>
            </div>
            <div class="col-md-2">
                <input type="date" name="date_from" class="form-control" value="<?= htmlspecialchars($dateFromFilter) ?>">
            </div>
            <div class="col-md-2">
                <input type="date" name="date_to" class="form-control" value="<?= htmlspecialchars($dateToFilter) ?>">
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">بحث</button>
            </div>
        </form>
    </div>

    <!-- جدول المهام -->
    <div class="table-section">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5><i class="fas fa-list"></i> قائمة المهام</h5>
            <div>
                <a href="?month=<?= $prevMonth ?>&year=<?= $prevYear ?>" class="btn btn-outline-primary btn-sm">الشهر السابق</a>
                <span class="mx-2"><?= date('F Y', strtotime("$currentYear-$currentMonth-01")) ?></span>
                <a href="?month=<?= $nextMonth ?>&year=<?= $nextYear ?>" class="btn btn-outline-primary btn-sm">الشهر التالي</a>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>العنوان</th>
                        <th>الوصف</th>
                        <th>من تاريخ</th>
                        <th>إلى تاريخ</th>
                        <th>الحالة</th>
                        <?php if ($isAdmin): ?><th>الإجراءات</th><?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php if (count($notifications)): ?>
                        <?php foreach ($notifications as $index => $note): ?>
                            <?php
                            $statusClass = '';
                            switch($note['Status']) {
                                case 'تم': $statusClass = 'status-completed'; break;
                                case 'تحت التنفيذ': $statusClass = 'status-in-progress'; break;
                                case 'لم تنفذ': $statusClass = 'status-pending'; break;
                            }
                            ?>
                            <tr>
                                <td><?= $index + 1 ?></td>
                                <td><strong><?= htmlspecialchars($note['Title']) ?></strong></td>
                                <td><?= nl2br(htmlspecialchars($note['Message'])) ?></td>
                                <td><?= date('d/m/Y', strtotime($note['NotifyDate'])) ?></td>
                                <td><?= date('d/m/Y', strtotime($note['ToDate'])) ?></td>
                                <td><span class="status-badge <?= $statusClass ?>"><?= $note['Status'] ?></span></td>
                                <?php if ($isAdmin): ?>
                                <td>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="notification_id" value="<?= $note['ID'] ?>">
                                        <select name="status" class="form-select form-select-sm d-inline w-auto" onchange="this.form.submit()">
                                            <option value="لم تنفذ" <?= $note['Status'] == 'لم تنفذ' ? 'selected' : '' ?>>في الانتظار</option>
                                            <option value="تحت التنفيذ" <?= $note['Status'] == 'تحت التنفيذ' ? 'selected' : '' ?>>قيد التنفيذ</option>
                                            <option value="تم" <?= $note['Status'] == 'تم' ? 'selected' : '' ?>>مكتملة</option>
                                        </select>
                                        <input type="hidden" name="update_status" value="1">
                                    </form>
                                    <form method="POST" class="d-inline ms-2" onsubmit="return confirm('هل تريد حذف هذه المهمة؟')">
                                        <input type="hidden" name="notification_id" value="<?= $note['ID'] ?>">
                                        <input type="hidden" name="delete_notification" value="1">
                                        <button type="submit" class="btn btn-danger btn-sm">حذف</button>
                                    </form>
                                </td>
                                <?php endif; ?>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="<?= $isAdmin ? 7 : 6 ?>" class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5>لا توجد مهام</h5>
                                <p class="text-muted">لا توجد مهام مطابقة للفترة المحددة</p>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
