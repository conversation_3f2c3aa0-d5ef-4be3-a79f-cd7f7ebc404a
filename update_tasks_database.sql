-- تحديث جدول المهام لإضافة ميزات جديدة
-- يجب تشغيل هذا الملف في قاعدة البيانات لإضافة الحقول الجديدة

-- إ<PERSON><PERSON><PERSON><PERSON> حقل الأولوية
ALTER TABLE `notifications` 
ADD COLUMN `Priority` ENUM('منخفضة', 'متوسطة', 'عالية', 'عاجلة') DEFAULT 'متوسطة' AFTER `Status`;

-- إضا<PERSON>ة حقل التصنيف
ALTER TABLE `notifications` 
ADD COLUMN `Category` VARCHAR(100) DEFAULT NULL AFTER `Priority`;

-- إضا<PERSON>ة حقل المرفقات
ALTER TABLE `notifications` 
ADD COLUMN `Attachments` TEXT DEFAULT NULL AFTER `Category`;

-- إ<PERSON><PERSON><PERSON><PERSON> حقل المسؤول عن المهمة
ALTER TABLE `notifications` 
ADD COLUMN `AssignedTo` INT(11) DEFAULT NULL AFTER `Attachments`;

-- إ<PERSON><PERSON><PERSON><PERSON> حقل التقدم (نسبة مئوية)
ALTER TABLE `notifications` 
ADD COLUMN `Progress` INT(3) DEFAULT 0 AFTER `AssignedTo`;

-- إضافة حقل الملاحظات الإضافية
ALTER TABLE `notifications` 
ADD COLUMN `Notes` TEXT DEFAULT NULL AFTER `Progress`;

-- إضافة حقل آخر تحديث
ALTER TABLE `notifications` 
ADD COLUMN `LastUpdated` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `Notes`;

-- إضافة حقل المستخدم الذي أنشأ المهمة
ALTER TABLE `notifications` 
ADD COLUMN `CreatedBy` INT(11) DEFAULT NULL AFTER `LastUpdated`;

-- إضافة فهارس للأداء
ALTER TABLE `notifications` 
ADD INDEX `idx_priority` (`Priority`),
ADD INDEX `idx_category` (`Category`),
ADD INDEX `idx_assigned_to` (`AssignedTo`),
ADD INDEX `idx_created_by` (`CreatedBy`),
ADD INDEX `idx_status_priority` (`Status`, `Priority`);

-- إنشاء جدول التعليقات على المهام
CREATE TABLE IF NOT EXISTS `task_comments` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `TaskID` int(11) NOT NULL,
  `UserID` int(11) NOT NULL,
  `Comment` text NOT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `idx_task_id` (`TaskID`),
  KEY `idx_user_id` (`UserID`),
  KEY `idx_created_at` (`CreatedAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- إنشاء جدول تصنيفات المهام
CREATE TABLE IF NOT EXISTS `task_categories` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) NOT NULL,
  `Description` text DEFAULT NULL,
  `Color` varchar(7) DEFAULT '#007bff',
  `Icon` varchar(50) DEFAULT 'fas fa-folder',
  `CreatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `IsActive` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `unique_name` (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- إدراج تصنيفات افتراضية
INSERT INTO `task_categories` (`Name`, `Description`, `Color`, `Icon`) VALUES
('عام', 'مهام عامة', '#6c757d', 'fas fa-tasks'),
('مالية', 'مهام متعلقة بالشؤون المالية', '#28a745', 'fas fa-dollar-sign'),
('إدارية', 'مهام إدارية وتنظيمية', '#007bff', 'fas fa-briefcase'),
('تقنية', 'مهام تقنية وتطوير', '#17a2b8', 'fas fa-code'),
('عاجل', 'مهام عاجلة تحتاج متابعة فورية', '#dc3545', 'fas fa-exclamation-triangle'),
('اجتماعات', 'مهام متعلقة بالاجتماعات', '#ffc107', 'fas fa-users'),
('تقارير', 'إعداد وتجهيز التقارير', '#6f42c1', 'fas fa-chart-bar'),
('متابعة', 'مهام المتابعة والمراجعة', '#fd7e14', 'fas fa-eye');

-- إنشاء جدول سجل تغييرات المهام
CREATE TABLE IF NOT EXISTS `task_history` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `TaskID` int(11) NOT NULL,
  `UserID` int(11) NOT NULL,
  `Action` varchar(100) NOT NULL,
  `OldValue` text DEFAULT NULL,
  `NewValue` text DEFAULT NULL,
  `CreatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `idx_task_id` (`TaskID`),
  KEY `idx_user_id` (`UserID`),
  KEY `idx_action` (`Action`),
  KEY `idx_created_at` (`CreatedAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- إضافة قيود المفاتيح الخارجية
ALTER TABLE `notifications` 
ADD CONSTRAINT `fk_assigned_to` FOREIGN KEY (`AssignedTo`) REFERENCES `users` (`ID`) ON DELETE SET NULL,
ADD CONSTRAINT `fk_created_by` FOREIGN KEY (`CreatedBy`) REFERENCES `users` (`ID`) ON DELETE SET NULL;

ALTER TABLE `task_comments` 
ADD CONSTRAINT `fk_comment_task` FOREIGN KEY (`TaskID`) REFERENCES `notifications` (`ID`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_comment_user` FOREIGN KEY (`UserID`) REFERENCES `users` (`ID`) ON DELETE CASCADE;

ALTER TABLE `task_history` 
ADD CONSTRAINT `fk_history_task` FOREIGN KEY (`TaskID`) REFERENCES `notifications` (`ID`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_history_user` FOREIGN KEY (`UserID`) REFERENCES `users` (`ID`) ON DELETE CASCADE;

-- تحديث المهام الموجودة لتعيين المنشئ
UPDATE `notifications` SET `CreatedBy` = 1 WHERE `CreatedBy` IS NULL;

COMMIT;
