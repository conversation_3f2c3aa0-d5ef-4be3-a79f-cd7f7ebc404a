<?php

require 'vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;



require 'db.php';



if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {

    $file = $_FILES['file']['tmp_name'];



    try {

        // قراءة ملف Excel

        $spreadsheet = IOFactory::load($file);

        $sheet = $spreadsheet->getActiveSheet();

        $rows = $sheet->toArray();



        // استبعاد الصف الأول (رؤوس الأعمدة)

        array_shift($rows);



        foreach ($rows as $row) {

            // استخراج القيم

            $customerName = $row[1];

            $amount = $row[2];

            $paymentDate = $row[3];

            $authorizationNumber = $row[4];

            $accountNumber = $row[5];

            $transferNumber = $row[6];

            $transferType = $row[7];

            $paymentNumber = $row[8];

            $notes = $row[9];



            // إدخال البيانات في قاعدة البيانات

            $stmt = $conn->prepare("INSERT INTO Expenses (CustomerName, Amount, PaymentDate, AuthorizationNumber, AccountNumber, TransferNumber, TransferType, PaymentNumber, Notes) 

                                    VALUES (:customerName, :amount, :paymentDate, :authorizationNumber, :accountNumber, :transferNumber, :transferType, :paymentNumber, :notes)");

            $stmt->execute([

                'customerName' => $customerName,

                'amount' => $amount,

                'paymentDate' => $paymentDate,

                'authorizationNumber' => $authorizationNumber,

                'accountNumber' => $accountNumber,

                'transferNumber' => $transferNumber,

                'transferType' => $transferType,

                'paymentNumber' => $paymentNumber,

                'notes' => $notes

            ]);

        }



        echo "تم استيراد البيانات بنجاح!";

    } catch (Exception $e) {

        echo "حدث خطأ أثناء استيراد البيانات: " . $e->getMessage();

    }

}

?>

<!DOCTYPE html>

<html lang="ar" dir="rtl">

<head>

    <meta charset="UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>تحميل ورفع بيانات الصرفيات</title>

</head>

<body>

    <div class="container">

        <h1>إدارة بيانات الصرفيات</h1>



        <!-- نموذج تصدير البيانات -->

        <form method="GET" action="export_expenses.php">

            <button type="submit" class="btn btn-success">تحميل البيانات (Excel)</button>

        </form>



        <!-- نموذج رفع البيانات -->

        <form method="POST" action="import_expenses.php" enctype="multipart/form-data">

            <label for="file">اختر ملف Excel:</label>

            <input type="file" name="file" id="file" accept=".xlsx" required>

            <button type="submit" class="btn btn-primary">رفع الملف</button>

        </form>

    </div>

</body>

</html>

