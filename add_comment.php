<?php
require_once 'db.php';
require_once 'functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة غير مسموحة']);
    exit;
}

$taskId = $_POST['task_id'] ?? 0;
$comment = trim($_POST['comment'] ?? '');
$userId = $_SESSION['user_id'];

if (!$taskId || !$comment) {
    echo json_encode(['success' => false, 'message' => 'البيانات مطلوبة']);
    exit;
}

try {
    // التحقق من وجود المهمة
    $taskStmt = $conn->prepare("SELECT ID, Title FROM Notifications WHERE ID = ?");
    $taskStmt->execute([$taskId]);
    $task = $taskStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$task) {
        echo json_encode(['success' => false, 'message' => 'المهمة غير موجودة']);
        exit;
    }
    
    // إضافة التعليق
    $stmt = $conn->prepare("INSERT INTO task_comments (TaskID, UserID, Comment) VALUES (?, ?, ?)");
    $stmt->execute([$taskId, $userId, $comment]);
    
    // تسجيل في سجل التغييرات
    $historyStmt = $conn->prepare("INSERT INTO task_history (TaskID, UserID, Action, NewValue) VALUES (?, ?, 'إضافة تعليق', ?)");
    $historyStmt->execute([$taskId, $userId, "تم إضافة تعليق: " . mb_substr($comment, 0, 50) . "..."]);
    
    echo json_encode(['success' => true, 'message' => 'تم إضافة التعليق بنجاح']);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
}
?>
