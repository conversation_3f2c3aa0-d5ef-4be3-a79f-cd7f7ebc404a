<?php
session_start();
require 'db.php'; // الاتصال بقاعدة البيانات


if (isset($_GET['message']) && $_GET['message'] === 'session_expired') {
    echo '<div class="alert alert-warning">انتهت الجلسة بسبب عدم النشاط. يرجى تسجيل الدخول مجددًا.</div>';
}
// التحقق من تسجيل الدخول
if (isset($_SESSION['user_id'])) {
    header("Location: /e-finance/index.php"); // إذا كان المستخدم مسجلاً دخوله، يتم توجيهه إلى الصفحة الرئيسية
    exit;
}

$error = ''; // تعريف متغير الخطأ

// التحقق من إرسال البيانات عبر POST
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];

    // التحقق من صحة المدخلات
    if (!empty($username) && !empty($password)) {
        // استعلام للتحقق من اسم المستخدم وكلمة المرور
        $stmt = $conn->prepare("SELECT * FROM Users WHERE Username = :username");
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        // التحقق من وجود المستخدم وكلمة المرور
        if ($user && password_verify($password, $user['Password'])) { // ✅ التحقق الصحيح
            $_SESSION['user_id'] = $user['ID'];
            $_SESSION['role'] = $user['Role'];
            $_SESSION['username'] = $user['Username'];
            header("Location: /e-finance/index.php"); // التوجيه إلى الصفحة الرئيسية بعد تسجيل الدخول
            exit;
        } else {
            $error = "اسم المستخدم أو كلمة المرور غير صحيحة.";
        }
    } else {
        $error = "يرجى ملء جميع الحقول.";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  </head>
<body>
    <div class="container mt-5">
        <h2 class="text-center">تسجيل الدخول</h2>

        <!-- رسالة الخطأ إذا كانت موجودة -->
        <?php if ($error): ?>
            <div class="alert alert-danger" role="alert">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <div class="mb-3">
                <label for="username" class="form-label">اسم المستخدم</label>
                <input type="text" class="form-control" id="username" name="username" required>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">تسجيل الدخول</button>
        </form>
    </div>
</body>
</html>
