<?php
require_once 'db.php';
require_once 'functions.php';
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (isset($_SESSION['user_id'])) {
    $_SESSION['page_start_time'] = time();

    $userId = $_SESSION['user_id'];
    $page = basename($_SERVER['PHP_SELF']);
    $action = 'زيارة الصفحة';
    $url = $_SERVER['REQUEST_URI'];
    $method = $_SERVER['REQUEST_METHOD'];
    $data = ($method === 'POST') ? json_encode($_POST) : json_encode($_GET);

    // سجل الزيارة بدون مدة الآن
    logActivity($conn, $userId, $page, $action, $url, $method, $data, null);
}

if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';

$currentMonth = isset($_GET['month']) ? (int)$_GET['month'] : (int)date('m');
$currentYear = isset($_GET['year']) ? (int)$_GET['year'] : (int)date('Y');
$statusFilter = $_GET['status'] ?? '';

$startDate = "$currentYear-" . str_pad($currentMonth, 2, '0', STR_PAD_LEFT) . "-01";
$endDate = date("Y-m-t", strtotime($startDate));

// إضافة تنبيه
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_notification']) && $isAdmin) {
    $title = $_POST['title'];
    $message = $_POST['message'];
    $notifyDate = $_POST['notify_date'];
    $toDate = $_POST['end_date'] ?? $_POST['notify_date'];
    $stmt = $conn->prepare("INSERT INTO Notifications (Title, Message, NotifyDate, ToDate, Status) VALUES (?, ?, ?, ?, 'لم تنفذ')");
    $stmt->execute([$title, $message, $notifyDate, $toDate]);
    $_SESSION['success'] = "تمت إضافة المهمة بنجاح.";
    header("Location: Add_Notification2.php");
    exit;
}

// حذف تنبيه
if (isset($_GET['delete']) && $isAdmin) {
    $stmt = $conn->prepare("DELETE FROM Notifications WHERE ID = ?");
    $stmt->execute([$_GET['delete']]);
    $_SESSION['success'] = "تم حذف المهمة.";
    header("Location: Add_Notification2.php");
    exit;
}

// تحديث الحالة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status']) && $isAdmin) {
    $stmt = $conn->prepare("UPDATE Notifications SET Status = ? WHERE ID = ?");
    $stmt->execute([$_POST['status'], $_POST['id']]);
    $_SESSION['success'] = "تم تحديث الحالة.";
    header("Location: Add_Notification2.php?month=$currentMonth&year=$currentYear");
    exit;
}

// ترحيل المهمة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reschedule']) && $isAdmin) {
    $stmt = $conn->prepare("UPDATE Notifications SET NotifyDate = DATE_ADD(NotifyDate, INTERVAL 1 DAY), ToDate = DATE_ADD(ToDate, INTERVAL 1 DAY) WHERE ID = ?");
    $stmt->execute([$_POST['reschedule']]);
    $_SESSION['success'] = "تم ترحيل المهمة ليوم الغد.";
    header("Location: Add_Notification2.php?month=$currentMonth&year=$currentYear");
    exit;
}

// جلب التنبيهات
$query = "SELECT * FROM Notifications WHERE (NotifyDate BETWEEN ? AND ? OR ToDate BETWEEN ? AND ?)";
$params = [$startDate, $endDate, $startDate, $endDate];
if ($statusFilter !== '') {
    $query .= " AND Status = ?";
    $params[] = $statusFilter;
}
$query .= " ORDER BY NotifyDate ASC";
$stmt = $conn->prepare($query);
$stmt->execute($params);
$notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

$prevMonth = $currentMonth - 1; $prevYear = $currentYear;
$nextMonth = $currentMonth + 1; $nextYear = $currentYear;
if ($prevMonth < 1) { $prevMonth = 12; $prevYear--; }
if ($nextMonth > 12) { $nextMonth = 1; $nextYear++; }
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة المهام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="notifications-style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* تحسينات خاصة لصفحة إدارة التنبيهات */
        body {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e3f2fd 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .notifications-container {
            max-width: 1400px;
            margin: auto;
            margin-top: 30px;
            padding: 0 15px;
        }

        .page-header {
            background: linear-gradient(135deg, #000000, #333333);
            color: white;
            padding: 25px;
            border-radius: 15px 15px 0 0;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .page-header h4 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .page-header .subtitle {
            margin-top: 8px;
            font-size: 0.95rem;
            opacity: 0.9;
        }

        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: none;
        }

        .card-body-enhanced {
            padding: 30px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        /* تحسين نموذج إضافة التنبيه */
        .add-notification-form {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid #007bff;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
        }

        .add-notification-form .form-label {
            color: #000000;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .add-notification-form .form-control,
        .add-notification-form .form-select {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 12px 15px;
            transition: all 0.3s ease;
            background: white;
        }

        .add-notification-form .form-control:focus,
        .add-notification-form .form-select:focus {
            border-color: #000000;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            transform: translateY(-2px);
        }

        /* تحسين أزرار الإضافة */
        .btn-add-notification {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-weight: 600;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }

        .btn-add-notification:hover {
            background: linear-gradient(135deg, #0056b3, #007bff);
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        /* تحسين التنقل بين الشهور */
        .month-navigation {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #007bff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .month-navigation h5 {
            margin: 0;
            color: #000000;
            font-weight: 600;
            font-size: 1.3rem;
        }

        .nav-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .nav-btn:hover {
            background: linear-gradient(135deg, #0056b3, #007bff);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            color: white;
        }

        /* تحسين فلتر الحالة */
        .status-filter {
            background: white;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .status-filter .form-select {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 10px 15px;
            font-weight: 500;
            background: white;
        }

        /* تحسين الجدول */
        .notifications-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 2px solid #e9ecef;
        }

        .notifications-table .table {
            margin: 0;
            width: 100%;
        }

        .notifications-table .table th {
            background: linear-gradient(135deg, #000000, #333333);
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 18px 15px;
            border: none;
            font-size: 0.95rem;
        }

        .notifications-table .table td {
            padding: 15px;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
            font-size: 0.95rem;
        }

        .notifications-table .table tbody tr {
            transition: all 0.3s ease;
        }

        .notifications-table .table tbody tr:hover {
            background-color: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        /* تحسين تمييز الصفوف */
        .highlight-today {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
            font-weight: bold;
            border-left: 4px solid #ffc107;
        }

        .highlight-end-today {
            background: linear-gradient(135deg, #d1e7dd, #a8e6cf) !important;
            border-left: 4px solid #28a745;
        }

        /* تحسين أزرار الإجراءات */
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        .action-buttons .form-select {
            border: 2px solid #007bff;
            border-radius: 6px;
            padding: 8px 12px;
            font-size: 0.85rem;
            min-width: 150px;
        }

        .action-buttons .btn {
            padding: 8px 15px;
            border-radius: 6px;
            font-size: 0.85rem;
            font-weight: 500;
            min-width: 150px;
            transition: all 0.3s ease;
        }

        .btn-update-status {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
        }

        .btn-update-status:hover {
            background: linear-gradient(135deg, #0056b3, #007bff);
            transform: translateY(-2px);
        }

        .btn-reschedule {
            background: linear-gradient(135deg, #6c757d, #5a6268);
            color: white;
            border: none;
        }

        .btn-reschedule:hover {
            background: linear-gradient(135deg, #5a6268, #6c757d);
            transform: translateY(-2px);
        }

        .btn-edit {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
        }

        .btn-edit:hover {
            background: linear-gradient(135deg, #0056b3, #007bff);
            transform: translateY(-2px);
        }

        .btn-delete {
            background: linear-gradient(135deg, #000000, #333333);
            color: white;
            border: none;
        }

        .btn-delete:hover {
            background: linear-gradient(135deg, #333333, #000000);
            transform: translateY(-2px);
        }

        /* تحسين رسائل التنبيه */
        .alert-success {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 2px solid #007bff;
            border-radius: 10px;
            color: #0d47a1;
            padding: 15px 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0, 123, 255, 0.1);
        }

        /* تحسين الاستجابة للأجهزة المحمولة */
        @media (max-width: 768px) {
            .notifications-container {
                padding: 0 10px;
                margin-top: 20px;
            }

            .add-notification-form {
                padding: 20px;
            }

            .month-navigation {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .action-buttons {
                gap: 5px;
            }

            .action-buttons .btn,
            .action-buttons .form-select {
                min-width: 120px;
                font-size: 0.8rem;
                padding: 6px 10px;
            }

            .notifications-table .table th,
            .notifications-table .table td {
                padding: 10px 8px;
                font-size: 0.85rem;
            }
        }

        /* تحسين حالات المهام */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-align: center;
            display: inline-block;
        }

        .status-completed {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-in-progress {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: #0d47a1;
            border: 1px solid #bbdefb;
        }

        .status-not-started {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #495057;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>

<?php include 'header.php'; ?>

<div class="notifications-container">
    <div class="main-card">
        <div class="page-header">
            <h4><i class="fas fa-tasks"></i> إدارة المهام والتنبيهات</h4>
            <div class="subtitle">نظام إدارة وتتبع المهام اليومية</div>
        </div>
        <div class="card-body-enhanced">

            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success text-center"><?= $_SESSION['success']; unset($_SESSION['success']); ?></div>
            <?php endif; ?>

            <?php if ($isAdmin): ?>
            <div class="add-notification-form">
                <h5 class="text-center mb-4" style="color: #000000; font-weight: 600;">
                    <i class="fas fa-plus-circle"></i> إضافة مهمة جديدة
                </h5>
                <form method="POST" class="row g-3">
                    <input type="hidden" name="add_notification" value="1">
                    <div class="col-md-6">
                        <label class="form-label"><i class="fas fa-heading"></i> عنوان المهمة</label>
                        <input type="text" name="title" class="form-control" placeholder="أدخل عنوان المهمة..." required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label"><i class="fas fa-calendar-alt"></i> من تاريخ</label>
                        <input type="date" name="notify_date" class="form-control" required>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label"><i class="fas fa-calendar-check"></i> إلى تاريخ</label>
                        <input type="date" name="end_date" class="form-control" required>
                    </div>
                    <div class="col-md-12">
                        <label class="form-label"><i class="fas fa-align-left"></i> وصف المهمة</label>
                        <textarea name="message" class="form-control" rows="4" placeholder="أدخل تفاصيل المهمة..." required></textarea>
                    </div>
                    <div class="col-md-12 text-center">
                        <button type="submit" class="btn-add-notification">
                            <i class="fas fa-plus"></i> إضافة مهمة جديدة
                        </button>
                    </div>
                </form>
            </div>
            <?php endif; ?>

            <!-- تنقل بين الشهور -->
            <div class="month-navigation">
                <a href="?month=<?= $prevMonth ?>&year=<?= $prevYear ?>" class="nav-btn" title="الشهر السابق">
                    <i class="fas fa-chevron-right"></i> الشهر السابق
                </a>
                <h5><i class="fas fa-calendar"></i> مهام شهر <?= date("F Y", strtotime($startDate)) ?></h5>
                <a href="?month=<?= $nextMonth ?>&year=<?= $nextYear ?>" class="nav-btn" title="الشهر التالي">
                    الشهر التالي <i class="fas fa-chevron-left"></i>
                </a>
            </div>

            <!-- فلتر حسب الحالة -->
            <div class="status-filter">
                <form method="GET" class="d-flex align-items-center gap-3">
                    <input type="hidden" name="month" value="<?= $currentMonth ?>">
                    <input type="hidden" name="year" value="<?= $currentYear ?>">
                    <label class="form-label mb-0" style="color: #000000; font-weight: 600;">
                        <i class="fas fa-filter"></i> فلترة حسب الحالة:
                    </label>
                    <select name="status" class="form-select w-auto" onchange="this.form.submit()">
                        <option value="">🔍 جميع الحالات</option>
                        <option value="تم" <?= $statusFilter == 'تم' ? 'selected' : '' ?>>✅ تم إنجازها</option>
                        <option value="تحت التنفيذ" <?= $statusFilter == 'تحت التنفيذ' ? 'selected' : '' ?>>🔄 تحت التنفيذ</option>
                        <option value="لم تنفذ" <?= $statusFilter == 'لم تنفذ' ? 'selected' : '' ?>>❌ لم تنفذ بعد</option>
                    </select>
                </form>
            </div>

            <!-- جدول التنبيهات -->
            <div class="notifications-table">
                <div class="table-responsive">
                    <table class="table text-center align-middle">
                        <thead>
                            <tr>
                                <th><i class="fas fa-heading"></i> العنوان</th>
                                <th><i class="fas fa-align-left"></i> الوصف</th>
                                <th><i class="fas fa-calendar-alt"></i> من تاريخ</th>
                                <th><i class="fas fa-calendar-check"></i> إلى تاريخ</th>
                                <th><i class="fas fa-tasks"></i> الحالة</th>
                                <?php if ($isAdmin): ?><th><i class="fas fa-cogs"></i> الإجراءات</th><?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (count($notifications)): ?>
                                <?php foreach ($notifications as $note): ?>
                                    <?php
                                    $today = date('Y-m-d');
                                    $isToday = $note['NotifyDate'] === $today;
                                    $isEndToday = $note['ToDate'] === $today;
                                    $rowClass = $isToday ? 'highlight-today' : ($isEndToday ? 'highlight-end-today' : '');

                                    // تحديد لون الحالة
                                    $statusClass = '';
                                    $statusIcon = '';
                                    switch($note['Status']) {
                                        case 'تم':
                                            $statusClass = 'status-completed';
                                            $statusIcon = 'fas fa-check-circle';
                                            break;
                                        case 'تحت التنفيذ':
                                            $statusClass = 'status-in-progress';
                                            $statusIcon = 'fas fa-clock';
                                            break;
                                        case 'لم تنفذ':
                                        default:
                                            $statusClass = 'status-not-started';
                                            $statusIcon = 'fas fa-exclamation-circle';
                                            break;
                                    }
                                    ?>
                                    <tr class="<?= $rowClass ?>">
                                        <td><strong><?= htmlspecialchars($note['Title']) ?></strong></td>
                                        <td style="text-align: right; max-width: 300px;">
                                            <?= nl2br(htmlspecialchars($note['Message'])) ?>
                                        </td>
                                        <td>
                                            <i class="fas fa-calendar-alt" style="color: #007bff;"></i>
                                            <?= date('d/m/Y', strtotime($note['NotifyDate'])) ?>
                                        </td>
                                        <td>
                                            <i class="fas fa-calendar-check" style="color: #007bff;"></i>
                                            <?= date('d/m/Y', strtotime($note['ToDate'])) ?>
                                        </td>
                                        <td>
                                            <span class="status-badge <?= $statusClass ?>">
                                                <i class="<?= $statusIcon ?>"></i>
                                                <?= $note['Status'] ?? 'غير محددة' ?>
                                            </span>
                                        </td>
                                        <?php if ($isAdmin): ?>
                                        <td>
                                            <div class="action-buttons">
                                                <form method="POST" style="width: 100%;">
                                                    <input type="hidden" name="id" value="<?= $note['ID'] ?>">
                                                    <select name="status" class="form-select" required>
                                                        <option value="تم" <?= $note['Status'] === 'تم' ? 'selected' : '' ?>>
                                                            ✅ تم إنجازها
                                                        </option>
                                                        <option value="تحت التنفيذ" <?= $note['Status'] === 'تحت التنفيذ' ? 'selected' : '' ?>>
                                                            🔄 تحت التنفيذ
                                                        </option>
                                                        <option value="لم تنفذ" <?= $note['Status'] === 'لم تنفذ' ? 'selected' : '' ?>>
                                                            ❌ لم تنفذ بعد
                                                        </option>
                                                    </select>
                                                    <button type="submit" name="update_status" class="btn btn-update-status">
                                                        <i class="fas fa-save"></i> تحديث الحالة
                                                    </button>
                                                    <?php if ($note['Status'] === 'لم تنفذ'): ?>
                                                        <button name="reschedule" value="<?= $note['ID'] ?>" class="btn btn-reschedule">
                                                            <i class="fas fa-calendar-plus"></i> ترحيل للغد
                                                        </button>
                                                    <?php endif; ?>
                                                    <a href="edit_notification.php?id=<?= $note['ID'] ?>" class="btn btn-edit">
                                                        <i class="fas fa-edit"></i> تعديل
                                                    </a>
                                                    <a href="?delete=<?= $note['ID'] ?>" class="btn btn-delete"
                                                       onclick="return confirm('هل أنت متأكد من حذف هذه المهمة؟');">
                                                        <i class="fas fa-trash"></i> حذف
                                                    </a>
                                                </form>
                                            </div>
                                        </td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="<?= $isAdmin ? 6 : 5 ?>" style="padding: 40px; color: #6c757d;">
                                        <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 15px; display: block;"></i>
                                        <h5>لا توجد مهام مطابقة للفترة المحددة</h5>
                                        <p class="mb-0">جرب تغيير الشهر أو إزالة فلتر الحالة</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- زر العودة لأعلى -->
<button onclick="scrollToTop()" id="scrollBtn" title="العودة لأعلى">
    <i class="fas fa-arrow-up"></i>
</button>

<script>
// إظهار زر العودة لأعلى عند التمرير
window.onscroll = function() {
    let btn = document.getElementById("scrollBtn");
    if (document.body.scrollTop > 200 || document.documentElement.scrollTop > 200) {
        btn.style.display = "block";
    } else {
        btn.style.display = "none";
    }
};

// العودة لأعلى الصفحة
function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

// تأكيد الحذف مع تحسين الرسالة
document.querySelectorAll('.btn-delete').forEach(function(btn) {
    btn.addEventListener('click', function(e) {
        if (!confirm('⚠️ تحذير!\n\nهل أنت متأكد من حذف هذه المهمة؟\nلا يمكن التراجع عن هذا الإجراء.')) {
            e.preventDefault();
        }
    });
});

// تحسين تجربة المستخدم عند تحديث الحالة
document.querySelectorAll('.btn-update-status').forEach(function(btn) {
    btn.addEventListener('click', function() {
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
        this.disabled = true;
    });
});
</script>

<script src="notifications-script.js"></script>

<?php include 'footer.php'; ?>
</body>
</html>
