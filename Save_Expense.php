<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header("Location: /e-finance/login");
    exit;
}

// التحقق من الطلب
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $customerID = $_POST['customer_id'];
        $budgetID = $_POST['budget_id'];
        $amount = floatval($_POST['amount']);
        $description = $_POST['description'];
        $date = $_POST['date'];

        // التحقق من وجود البيانات المطلوبة
        if (!$customerID || !$budgetID || !$amount || !$description || !$date) {
            throw new Exception("الرجاء ملء جميع الحقول المطلوبة.");
        }

        // جلب الموازنة المحددة
        $stmt = $conn->prepare("SELECT TotalAmount, SpentAmount FROM Budgets WHERE ID = :budget_id");
        $stmt->bindParam(':budget_id', $budgetID);
        $stmt->execute();
        $budget = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$budget) {
            throw new Exception("لم يتم العثور على الموازنة المحددة.");
        }

        $available = $budget['TotalAmount'] - $budget['SpentAmount'];

        // التحقق من توفر المبلغ
        if ($amount > $available) {
            throw new Exception("لا يمكن صرف مبلغ أكبر من الموازنة المتبقية. المتاح: " . number_format($available, 2));
        }

        // حفظ الصرفية
        $stmt = $conn->prepare("INSERT INTO Expenses (CustomerID, BudgetID, Amount, Description, Date, CreatedAt)
                                VALUES (:customer_id, :budget_id, :amount, :description, :date, NOW())");
        $stmt->execute([
            ':customer_id' => $customerID,
            ':budget_id' => $budgetID,
            ':amount' => $amount,
            ':description' => $description,
            ':date' => $date
        ]);

        // تحديث المبلغ المصروف في الموازنة
        $stmt = $conn->prepare("UPDATE Budgets SET SpentAmount = SpentAmount + :amount WHERE ID = :budget_id");
        $stmt->execute([
            ':amount' => $amount,
            ':budget_id' => $budgetID
        ]);

        echo "<script>alert('تمت إضافة الصرفية بنجاح.'); window.location.href='Expense_Management.php';</script>";
    } catch (Exception $e) {
        echo "<div style='color: red;'>خطأ: " . $e->getMessage() . "</div>";
    }
}
?>
