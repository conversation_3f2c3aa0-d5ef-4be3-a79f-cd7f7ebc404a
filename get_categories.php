<?php
session_start();
require 'db.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح'], JSON_UNESCAPED_UNICODE);
    exit;
}

// التحقق من وجود معرف الموازنة
if (!isset($_GET['budget_id']) || empty($_GET['budget_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'معرف الموازنة مطلوب'], JSON_UNESCAPED_UNICODE);
    exit;
}

$budgetId = intval($_GET['budget_id']);

try {
    // جلب البنود المرتبطة بالموازنة مع معلومات الرصيد
    $stmt = $conn->prepare("
        SELECT
            ID,
            Name,
            Amount,
            SpentAmount,
            (Amount - SpentAmount) AS RemainingAmount
        FROM Categories
        WHERE BudgetID = ?
        ORDER BY Name ASC
    ");

    $stmt->execute([$budgetId]);
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // تنسيق البيانات لإرسالها
    $formattedCategories = [];
    foreach ($categories as $category) {
        $remaining = $category['Amount'] > 0 ? $category['RemainingAmount'] : null;
        $formattedCategories[] = [
            'ID' => $category['ID'],
            'Name' => $category['Name'],
            'Amount' => $category['Amount'],
            'SpentAmount' => $category['SpentAmount'],
            'RemainingAmount' => $remaining,
            'DisplayName' => $category['Name'] .
                ($category['Amount'] > 0 ?
                    ' (متبقي: ' . number_format($remaining, 0) . ' جنيه)' :
                    ' (غير محدد المبلغ)'
                )
        ];
    }

    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($formattedCategories, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'حدث خطأ في الخادم: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
